{"__meta": {"id": "X2910cf1dedf09287e3e619ac7103af8a", "datetime": "2025-06-08 00:30:09", "utime": **********.071382, "method": "GET", "uri": "/customer/check/warehouse?customer_id=7&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749342608.195645, "end": **********.071405, "duration": 0.8757598400115967, "duration_str": "876ms", "measures": [{"label": "Booting", "start": 1749342608.195645, "relative_start": 0, "end": 1749342608.9497, "relative_end": 1749342608.9497, "duration": 0.7540550231933594, "duration_str": "754ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749342608.949725, "relative_start": 0.7540798187255859, "end": **********.071408, "relative_end": 3.0994415283203125e-06, "duration": 0.12168312072753906, "duration_str": "122ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45176624, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.020040000000000002, "accumulated_duration_str": "20.04ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0195172, "duration": 0.01835, "duration_str": "18.35ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.567}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.05377, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.567, "width_percent": 4.142}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0597541, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 95.709, "width_percent": 4.291}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1778841497 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1778841497\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1145332177 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1145332177\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1094213846 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1094213846\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-386874860 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1834 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749342593005%7C4%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ill0R2VVUEEvaVdBOXhQUEhxZGZ0bUE9PSIsInZhbHVlIjoidE1UMzdEWnpVZGRLZW1yNE9HeWErd0theXk1NGVWazVKRGpRbHV4eE81QW5HT29iYmJ2djRPdmVXbFNXRTduMzVVNEVkdU92eXp5VXcvbE5KUXppOW5pVTgyeVVVRzRSbi9qaC9HUEZDcnB5eWxNSW5SV3I0ZkhBOXZWVXdzQ0xuejE3TEdxRkpOdlN3MGMrRjJudDZMdEJOSFo2OFA3ekU4aGxKUUNpV0dtbHh5Y1RYdUlHZ2JZNnJZa0diRUJoOWVaRXl2ZjdRditka1hIOFN2dlp1S3BXaXRQanl4NUxoOHM3ZldwNm9aVXpWVEZKOVN5ZUJqV1djVTFkZzhud0hJbmhQaC9oa3NMWHBjRmQzelBwdDhmTEJPUlJBTXZpMGpFNFlmdEg5L0Q5MEFqUmllVkxoQ1cxNmttYWtUbVBEWW55eHJSeVpWQjkwQlNzQXVXeE1QamxYeVpJTUN3WE01bGw2ZjZjQlhRVjYyYW1NYzExYS9lWWFZamlackp0dEkwOG5VUTFqVXpOSEpuNVhPc2FvajZ1bFg1YjVVTG5meVJuTlZ2TE5VcWNGcjZGdmFyNU40c3QrRXUxK0NaZkNRUnBVNzBlU0dWam0vQS91VGlDZ1FTekg2YUU1N0hScm5SK29JbTg1c2MyRkIzZ2p4ak90SEpoVFhLNWFwS0siLCJtYWMiOiI1MWU4ZWRjNGY0ZTE3Yjg0ZmZkZDg1OGU1MmI4ZDVjNDk0ZjM2M2FkNDJjYmYzMWI4OGY4OTIyOWY4NmJiNzBlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkFOcTc1WWkrN2VSNVFBTGlIcnF4SkE9PSIsInZhbHVlIjoiVkdaL2M0b1QvejR3ZWVjeElKdzlFMHI5YWxqNTJIWk5WandYT3FZRlc4SVpIL3A1cjN3YnU4QjI4WC9jSURNQSsvK1VCaHJvNVZCSG1mWHphc3l1dnpSS2N5OVNiS2lHUGo4ZHVtTnZRb1dNa0dDL1JySlNEbTl5d3kweVh2QTZYSk0vc1c4ZS9FQmZYVFpTTjIzYUMweGNpUStTUW1tWlNGTGxyMG5IOUtoK082U1VmSUxTeG91djhqWTF2aTcrTmJlTVZITndHNlIwTVZmZUs5aVlNT0laN2pPSHN2TlBFNFNHaGErOVlKeXdkRlpWTDhFWDk4eDc0ZWdRRnVRaFhmdnE0dUt4STJBS3FnamJpS0I3MmxYTVhQSzVGQkxzTzNGaGtxam8xanZ3QVRiK3lQa2lMQVFON0lCY3VsODRxcU1HWHEveFludHdWUWV1amVNVFpsZzlZRFJwZUNncmF3TkNVNjZFcEx6eXFiNHlEcUJyTHE1aHI4b1R6a2xzMFE5MU5kNmJaZVRuMjI3eU1waTNVT1lWbzBKdmo3M01sZ3hpV1hhOUQvYWQzWUovL2hWUWJpVGF1bFdHNDhpbDlCS3VKNVZ4ZGlSUVBKSGtCS1FlT0JrekpLbmtOV0tNMXpyYUhwWC9nMVAxd3dFb2dibFNhWldMbzBDMUVmRlIiLCJtYWMiOiI1YmViNGMyNGJmNDk4YjU0N2QyYjI4OTY5MWRkM2Y4NzliODY5ZDAxODNiY2FjYjdmZmY4MTM3NzZiYTNlZjgwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-386874860\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-946294960 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-946294960\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2081239368 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:30:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImFZV1M4SS9nQUFOMmQwTnFiV0pJMFE9PSIsInZhbHVlIjoiNy9GSWVKKzZ5bmNYM2NENGpIaURMWFUwM1lnTU85dnlZVHZKSFRFVENjNkhJcDk5YzZzSXVaSW9pZVc4QW9LcEJscHMwamF5RmFudGNZMHlFdlN5M3kvQmtJaWZLWFFZcXl0T1p0b09kczYyRTVHb1lrTkN6cWE4dFdxUXNDWDUwNk91T1QzV0QxRzIvbjAvanRRNFpkdVRYVmJCeXhYbkJ0Ykp6UjZDRVgwM2RjZEZTRFBEUVNJSWxNNEVNOXJQa0tNVWY2bzY2NTdrTDRacUJhSERTby9LODg3aW4xWlE1dWZWUDlSWCtvd2wxWmsxTFMrOGdKMTBTMlg0dU44ZGNMeDVlUDgwdmZSRkRETS96V3V6ZldscVQxQnB0TU5rVXJZdG9NMXFTeWlYQkZ3Y1FyRzlrZ1pWNmo1NC92WWk2TGNJOFhiem5xMElTbWIvbWxiNzgzNjBMUXllbzlZdkxlcnEwQkNRT1JGdURNTVdPUHdYMTFUTU9WNjU4bFQ2V3JEekVMSVdmYVVhc0JBenBTQ3h4NkUvdlZHVTVQc3ZrVU14R2c4eTRDVDhxSmJ2L2ZIZjNjc29tSHdhNzhsKzQyR1hzWS9ta0dUU1JaMnRKT3NuVHZ0YkY4NEFmdEhNZ1NlNGdrcys3anAySG13L2xOVVh0RHAxZ2plNk9mc0ciLCJtYWMiOiIyN2Y1ZWQxNTA2MGVmYTAzNjJjN2M0ZGU4YTk0ZTk5NTQ4MzhmYjM1NzA0N2JmNzJiMDIzNDkxOTBhNmNjNDQyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:30:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImhVK3UxYkR6c0hkWHJzV29kcnhjenc9PSIsInZhbHVlIjoicGFxbGtzMnBBWVl3VkR4d2JpNitOalNLb3RTQ000MVJGVlZOTXNWUzJIWVVLUVdXWjk0ZytYZ0dNaS9oNXdRNlZQVEVjZHVzMGlLcUhva1lxNU5YamFZWWdqTksya05wNXRmR0JzY3d6MDhLWjQwUU5XNHZ1dTg3eFJuRjNKZk55M2FLTCt6dmtMYmk0OEZ6dkNaNytsRFRYalRZdWw3UnFBcHprN2ZIdDdKTk1nV0g4K3BFOXNSeUdYakFxTlQrUEdXRjV3N0p1VDU3Z0pmT0dpVG1JckJHUFJGT3ZCN0o3WlREZGxqRXd0T2Y1ajEvb1FsOU9jQytsb1RmZXJWb0hJN2JDMHM0Z2VTaW1pYzRzcUJUNG1xSDdmbGJQcWxLenBKRlY2Uitra0xYenBsT2p5WUNVNEdrWjVyb215UElMVEhVTytza1IzOUdBbUgvc3pWMjcvbDNEVUNubmdFVzFLR3lUWnBzTFRDQjZuaVQyV3NKZm1aNjVCK1JUWjVnOGsxUzZQUDRGNjZnNXBxZFlDTW9OSmlyMGFocnZIUXhucDlJN3MzbS9QaVBmZHBpVmRldHdLUnVHL3JiSXdMdllKU0xBRjRKV0FBQnFMRStrL1lsaG1MS2pjY09Kd1FoZ2lPRWJ4QkxxT0NRNitiQk1QZVJvV2hjaHo2R2VZeHIiLCJtYWMiOiIzNzViMWMyMGY0YzdlZDVmMTBjZmU4OTY5MzBiMDJmMTc4NjcxYzJmNjc0NmFiMTgxYjQzYTI2YWMyNmJhMjljIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:30:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImFZV1M4SS9nQUFOMmQwTnFiV0pJMFE9PSIsInZhbHVlIjoiNy9GSWVKKzZ5bmNYM2NENGpIaURMWFUwM1lnTU85dnlZVHZKSFRFVENjNkhJcDk5YzZzSXVaSW9pZVc4QW9LcEJscHMwamF5RmFudGNZMHlFdlN5M3kvQmtJaWZLWFFZcXl0T1p0b09kczYyRTVHb1lrTkN6cWE4dFdxUXNDWDUwNk91T1QzV0QxRzIvbjAvanRRNFpkdVRYVmJCeXhYbkJ0Ykp6UjZDRVgwM2RjZEZTRFBEUVNJSWxNNEVNOXJQa0tNVWY2bzY2NTdrTDRacUJhSERTby9LODg3aW4xWlE1dWZWUDlSWCtvd2wxWmsxTFMrOGdKMTBTMlg0dU44ZGNMeDVlUDgwdmZSRkRETS96V3V6ZldscVQxQnB0TU5rVXJZdG9NMXFTeWlYQkZ3Y1FyRzlrZ1pWNmo1NC92WWk2TGNJOFhiem5xMElTbWIvbWxiNzgzNjBMUXllbzlZdkxlcnEwQkNRT1JGdURNTVdPUHdYMTFUTU9WNjU4bFQ2V3JEekVMSVdmYVVhc0JBenBTQ3h4NkUvdlZHVTVQc3ZrVU14R2c4eTRDVDhxSmJ2L2ZIZjNjc29tSHdhNzhsKzQyR1hzWS9ta0dUU1JaMnRKT3NuVHZ0YkY4NEFmdEhNZ1NlNGdrcys3anAySG13L2xOVVh0RHAxZ2plNk9mc0ciLCJtYWMiOiIyN2Y1ZWQxNTA2MGVmYTAzNjJjN2M0ZGU4YTk0ZTk5NTQ4MzhmYjM1NzA0N2JmNzJiMDIzNDkxOTBhNmNjNDQyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:30:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImhVK3UxYkR6c0hkWHJzV29kcnhjenc9PSIsInZhbHVlIjoicGFxbGtzMnBBWVl3VkR4d2JpNitOalNLb3RTQ000MVJGVlZOTXNWUzJIWVVLUVdXWjk0ZytYZ0dNaS9oNXdRNlZQVEVjZHVzMGlLcUhva1lxNU5YamFZWWdqTksya05wNXRmR0JzY3d6MDhLWjQwUU5XNHZ1dTg3eFJuRjNKZk55M2FLTCt6dmtMYmk0OEZ6dkNaNytsRFRYalRZdWw3UnFBcHprN2ZIdDdKTk1nV0g4K3BFOXNSeUdYakFxTlQrUEdXRjV3N0p1VDU3Z0pmT0dpVG1JckJHUFJGT3ZCN0o3WlREZGxqRXd0T2Y1ajEvb1FsOU9jQytsb1RmZXJWb0hJN2JDMHM0Z2VTaW1pYzRzcUJUNG1xSDdmbGJQcWxLenBKRlY2Uitra0xYenBsT2p5WUNVNEdrWjVyb215UElMVEhVTytza1IzOUdBbUgvc3pWMjcvbDNEVUNubmdFVzFLR3lUWnBzTFRDQjZuaVQyV3NKZm1aNjVCK1JUWjVnOGsxUzZQUDRGNjZnNXBxZFlDTW9OSmlyMGFocnZIUXhucDlJN3MzbS9QaVBmZHBpVmRldHdLUnVHL3JiSXdMdllKU0xBRjRKV0FBQnFMRStrL1lsaG1MS2pjY09Kd1FoZ2lPRWJ4QkxxT0NRNitiQk1QZVJvV2hjaHo2R2VZeHIiLCJtYWMiOiIzNzViMWMyMGY0YzdlZDVmMTBjZmU4OTY5MzBiMDJmMTc4NjcxYzJmNjc0NmFiMTgxYjQzYTI2YWMyNmJhMjljIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:30:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2081239368\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-71228332 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-71228332\", {\"maxDepth\":0})</script>\n"}}