{"__meta": {"id": "X5551bc4a337fa31c2456474f7fe30692", "datetime": "2025-06-08 01:06:00", "utime": **********.787605, "method": "GET", "uri": "/financial-operations/product-analytics/inventory-turnover?warehouse_id=&category_id=&date_from=2025-06-01&date_to=2025-06-30", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.191329, "end": **********.787627, "duration": 0.5962979793548584, "duration_str": "596ms", "measures": [{"label": "Booting", "start": **********.191329, "relative_start": 0, "end": **********.685208, "relative_end": **********.685208, "duration": 0.4938790798187256, "duration_str": "494ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.685222, "relative_start": 0.4938929080963135, "end": **********.787629, "relative_end": 1.9073486328125e-06, "duration": 0.10240697860717773, "duration_str": "102ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46077008, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/product-analytics/inventory-turnover", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductAnalyticsController@getInventoryTurnover", "namespace": null, "prefix": "", "where": [], "as": "financial.product.analytics.inventory-turnover", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=433\" onclick=\"\">app/Http/Controllers/ProductAnalyticsController.php:433-509</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02443, "accumulated_duration_str": "24.43ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.734113, "duration": 0.01459, "duration_str": "14.59ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 59.722}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.76115, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 59.722, "width_percent": 3.234}, {"sql": "select `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`purchase_price`, `psc`.`name` as `category_name`, `psu`.`name` as `unit_name`, `wp`.`quantity` as `current_stock`, COALESCE(sales.total_sold, 0) as total_sold, COALESCE(sales.total_revenue, 0) as total_revenue, ps.purchase_price * wp.quantity as stock_value, CASE\nWHEN wp.quantity > 0 THEN COALESCE(sales.total_sold, 0) / wp.quantity\nELSE 0\nEND as turnover_ratio, CASE\nWHEN COALESCE(sales.total_sold, 0) > 0 THEN wp.quantity / COALESCE(sales.total_sold, 0) * 30\nELSE 999\nEND as days_of_supply from `product_services` as `ps` left join `warehouse_products` as `wp` on `ps`.`id` = `wp`.`product_id` left join `product_service_categories` as `psc` on `ps`.`category_id` = `psc`.`id` left join `product_service_units` as `psu` on `ps`.`unit_id` = `psu`.`id` left join (\nSELECT\npp.product_id,\nSUM(pp.quantity) as total_sold,\nSUM(pp.quantity * pp.price) as total_revenue\nFROM pos_products pp\nJOIN pos p ON pp.pos_id = p.id\nWHERE p.created_by = 15\nAND p.pos_date BETWEEN \"2025-06-01\" AND \"2025-06-30\"\nGROUP BY pp.product_id\n) as sales on `ps`.`id` = `sales`.`product_id` where `ps`.`created_by` = 15 order by `turnover_ratio` desc limit 20", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 488}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.767167, "duration": 0.00905, "duration_str": "9.05ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:488", "source": "app/Http/Controllers/ProductAnalyticsController.php:488", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=488", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "488"}, "connection": "ty", "start_percent": 62.955, "width_percent": 37.045}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/product-analytics/inventory-turnover", "status_code": "<pre class=sf-dump id=sf-dump-1096412116 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1096412116\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1024822615 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1024822615\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-807999264 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-807999264\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-285914737 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749344755567%7C50%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InM5UE5xUXo2OWl3K0x6UTZsZWUxU0E9PSIsInZhbHVlIjoicUhtRFBadzFmaTJtUE5LL0ljQmY3SEw4SHg0OFVOV0pSWlFTZTFxNk05MU1JTENPeVFMT1dNei9HWUMreEorckdHbmlaOVRwM1lwQzZTeTk0K0tpYkszNlhkWnBLR3VENm45eEdxMkVyc0RQbE1WQjUvLzBoemxwQ1BJby9JbnFjdTF2UE5HSEI5bG9wK0ZBL3RSMFpyc0NoQSswMHNMRTNudC9lSkVmYVlpS2ZBSUtmdTRKQUxaa25PZW9vOTFERGZtYlRaNzg3NVhsVjNHUTNKWDI4SSttYWFiOVJEL2ZScWxlUVpUZnF3c2tnZXBFV3RCSFZZTnJkcE5vQkN1ZkQweHJ4Y25odTJuc0g4eTBtNkxnWWxIcCtYcE9SNEpsdHZlUjRTbG8xU3prWkZCM1k2ZVU3UVRORklpcWg5dGNzbTFuOFpTZWMyRUJmc2R5cFRVQjI3OUVQM3FWTEQ5TnB1aDJmemJEOHdOVDRXa05YSWp1VjNzUzBNdW5qbjRrdEpRc3JYNndPTFpBY2ZjVkVYblZoeGRCZ3d5WEhGckFKSE45b2V6Z2hKc3l1V0ZxQWNKODBiYlhzQTUrSjkwcVJwRmdMaVBwSUs3TGJpZm5pWnhnV1p2ZlJqOGw3TTM1TU1CQzZzVFJJR1FnNUZkcGorOXpucGlPekhTd1pxTjkiLCJtYWMiOiJkMzdmYjlmM2VlYjI1ZjA1YmMxZWZiNWMxMzA0OGNkMTMzNjcxZTA5YTk5YTk2ZDRhMTc0NTEzMzk1Y2UyMmI3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InNsVXQ3a25CK1JtcjZzVEs0ZmV4NUE9PSIsInZhbHVlIjoiYjJlOUt2SVJseFg2TjlNVVk4ZU5xNU1YNVVUc0w3QVoyemJNdGhGU0MwbW9URFNnZnBjMXpSN0k3SHI2RTFJZTcyelg4RXVyUDNrVHFDSmZIWUduMzdMSkx1MWJiK1dReUtzSjNNUTNXMXRBWXZXWHUwTFhSdHJsQkExWWpKd2M5VHUvT3NtY29OeDgvQmV1eExjV0Y0L0U2d0w2QmJzTWhYU1NPb1VHaHplUjAzc2xmT1NnSk5nOWVhdVhWUERPMHJUYkVmcDY2bHVscVBaaUprZFlmcXNPcFpucnNjbE9GaERuSzI4WVkzWmhtTm5tMU9ZV1QzYkZXSkhTcTBRakh0eDRUUXB6OEdTcThFMk1nRlE4c2pVRG5XVlVjazhta25ySTJvQlFCbTVKOU9NUlFkVHorSkdWb3ZGOXZxbWI3KzdlZEd0cW1LSXdpSk52akRpbkRPNFNzaVZ6cExZSTBKblVxZ3JYak82STVkVnYyU3Npa09UTG5mUGhjVTc1QzJZdHo1TmN5VjE1eUpWZ2NSU2R3VjkrQWIvRnk3L3NoNG13RmFacmt6ak8wRW5Sdk5sVnVJSWV0OTlqdWs2Q0ZFTFlZdVB1VllsNGpqbWc0MlRGaUQxMFc3aTZua1pxTWVpY0lnTjlCUm84RnZYMVZtZXE1SkRXcHk0SEFSa2kiLCJtYWMiOiJkNDJkOTliMzgwYjUwOGYwMzUzY2ZmOGZmZThiNGJkNTY0ODE0M2Y1OGVlYTZkMWZkZDc4MGIzMTM4NmRhNDZjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-285914737\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1283308910 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 01:06:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImhrVVZ4RWFSVER2Tm5sQzhmVm1rQWc9PSIsInZhbHVlIjoiZVJYcGloN0dtTW1WSkl6dVpBY2dWSm5pS1pCNS9CREY3aVVkdFo5WmNzcDQzRVl0czV1cXlEV1M2c0ZGY0dIOWRvZG1rU2M3b1FWK1NkdlQ4N2xRMXFsTWlPRFRxSE5BU3NDWnFHOXBCSjk2YmxTSEdZNThNbXZhMmhtTTBDV0NNdEFCMk5pMmo2VTJuL2xISWcvOUVTcHQ1dkQ1VVVKVFJYSHhNTUV3VjFhNGRzcnd4ZW1uMDFtdkJHVVZlSVBsVitEd3p4cW1FVFFWQjRhMHhNZ0daOEFpSUMvbCtCUWtzaG13dkFmd0RSazJHenRjVFRheGFZcUJYbmR3cUNEM0JZRjFoMUt2UzJ6U1lkZXJrR2w5MnorRjEzM2I0S09IazZvR2VKYlFOa2gwc0R1T0JzZlREcHhpWVFqYXBFUW1USFdXUDQvWTFpcnBvcDE4TjVVMVVIZ20zaGd3NU5hc1o4amw5Q2lBZHRLRlhLNjZKTUxvc25ucGYwK3VVM3UzNEV2R0ZSbVIrcmlrNklwN0ZjdXNsckVLbzJ6RGdzVWUxaVZZcUtCZUpVcGFJUWZGYkV1bWxiZUg5bWw5TnV5QWxwTy9BdGNhQkxwYVFRdm1ZaU8vRytrUXByaE5LbUlUWVp0Mmd2RWMvZVdpdXFqNmRzNGhNV3pVaGJIRGV6RUsiLCJtYWMiOiIwMTU4OWI0OGM3ZTgzYWM0NDJiMGZiNzI0M2UzOWJkZmI5OTg3MzNmYmNhZjgwMTlkNWIzMTgwMjRkZWI4MTIwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:06:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik05NHhpN1BKeFRxdlVDNW1sd0NtNmc9PSIsInZhbHVlIjoiTjVjM2FNNTR3ek04cFJBQnExVmxLL1ZMWnRTNTJoQmVkQ0ZkcysyUVorNFFGQXJSUjIybE0xSzBEc0VISWF0cVdLV2F3ZWVBYWRpenNxdGZVbkJVQWhsNWZBdGpTVVh1UG1RLzA5REtaemExSnEzczZRTFN4RDhURmlnRjV2QUFNandSdTBZN25SZGlzVFgxOVRLODF1Q0hWbGNGVTk1eExZdk5sbTloMTMxZ3hXcEs2ellKVmJiVXNWU0s5blZkN0s5UHdvOXJxejYybzF6ZDBJMGovb3g0clRqSXJQNDlxV0JxZCs1Rjl6cWJrZXIveDg4aTdLUUljWW8xTG1sMnMvNUY5Qk5tZ3B6SFpJL0gxNFhHQ1dHRk96SGo0QTg5a3ZzelpENGtLRnhEOFpwNGtKN2lpcnZMWW5KbTFjOUhMaXFYMTViaHFtWWExWEY0bkRPZGJBRlEyWk5LMTVoSnlXaEZGM0ZVMTZCMGJVNmV4d1F2eGFwdXhkWnpKQ0JaQXYzN0F2L1RwZGVLQlJ2UmRMdldGSHk1SllrMUR5bUtVZHRYM2FnSkd5bjVLQWd2LzZtR251aG1YZ3NxNVkxbFhOSDFDeG5FTHovTUZBVGUyQkV1bnJjVjB4bDEvREY1T1grRVlhdFUxQ1kvQm1qeE1EZjVrWndHMXVtNTkwVlUiLCJtYWMiOiI0NjUyNmQ0YTVlYjhiYzZmNTcxY2E1ZWNjMWJjNjU1MzlmODI5MzE2NDg4MTRiYmJkZTM2YmQ1NjEwMmFkNTY3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:06:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImhrVVZ4RWFSVER2Tm5sQzhmVm1rQWc9PSIsInZhbHVlIjoiZVJYcGloN0dtTW1WSkl6dVpBY2dWSm5pS1pCNS9CREY3aVVkdFo5WmNzcDQzRVl0czV1cXlEV1M2c0ZGY0dIOWRvZG1rU2M3b1FWK1NkdlQ4N2xRMXFsTWlPRFRxSE5BU3NDWnFHOXBCSjk2YmxTSEdZNThNbXZhMmhtTTBDV0NNdEFCMk5pMmo2VTJuL2xISWcvOUVTcHQ1dkQ1VVVKVFJYSHhNTUV3VjFhNGRzcnd4ZW1uMDFtdkJHVVZlSVBsVitEd3p4cW1FVFFWQjRhMHhNZ0daOEFpSUMvbCtCUWtzaG13dkFmd0RSazJHenRjVFRheGFZcUJYbmR3cUNEM0JZRjFoMUt2UzJ6U1lkZXJrR2w5MnorRjEzM2I0S09IazZvR2VKYlFOa2gwc0R1T0JzZlREcHhpWVFqYXBFUW1USFdXUDQvWTFpcnBvcDE4TjVVMVVIZ20zaGd3NU5hc1o4amw5Q2lBZHRLRlhLNjZKTUxvc25ucGYwK3VVM3UzNEV2R0ZSbVIrcmlrNklwN0ZjdXNsckVLbzJ6RGdzVWUxaVZZcUtCZUpVcGFJUWZGYkV1bWxiZUg5bWw5TnV5QWxwTy9BdGNhQkxwYVFRdm1ZaU8vRytrUXByaE5LbUlUWVp0Mmd2RWMvZVdpdXFqNmRzNGhNV3pVaGJIRGV6RUsiLCJtYWMiOiIwMTU4OWI0OGM3ZTgzYWM0NDJiMGZiNzI0M2UzOWJkZmI5OTg3MzNmYmNhZjgwMTlkNWIzMTgwMjRkZWI4MTIwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:06:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik05NHhpN1BKeFRxdlVDNW1sd0NtNmc9PSIsInZhbHVlIjoiTjVjM2FNNTR3ek04cFJBQnExVmxLL1ZMWnRTNTJoQmVkQ0ZkcysyUVorNFFGQXJSUjIybE0xSzBEc0VISWF0cVdLV2F3ZWVBYWRpenNxdGZVbkJVQWhsNWZBdGpTVVh1UG1RLzA5REtaemExSnEzczZRTFN4RDhURmlnRjV2QUFNandSdTBZN25SZGlzVFgxOVRLODF1Q0hWbGNGVTk1eExZdk5sbTloMTMxZ3hXcEs2ellKVmJiVXNWU0s5blZkN0s5UHdvOXJxejYybzF6ZDBJMGovb3g0clRqSXJQNDlxV0JxZCs1Rjl6cWJrZXIveDg4aTdLUUljWW8xTG1sMnMvNUY5Qk5tZ3B6SFpJL0gxNFhHQ1dHRk96SGo0QTg5a3ZzelpENGtLRnhEOFpwNGtKN2lpcnZMWW5KbTFjOUhMaXFYMTViaHFtWWExWEY0bkRPZGJBRlEyWk5LMTVoSnlXaEZGM0ZVMTZCMGJVNmV4d1F2eGFwdXhkWnpKQ0JaQXYzN0F2L1RwZGVLQlJ2UmRMdldGSHk1SllrMUR5bUtVZHRYM2FnSkd5bjVLQWd2LzZtR251aG1YZ3NxNVkxbFhOSDFDeG5FTHovTUZBVGUyQkV1bnJjVjB4bDEvREY1T1grRVlhdFUxQ1kvQm1qeE1EZjVrWndHMXVtNTkwVlUiLCJtYWMiOiI0NjUyNmQ0YTVlYjhiYzZmNTcxY2E1ZWNjMWJjNjU1MzlmODI5MzE2NDg4MTRiYmJkZTM2YmQ1NjEwMmFkNTY3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:06:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1283308910\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}