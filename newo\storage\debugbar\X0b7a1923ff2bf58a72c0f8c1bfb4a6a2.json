{"__meta": {"id": "X0b7a1923ff2bf58a72c0f8c1bfb4a6a2", "datetime": "2025-06-08 00:31:36", "utime": **********.35895, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749342694.796464, "end": **********.358983, "duration": 1.5625190734863281, "duration_str": "1.56s", "measures": [{"label": "Booting", "start": 1749342694.796464, "relative_start": 0, "end": **********.149897, "relative_end": **********.149897, "duration": 1.3534331321716309, "duration_str": "1.35s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.149922, "relative_start": 1.3534579277038574, "end": **********.358988, "relative_end": 5.0067901611328125e-06, "duration": 0.20906615257263184, "duration_str": "209ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45052640, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01026, "accumulated_duration_str": "10.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.282652, "duration": 0.00789, "duration_str": "7.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 76.901}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.314084, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 76.901, "width_percent": 12.378}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.332749, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 89.279, "width_percent": 10.721}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-908600296 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-908600296\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-450346256 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-450346256\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1587586213 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1587586213\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/invoice/processing/invoice-processor</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749342560544%7C33%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImxwTVp1WVlsZ2ZRWDRqbCsyK2cyVnc9PSIsInZhbHVlIjoieTR4OEhrU0dyUGFtV0I5dlkzQWRaV2tUVk5TbEFRdFFvU1NraXA1eFlsejk0RmpCdlhqakhnclZXRVBmTW5KdDd3N3NObDkvenlETm1mOVp0ZktWeVR5K21YaTh1OEVEV0sxT2w0L0lqRzVQb2Q4ZU5pZzk5c0dDUjc4L2pyRXkrckw0TmFYOTF3MDhVZGJKWWJLclBNUGtFU1ErdS9sVEpXNlpNWEgxMldRZGVHa1BKWXhJWFFUM2VseTVSOC9nZnJNNlR4WnFNOE45eWtFbEdzWExzMkwwSW5FSWU1S2NzUUo4TFpQbmlsY3RlODhLcnRNMkJ5UndpYjJ0dW81TUhiVC9weEFqd1ljNXRHVXJqbUNLWXFkV1FQNk5naDRTaVMwZjYraWpBdjlqSjdLSzhFcHJPQzk3ejE4anMyMXBTMEkrb2luQ3hMcXc3aG54YmhxYy9JSmxUQXhWWkJaK3BaQUdyTXJtRk9wdlN1bzZmdGR0Tyt5QUNrZHdJMXRGZzR1QkQrRUFzL0tXMVVtR1U1TXRJRXdPZjJRV3hUK1NXcitIb296b1puL3lMU0N3eDE5WGpTK2FORmNVMFRCcDNQSUh5TlQvaFB5YUJPTFh2TW5NTU9KVlU2NCt2VmZtVmVlQjAwZnZJVzBXZUpSd1FEZHVja0tkb3VIT3k0RTIiLCJtYWMiOiI3MTkzNjZhYWY1NWExMzY4NTU1MGYzZGZmOTZkNzg5MWRjOGUwYWNkMWI0YjY1NDBiOGY2NmJiNTI3NzVjMjgwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImcvWnFpSE1SZFJ4N21qV2tNVFBKTnc9PSIsInZhbHVlIjoibFdUSWdnUnI5UG9BVXJnZTlBMG1kNVpORk5pTEQwUVZWdE93WE0rMFdtbXZWZFNkd001Yjd3SmJzOXQ0elRmb2NpTU1UdTlFL2ZHU3VCRk9ScDlPR1d0bEJyTGZuVldKUFFHbExtZlV6bkQvcWVET25mOHkwd3kxZldHOGpLNXVEVEUzQ0ZvQWRHOHEydHgwOTJIcndJV3gya2RkbmhSMUdVT09oZWpXaURCUktFQ0JEVUp4R1FWSm1ObWxpNkJOLzlEdkVPS1lWWkRET25nbHNaZWFFQlFLVm1TbE05alZTWm0rVjlnTmF1cU1YcHJlaE1HdWNBeUZ3VkE1UTF2VmJlZExQdy9TT2VLNURkQlV6VWgrR2JQeXFzMmgycjhLQlpJR2VhOGtXdmE2SDJhZ0JCNENxb29JWnpPNFliVGtiR2d6c1RQSEd2cy8yWXE4RUw3bmxkTy8rSnpaOGxGQ2ZGNTVTZ0JEQmgzekFyT2dWeHZIRTlIYkhQL3YyRE96Tk92WmZDRyt3bEdYWUd4ZzJoTFp2dUJVeEsrRVlleVNmU1BwLzh6N0d1c0c0c0NCS2sweThhM0ZqZTZMY0pRSXcvdnVYNjFPQmk3SC9RcmJDWHhIcHpIcUR6VmNXQ2x2MVdJd2xDcnlTYmREbEVHbVlNa3YzQS9NV1pJSXZ4WVkiLCJtYWMiOiI5NDc4NTFmYjEwMjUyN2FiNzNkNDZkZDg0ZWQ5MDFhNzQ1ZGY2YWFhODc5ZGZhMTk5MDhjYzI1NTRlZTZkOTc4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1968491642 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1968491642\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1420785817 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:31:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjRzRDd0Rkg3OTF4TStFVjB0dit5TlE9PSIsInZhbHVlIjoib0R0RnNvRTdYMlJ2WHdFVkRVZHQ2aW9ZdE1lMGQwRmZhSzFZdUw1czN6djRUKzNXd0w2S1U5dmpXalJGLzVqOWNHcWlpSDFqeWdmZkk5MStWYThEU3VCRDRnODBFSUY1Q0h2SUpQQzFQN3BScVI2UlRiSWhVZlBFMHNJWTZsVEwzSjYyWWNVbnVIYVRjSjlhQUppZ1QwcHdOMW53OURwSXhwakRCOTNPRHo4V3hyb09HT284Tit6WHlWUUFMQTBtd0d6ZEt4VlhkR2M3UzViM01Idll2MHM2VzVrSzJEeHV1WEljMm1US1U4ZmhBUHYycS8vYlE3ZVl3UGJRUXVtYU9DUStZQTFENUJhMWJzYnFlK2pVZ3NaVmU1d2VWZmpkbUh6ZHNwNTFqQVBPZlpaOFlvVXJXVGF1YkJCRVdIRFgxZkVuOURySkJOK0VyQkRKaU8zQXFxbk5hRWUvRnYrT0xyMlhaMHJxVmcvN0VSZVZ4K2Y2MWtsQTdzME1Yb2dLVGgydDBsZTA3QmozaDlDWG1idUlRQ1JpNm9ZTzVPaUdMaHRTOGtUR0hlYXFESmVvdTR1b3hwcUdiUlZYNGxleTFGS0tNb1czQVNqK3VuRmJUTTFpaWcrZkp5WHVoTHNJbzhvMytJSFd2bnJKTUtPWnVhMzJOeC82TUpRVUlFYXkiLCJtYWMiOiI5Y2U4MjdmYWY4YWNhZWE5ZWQwMjM4YTJmZGEyYjc4NDU5YTkwNjdkMjI5YzJhYWQyM2RhZWM1Yzk0ZDkyZTY1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:31:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InQyTjBYVDgycXU3QWE2dWlOUzBVZWc9PSIsInZhbHVlIjoiZlZYRDR4dnRlYVVkTTRoRUpZZDVrRnkveVRRNGVxcTY1c2xLU281R25WMmhhNDBEZXMrMElSU3Q1ZjV2WDNsaURWTnRxU21SWkxTSVVTRTRWdVRhVFhmck91cHJjMEhzTFBuZFZ3WFU0MWV6T2Y0V2doNlNmNnpNNGV2Q21EZGlqTGE2S29TdjhkVW5mU1pRNHdqaFdxTEQ5LzluaEJISGVJVVNtb2c5RVI3SWFzMGI3RUVqUzNEUGloR2NWUTFucE1Nc3JRUVpKeGhhTEZxdk9CTWpHbTJGT1BrTTIrcDExUnlRKzRmd1E3blE1ZDFrLytFRS9MTXhZMHB4cnZUL0xmdXJseWVsdWZvcEl2UVBkeWtyTWVEODBQUk5sSnpMeGNkVXpWS3RTRmVIbUZOTTFtdkRMdTBSazhYSDB5bUFjNm13bjd4VVd5SzRTc2JGQzdTWkFyOTBReHBCZTBIZFY3VWNSL3Q5ZVpuMVVXTXhjbEZkMkFOUm9DRUkyT2Jqa2p5WjZNQzNjUHRrUEh3dzNoeStaa1Rka1gySFFTckppUDYrWHVPUmpMMXhRUTArYjRkUWRkSUFDck8wRmZrdUVjQ2x2aGpJb1JlMWlPTDN5Ly80Y3FUSXJFVUZ6VmhHRGk0dHpsRXlXUWU3a0FkcERsMDhyNHRpT0N1TzBGMFUiLCJtYWMiOiI4Y2U4OGNlMWIwNTkxZTFhOGVhMTcxYzBjMzI3YzgyOTUyNmNmNjYyMjNhYzg0MDA4MGUwMjBiNmY4NzJkYTYwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:31:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjRzRDd0Rkg3OTF4TStFVjB0dit5TlE9PSIsInZhbHVlIjoib0R0RnNvRTdYMlJ2WHdFVkRVZHQ2aW9ZdE1lMGQwRmZhSzFZdUw1czN6djRUKzNXd0w2S1U5dmpXalJGLzVqOWNHcWlpSDFqeWdmZkk5MStWYThEU3VCRDRnODBFSUY1Q0h2SUpQQzFQN3BScVI2UlRiSWhVZlBFMHNJWTZsVEwzSjYyWWNVbnVIYVRjSjlhQUppZ1QwcHdOMW53OURwSXhwakRCOTNPRHo4V3hyb09HT284Tit6WHlWUUFMQTBtd0d6ZEt4VlhkR2M3UzViM01Idll2MHM2VzVrSzJEeHV1WEljMm1US1U4ZmhBUHYycS8vYlE3ZVl3UGJRUXVtYU9DUStZQTFENUJhMWJzYnFlK2pVZ3NaVmU1d2VWZmpkbUh6ZHNwNTFqQVBPZlpaOFlvVXJXVGF1YkJCRVdIRFgxZkVuOURySkJOK0VyQkRKaU8zQXFxbk5hRWUvRnYrT0xyMlhaMHJxVmcvN0VSZVZ4K2Y2MWtsQTdzME1Yb2dLVGgydDBsZTA3QmozaDlDWG1idUlRQ1JpNm9ZTzVPaUdMaHRTOGtUR0hlYXFESmVvdTR1b3hwcUdiUlZYNGxleTFGS0tNb1czQVNqK3VuRmJUTTFpaWcrZkp5WHVoTHNJbzhvMytJSFd2bnJKTUtPWnVhMzJOeC82TUpRVUlFYXkiLCJtYWMiOiI5Y2U4MjdmYWY4YWNhZWE5ZWQwMjM4YTJmZGEyYjc4NDU5YTkwNjdkMjI5YzJhYWQyM2RhZWM1Yzk0ZDkyZTY1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:31:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InQyTjBYVDgycXU3QWE2dWlOUzBVZWc9PSIsInZhbHVlIjoiZlZYRDR4dnRlYVVkTTRoRUpZZDVrRnkveVRRNGVxcTY1c2xLU281R25WMmhhNDBEZXMrMElSU3Q1ZjV2WDNsaURWTnRxU21SWkxTSVVTRTRWdVRhVFhmck91cHJjMEhzTFBuZFZ3WFU0MWV6T2Y0V2doNlNmNnpNNGV2Q21EZGlqTGE2S29TdjhkVW5mU1pRNHdqaFdxTEQ5LzluaEJISGVJVVNtb2c5RVI3SWFzMGI3RUVqUzNEUGloR2NWUTFucE1Nc3JRUVpKeGhhTEZxdk9CTWpHbTJGT1BrTTIrcDExUnlRKzRmd1E3blE1ZDFrLytFRS9MTXhZMHB4cnZUL0xmdXJseWVsdWZvcEl2UVBkeWtyTWVEODBQUk5sSnpMeGNkVXpWS3RTRmVIbUZOTTFtdkRMdTBSazhYSDB5bUFjNm13bjd4VVd5SzRTc2JGQzdTWkFyOTBReHBCZTBIZFY3VWNSL3Q5ZVpuMVVXTXhjbEZkMkFOUm9DRUkyT2Jqa2p5WjZNQzNjUHRrUEh3dzNoeStaa1Rka1gySFFTckppUDYrWHVPUmpMMXhRUTArYjRkUWRkSUFDck8wRmZrdUVjQ2x2aGpJb1JlMWlPTDN5Ly80Y3FUSXJFVUZ6VmhHRGk0dHpsRXlXUWU3a0FkcERsMDhyNHRpT0N1TzBGMFUiLCJtYWMiOiI4Y2U4OGNlMWIwNTkxZTFhOGVhMTcxYzBjMzI3YzgyOTUyNmNmNjYyMjNhYzg0MDA4MGUwMjBiNmY4NzJkYTYwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:31:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1420785817\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1604359298 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/invoice/processing/invoice-processor</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1604359298\", {\"maxDepth\":0})</script>\n"}}