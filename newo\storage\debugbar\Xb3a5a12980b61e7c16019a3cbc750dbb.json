{"__meta": {"id": "Xb3a5a12980b61e7c16019a3cbc750dbb", "datetime": "2025-06-08 00:58:26", "utime": **********.968812, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.302987, "end": **********.968836, "duration": 0.665848970413208, "duration_str": "666ms", "measures": [{"label": "Booting", "start": **********.302987, "relative_start": 0, "end": **********.884205, "relative_end": **********.884205, "duration": 0.5812180042266846, "duration_str": "581ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.884217, "relative_start": 0.5812299251556396, "end": **********.968839, "relative_end": 2.86102294921875e-06, "duration": 0.08462190628051758, "duration_str": "84.62ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45593584, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0046099999999999995, "accumulated_duration_str": "4.61ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.930732, "duration": 0.00323, "duration_str": "3.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 70.065}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.9466221, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 70.065, "width_percent": 14.534}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.955549, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 84.599, "width_percent": 15.401}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-276020523 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-276020523\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-584883917 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-584883917\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-599889323 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-599889323\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i31pha%7C1749344302839%7C9%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImlOcVFuYzJoZkIwb2VVTXJOa1FtRmc9PSIsInZhbHVlIjoibEIyNWM1Q2wxcTNsZExxbzZCRlFNTTZjbzZya1hhK3NQMHdTZ0h1ZkI4VEF2T1hJRnJEdVBQSnYrMWxyb3RpVEZkV1dxUzc2dm1ESVJoQWhoSFU5REU3ZTB4bjVBbUFXTVNXWG9qM3BwUWZVK0FCcUxuejZXN3dTV1p3TG9OMWJyWElFdTZwTDlscVJIT3Y0eXdJY3orNHZrN1NkMlVERmVHdWIxQ2o3RytXTFdxbThQRHNaRlErbjh3WWUrMnZmM3ZHdjRjT0JSNTRlRk1vZy96MnNoeVowN0RZNFdwZktWbkNya0wvQWF1V0tQL0dWdTdqNGk2ZldGZUozT29oNzFORHdENExESFFoYjV3YUllSFFGejhUZDhWaEk1NDRkd3psL2NzRmJScGp6dlRXVXppRWJsaUpMNEdzdUhjaW9WZDY5OUxEV1dYSnJyUnY1clN6T3BnZmtnb0ljaHhVTEtiWWloL3QzZnZmeHAxZlZVK0FOcmtHcG5rQWlVc2loQmlOaUpUQVdWSXZDaWY3Y1l1TTI1NDQ1TGJYZTFoLytkUHRXVm1xOFMzZk8vUytsRmU1QWxUUVlGazNxWGpOREZTaFhzOExnV2pPYmR5MFl4b1RBZjJTWHlDSzJBcDNCYzgyMnlaeXo5cFhpZW1TZnFrVlkxOGJoYjVtUStUejMiLCJtYWMiOiIzOTIzY2I2MzI0M2Q5Zjc0YjNlYjA1YWJlZmY5MTg0Yjk4NTk1NzAzMDg3ZmZiNThiOWM5ZGM4ODY1NmVlMGVlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlZiZjFTeTJzNWJFMkVoVFlIbklkd1E9PSIsInZhbHVlIjoibGIrbHlhRWFpWDF0OEVFUFZsdFNKVU9ORlBPRVQxR1Q0czA3M0JKTFZpZWtvT1dEL3l1dzNmMEg4VUZoWVlHSEI0bTg2Q3hYeisySVhpb0h1aWJMbGp2SENBRnVsMUNiN2JhamhOMDJVVW9JaVhsSG43UDlIUmhxS0d1cEdLY3E2ZDNmOVNxRnJOeURkMVE0RHczb3BvbkhmYUdrVWJKY0ZJNG5HYlpnZFk4WWpZZG15aGRrWVNtcFVHdzJaM2dYbThjR2N3MzZQcTNhdTdtRmY3YXVIQ0lSQ24vUGE0TlVMTnhaZHc3dmw0QUFxVFF1UHA5N053ME1yVXh3YkdDMkZ5ZGltZUNVcm1QM2xRWXo0bE1Bdk9mcjZoYjFrM0t6bUZiSXZINndhaitvYjV2UnZEVGRPQnpVTFJTWUdCZ2xFU255RlVQNTlGSXBLSFJnR3FOTGpVWGwzTTA4Q2Rhc0E4S2ZEYTk1cEZDbjg1bDFpcThoWWdWUWs2ZVJQRkxEMk9DWU4wRkg1Y0pIS2N6U2lDMVNDR0dwWGFIK2xYOEVCSGNNNnNJTC9ILzFqVy9Zd2tHMUFDSFJ6WkcxYWJZRzI0T21UdmpIVWYyRVc0QXZYcGVtQ1V5aVU3c3JzZmNPOXUxbkxNMGNYWXFDQm9hUE9aZnhIL3pPdExiQlhMN3IiLCJtYWMiOiJkZmQ5MTk0NjAxYzdjODIwNmFkMjg2OGNmMGY1YjJiNTY5OTAxMDNkYjc1ODdiZGZkNTU4YzQ2NjNkY2FkMGY0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1036463136 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1036463136\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1389583406 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:58:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InpJdWM4Z3VsV3plM1Q0NFBzRFZKZ3c9PSIsInZhbHVlIjoiN2VKRy9wN3k0TWdMdktzVzFYMld4UjJJQXJYN3U3Y2NhR3JheDJ2eXZ0ZnVNczhEQ0tKUHZnKzRiZWplYTZoMEEvbjArM3FqNGRQL3RWTU52Z05vdG9HbWllRyt6QzRFS3AyVWFlU3hnY1Avbk9aUXBCVmJRYWJxOHVTcndVb0tPczNHdCtiNHNIa0pZNWJRQm9QRTFGN0RLMCsxR25vOG1abEZ5SGswTU5PeE5tbk1sb3ZlU3NrRnF6NTJHS1I0U1VodE10STVUYmhyUG00dkowM2FoUjFXWm0vT1o0azBHc1doMW5lVG5DdStFQnA1a3ZwcUV0MzdsSHk5anpmS0phNzE3TzFibVJwYnVvWTJWSExIYWRUbkZUTGo3STNMdDdQZS9jM1pjWHlSZmJZeWZMQXo4MmlaUFZHUlN0bzBxZzVzRXpFT2U0aWZlZkRScGcyM2x1UnBmS3k5Ni9IVE0yN2NCQUM2d2p5bFBlS090S2V6WVlkUWx0WEE0SDNBOUNrQmRDeThHdi84VDNZWUtZNDNNMjRSempqTldDY0RDTjJTb3ZidENxM0haU3BEZGYzTU42SE5yTm1QZG5zZzVCTmg4S0lzZWM4L3NCN1FPTXF0TStaRDR2YXJNR0VNSVVmNnVwdWRwQjJWVlc3SFJSUTdNeFpIcm53VnlhNlYiLCJtYWMiOiIzMWY4MmY4NGJmZDU3MWI4MDEzODE0ZWNhYzkyOTAxNmJhNGFlYjI1ODM1ZjRiOWM5NTczOGQ2YzliMTllZmUxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:58:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InNpUUlkVC9EbVROdkJ6eURDeEpxOFE9PSIsInZhbHVlIjoiY1RGTlZVaDVQcGRSVFpCZEs5NTU1ZFFaVWxWZ0ErZnRFNEtWQXZtU2ozNzdtQmdXbHM1bVlsQ2tGSmZLTVptYVJZOWhOV3ZMaktsY3NKK01UR2ptclhJTkFlTDE1cVNhZUdaM1F1Z2xQdFFlT3hOMEkxMFF6MjdLVmVUQjZvVTRUcXlKb1orVHVSRUNSeDR6Q01nVDBPSW9nVlQ3SjV5ckFjMnh4YnBHQ0N5ajBPRXZIMVp0SGV5a1pXbjNxVTMvYk1Jelg3bDRKaWpSNjFqZjA0L0RJcDR6SDRKSHFVZUpYay9nWWxVYVVpMlZrU08xSi9PUjFwOStIZ0w1NU1rbUVOS0E3TlBvdEN6aGdUWVdOTHJ0N0p4QUF5enlYQUNLTmdyTm05WkRCeXFiaFc4dThGVWlEU1d3MTNhOTNoc3RzTllFa0loa3dId0VJRTBGbW1ISlhKalA2TzJEcXNNWE5WdlRzbnNBOXFhaERTblI4S1JoaG1RaFBLQUVaNFRUTUp0RXdlNlQxcmpVd2JCZytlUGRTSjVEc0FFeFlpaWIxZjF0YkZ2dWh6a3o3RjVaRVY0SzZrTTBwekt1VVNhYWMrVjU0SVpzWmg3eFZGeS8yaXFuU1IxUkx1bkJxMnhaaHlJMUkzQXBwMFpEdDJFUTk4WEdDRi9aZmFORThtS0kiLCJtYWMiOiJhZDZlMzhjYWIzYzNkZDRhZjZiYzNiMmNhYTM2NzdlYTdhZGI2ZjExN2I4ZGNiZDEzZmEyMmE0NzRjOTA5MTExIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:58:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InpJdWM4Z3VsV3plM1Q0NFBzRFZKZ3c9PSIsInZhbHVlIjoiN2VKRy9wN3k0TWdMdktzVzFYMld4UjJJQXJYN3U3Y2NhR3JheDJ2eXZ0ZnVNczhEQ0tKUHZnKzRiZWplYTZoMEEvbjArM3FqNGRQL3RWTU52Z05vdG9HbWllRyt6QzRFS3AyVWFlU3hnY1Avbk9aUXBCVmJRYWJxOHVTcndVb0tPczNHdCtiNHNIa0pZNWJRQm9QRTFGN0RLMCsxR25vOG1abEZ5SGswTU5PeE5tbk1sb3ZlU3NrRnF6NTJHS1I0U1VodE10STVUYmhyUG00dkowM2FoUjFXWm0vT1o0azBHc1doMW5lVG5DdStFQnA1a3ZwcUV0MzdsSHk5anpmS0phNzE3TzFibVJwYnVvWTJWSExIYWRUbkZUTGo3STNMdDdQZS9jM1pjWHlSZmJZeWZMQXo4MmlaUFZHUlN0bzBxZzVzRXpFT2U0aWZlZkRScGcyM2x1UnBmS3k5Ni9IVE0yN2NCQUM2d2p5bFBlS090S2V6WVlkUWx0WEE0SDNBOUNrQmRDeThHdi84VDNZWUtZNDNNMjRSempqTldDY0RDTjJTb3ZidENxM0haU3BEZGYzTU42SE5yTm1QZG5zZzVCTmg4S0lzZWM4L3NCN1FPTXF0TStaRDR2YXJNR0VNSVVmNnVwdWRwQjJWVlc3SFJSUTdNeFpIcm53VnlhNlYiLCJtYWMiOiIzMWY4MmY4NGJmZDU3MWI4MDEzODE0ZWNhYzkyOTAxNmJhNGFlYjI1ODM1ZjRiOWM5NTczOGQ2YzliMTllZmUxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:58:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InNpUUlkVC9EbVROdkJ6eURDeEpxOFE9PSIsInZhbHVlIjoiY1RGTlZVaDVQcGRSVFpCZEs5NTU1ZFFaVWxWZ0ErZnRFNEtWQXZtU2ozNzdtQmdXbHM1bVlsQ2tGSmZLTVptYVJZOWhOV3ZMaktsY3NKK01UR2ptclhJTkFlTDE1cVNhZUdaM1F1Z2xQdFFlT3hOMEkxMFF6MjdLVmVUQjZvVTRUcXlKb1orVHVSRUNSeDR6Q01nVDBPSW9nVlQ3SjV5ckFjMnh4YnBHQ0N5ajBPRXZIMVp0SGV5a1pXbjNxVTMvYk1Jelg3bDRKaWpSNjFqZjA0L0RJcDR6SDRKSHFVZUpYay9nWWxVYVVpMlZrU08xSi9PUjFwOStIZ0w1NU1rbUVOS0E3TlBvdEN6aGdUWVdOTHJ0N0p4QUF5enlYQUNLTmdyTm05WkRCeXFiaFc4dThGVWlEU1d3MTNhOTNoc3RzTllFa0loa3dId0VJRTBGbW1ISlhKalA2TzJEcXNNWE5WdlRzbnNBOXFhaERTblI4S1JoaG1RaFBLQUVaNFRUTUp0RXdlNlQxcmpVd2JCZytlUGRTSjVEc0FFeFlpaWIxZjF0YkZ2dWh6a3o3RjVaRVY0SzZrTTBwekt1VVNhYWMrVjU0SVpzWmg3eFZGeS8yaXFuU1IxUkx1bkJxMnhaaHlJMUkzQXBwMFpEdDJFUTk4WEdDRi9aZmFORThtS0kiLCJtYWMiOiJhZDZlMzhjYWIzYzNkZDRhZjZiYzNiMmNhYTM2NzdlYTdhZGI2ZjExN2I4ZGNiZDEzZmEyMmE0NzRjOTA5MTExIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:58:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1389583406\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1784864768 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1784864768\", {\"maxDepth\":0})</script>\n"}}