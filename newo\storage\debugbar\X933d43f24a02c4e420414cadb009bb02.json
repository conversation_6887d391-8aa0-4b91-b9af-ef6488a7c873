{"__meta": {"id": "X933d43f24a02c4e420414cadb009bb02", "datetime": "2025-06-08 01:18:28", "utime": **********.34543, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749345507.572436, "end": **********.345453, "duration": 0.7730169296264648, "duration_str": "773ms", "measures": [{"label": "Booting", "start": 1749345507.572436, "relative_start": 0, "end": **********.249605, "relative_end": **********.249605, "duration": 0.6771688461303711, "duration_str": "677ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.249621, "relative_start": 0.6771848201751709, "end": **********.345457, "relative_end": 4.0531158447265625e-06, "duration": 0.09583616256713867, "duration_str": "95.84ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45040936, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00488, "accumulated_duration_str": "4.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.300167, "duration": 0.00299, "duration_str": "2.99ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 61.27}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.318447, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 61.27, "width_percent": 13.934}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.330418, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 75.205, "width_percent": 24.795}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-189038791 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-189038791\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-265425157 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-265425157\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1561192398 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1561192398\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-63980097 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749345302717%7C55%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjZnMmZkOUR0VlZwak0vcm5zRDVGK3c9PSIsInZhbHVlIjoiWE51UTFxNkpEL3B1aGlvZFB1dGw2MlA1UDFVYzMrU1R1RjFkdFJVT3psRWgwbktVVE5IRkd2NlU0Rmt0amN2TGFCUkZFeG5YaHVjSW5WL2NzWUVJZVVENnlESjVQZnh3MThUdE1PL1dNU1JuMFpuQ3BWQlhWSGYzaGI5dlFmUS9ldCtaUXpNeUVVK3MwaEIwS0MwM014ek9ZaHRtR0JTTDJIdGtZUldZNzNtQUxqc3QvODdPMSt0empjL2ZIbENCakZnbFRUckVPS1FKMUpTL0tVNkJtOE01NTRYOGZUNEFVbTR4dlZFUElUTlpQWnRrNzN3cDVMT0ZqeFNGU2I2YWU5bkE5dnd4NFErandsaTJmdWMzOGhFZjNMY2ova20rVTBLalIvMHRuZkZDNVBjWjJxQWVRQlVHL0tTWW0vYmhzK3ZQTHNrYWtEVkFZS0Z4MWdHS1BiZ0VmcGFydk9Bc1QxWmFsVUR0VUx2NU1taU5JOEFCT1lkRkF2UlFnTUIxY05pUGUwbW5aUjlwbU1kRkhQVzlEcExXQVhCNGVxdFZ0Y3lKMWppWit5MGEwNlp4NW5UQSthT2xMRlgyOHVmZEFUUmhaV1FQWkpKNnN6WUZmaXI5d1ZyOHo1a2dSQlR6VmVnKzNYdjNGT09WellOVm1pOXZKR3hQS1cycHJZU2MiLCJtYWMiOiI5MjI5ZTllNjJiNmEwYTVlN2I5ZGQ1MzlmYjRmOWJkODcxMGJhZmU4YWNhMTE4ZjhmM2RjNDVlZGJlOGFjYzhlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlpnV0VtcU1IZklzdlQ0aWN1TmNvUVE9PSIsInZhbHVlIjoiZDdPZ0V0anQzcUNOT0hsODFwbFAxSFg4amRSVERsQlBUbFlNRU9rdDFtczFZY3lmZXYrNnFPNEFEcXE4SlQ2UldVeXlEMCsxb3pMUk9tZkNEdUk0UFFxaVZCNVRXUUo0RDJ5YUd0aERsd21Yc2Npd0RrT2dIeStoWUY0Q2Y0MEtPNGtWTHRGaWFIMnlwWWdKV1pqTm5mekxkR2dHeXdqQlUzUTAvdmFncGErTEZSYU1lMTFSMTBUbEhtY0tjN0R2c29qVTlRT3dSNERydHlDV0VTeWRXWTUzbXVacGV5MjRKUEwyaS9nMWQ3Vi90WU8zYk5ROS92c0dnV1k2UWltT04yc3p2UU1IZC9YazAxTUpzR0VJcUxkekcvZ2FMd2g3dFk5dUw4S3dvT1BJdkdBOHBQQ2FoS1R2SEh4cVlNOVNuMGMrcWtTOVAyblBzLzNSN2dZSkJxdWtQa1QzdzJmY21QVTRFT2dlK3NnL05IcUJpZjJYajFpc29MY014N2xrbzVTVDB5WVRhQ0hMVUNUdFFlNmFtbHVZWSt3VVo3K1diYkoxQ3d5a21iRlNLTGE1ZHhOUStaNXRKT1NKZExQZ1dSemk2bmhreTZhU3ZHYzlub0c1RjdoTWpCNy9zbmpudG1PS2dpa0RGZTZvbXRvQm4rZWg5ZHFTdDVFMnFoYVUiLCJtYWMiOiI5MDg3NmI5MzgyYmVlMDMyNTZkZTU0ZmFjYTJkNDM3OTgwMWFkYjkxYjgwN2NmNmE1MjNiNGZlZDhlYmRiNjEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-63980097\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-299916706 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-299916706\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-130642072 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 01:18:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNadWpjMHRkZmtsSFhBWlFRRTBIdXc9PSIsInZhbHVlIjoibFZFclNucmRBK0ZNU3RkbTFLYUVDYjQvNy90N3Z6TUoyaTJkQ0VXYkdXOFdPT3ZaNkIwSXFHWm1hc3dKSzZ1Qk9zb2ZuSXlWTyszOG5vYU9lbXE0MHJJR1NlTXlpWDdNUHhNT0ZXdWhTZzJFdElIQ0NMcTFUdlRnWGtLMWFVQjZXR2swb0ZIcFBxUU50Y2s5WUJtUTBrN0Y3a2VFQXZpdWROR25qWVZaNHNGTG56ZjRHSU1FSEw0TVFWOFJId3psRU9wTHRORmZhYWRORVFxUENuN2RPRHBUamNnYTBHRFZwMnRpMmhDVWtveC8zcnJaQjJLUTdKdzFlN0QzZEdkQm1lWFo1Q1poQmN4V1V6RXZqTC9ZUHlxOTM2Y2VvNGk4YkNIVURFTEZPeXFCaVA1dVpCVFV3TUNiTnBwNXFKR1RvbVJCT3lQR2NybENSWkJlZXd4MVFYbEFRZTJMNk9QNE80cHlDVVMrYzJycWdXczZNWmR1M2ZJRGhBK3doUU1TMncxeUZtR3lpMG1NQmR3ZlgwbDc4WDlFY1JZMDI3cm5UeHUvakpNV0RiVU9rVGZFZjNGcnkwUThXM1FCQkl5azJLcWRabWVyRXVlY2xXei84am9NMGhkTG5zNnFYeGFHTlNZTnV2NzJQcnBoZjN5YklRZ2hYdHJTMTg0VW9qYW0iLCJtYWMiOiIzOThkODhmYjA0NDYxYWVmOGRlZGYzM2MwMTdlMzIzZThjZTFhOGIzNDNlMDQwYjBlNmI0ZDIxOWY1NzE2YjQ0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:18:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjJKcDRFeklQWE54ak1pc2psdGdwdGc9PSIsInZhbHVlIjoiTytrbmVrdUxxdC9TQXpKTlJ3b1dGRXFiWDNBa1EyWTZQMnFvS3JQcEd6dnowekYvbGErQzVMdGtqVG51VGJZQjFZYjc2UGJNc0t0cHVIVFpNenErM0J3bEFzZ1lKeDJYSFRDbVM2UllhNzdCckI0dFd0NEdmK1cyQ2IzZDNJR08xaEtmamlqZklaRncxM1pzczY0Z0Qrdy9iUUYyUFNoTVYvT2ZCUHpBKzgySW1paVN0YlhkS1hyemVVOEJPcVhuUS9SZllVekJ2Qm9GSWpTTXJBdE5hK3QxUTJ0WGFjSmViVkxNalcyMmhhbjFlbTA1Z0lGOEt6MXBmYW9ZdTVhRGNTU2VQL3F1Y2wyNEZsT0Njc01BQklwTGpMY0VDK3h6Um5SeXJwR2R2dk80ZG4ySm0yUFAyVGpLcWFZa2Y4NVRQdGI0Rjc4bk5DejdBVXBkMjUxb3NicU9DT1g2NmhoeWVYeWhVUFBJUEU1dm9SaDUyYVd2SE1hOURsK3UzcCszV3pKczJDcVJNeHc5N1UxTUJzdHhkbkFZemlCVUVEQWVHcHZFVzd2WE50Sm9YVTFEQ1VIWFBZOWFFZzQxbStXMlIzeHdYRGxzQlBOZVJLdkxCeEZEa0NybDJhRFpBUFl6QlFFaEl1YzdPRnl4UHZ3M0piRUE1dXpDVXJ1TnN0U1MiLCJtYWMiOiIyNGRkMDg2MzRkMjZmNjQwMzdiZDRhNDVjMzQyZDgzOWM5NTk1Yjg5YTg1NzdmMGE2MGIzMDQ0OGY1NmU4MWRkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:18:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNadWpjMHRkZmtsSFhBWlFRRTBIdXc9PSIsInZhbHVlIjoibFZFclNucmRBK0ZNU3RkbTFLYUVDYjQvNy90N3Z6TUoyaTJkQ0VXYkdXOFdPT3ZaNkIwSXFHWm1hc3dKSzZ1Qk9zb2ZuSXlWTyszOG5vYU9lbXE0MHJJR1NlTXlpWDdNUHhNT0ZXdWhTZzJFdElIQ0NMcTFUdlRnWGtLMWFVQjZXR2swb0ZIcFBxUU50Y2s5WUJtUTBrN0Y3a2VFQXZpdWROR25qWVZaNHNGTG56ZjRHSU1FSEw0TVFWOFJId3psRU9wTHRORmZhYWRORVFxUENuN2RPRHBUamNnYTBHRFZwMnRpMmhDVWtveC8zcnJaQjJLUTdKdzFlN0QzZEdkQm1lWFo1Q1poQmN4V1V6RXZqTC9ZUHlxOTM2Y2VvNGk4YkNIVURFTEZPeXFCaVA1dVpCVFV3TUNiTnBwNXFKR1RvbVJCT3lQR2NybENSWkJlZXd4MVFYbEFRZTJMNk9QNE80cHlDVVMrYzJycWdXczZNWmR1M2ZJRGhBK3doUU1TMncxeUZtR3lpMG1NQmR3ZlgwbDc4WDlFY1JZMDI3cm5UeHUvakpNV0RiVU9rVGZFZjNGcnkwUThXM1FCQkl5azJLcWRabWVyRXVlY2xXei84am9NMGhkTG5zNnFYeGFHTlNZTnV2NzJQcnBoZjN5YklRZ2hYdHJTMTg0VW9qYW0iLCJtYWMiOiIzOThkODhmYjA0NDYxYWVmOGRlZGYzM2MwMTdlMzIzZThjZTFhOGIzNDNlMDQwYjBlNmI0ZDIxOWY1NzE2YjQ0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:18:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjJKcDRFeklQWE54ak1pc2psdGdwdGc9PSIsInZhbHVlIjoiTytrbmVrdUxxdC9TQXpKTlJ3b1dGRXFiWDNBa1EyWTZQMnFvS3JQcEd6dnowekYvbGErQzVMdGtqVG51VGJZQjFZYjc2UGJNc0t0cHVIVFpNenErM0J3bEFzZ1lKeDJYSFRDbVM2UllhNzdCckI0dFd0NEdmK1cyQ2IzZDNJR08xaEtmamlqZklaRncxM1pzczY0Z0Qrdy9iUUYyUFNoTVYvT2ZCUHpBKzgySW1paVN0YlhkS1hyemVVOEJPcVhuUS9SZllVekJ2Qm9GSWpTTXJBdE5hK3QxUTJ0WGFjSmViVkxNalcyMmhhbjFlbTA1Z0lGOEt6MXBmYW9ZdTVhRGNTU2VQL3F1Y2wyNEZsT0Njc01BQklwTGpMY0VDK3h6Um5SeXJwR2R2dk80ZG4ySm0yUFAyVGpLcWFZa2Y4NVRQdGI0Rjc4bk5DejdBVXBkMjUxb3NicU9DT1g2NmhoeWVYeWhVUFBJUEU1dm9SaDUyYVd2SE1hOURsK3UzcCszV3pKczJDcVJNeHc5N1UxTUJzdHhkbkFZemlCVUVEQWVHcHZFVzd2WE50Sm9YVTFEQ1VIWFBZOWFFZzQxbStXMlIzeHdYRGxzQlBOZVJLdkxCeEZEa0NybDJhRFpBUFl6QlFFaEl1YzdPRnl4UHZ3M0piRUE1dXpDVXJ1TnN0U1MiLCJtYWMiOiIyNGRkMDg2MzRkMjZmNjQwMzdiZDRhNDVjMzQyZDgzOWM5NTk1Yjg5YTg1NzdmMGE2MGIzMDQ0OGY1NmU4MWRkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:18:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-130642072\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1749377164 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1749377164\", {\"maxDepth\":0})</script>\n"}}