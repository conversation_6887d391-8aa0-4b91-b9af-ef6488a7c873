{"__meta": {"id": "X192716602fe34328041bc70933e880bb", "datetime": "2025-06-08 01:15:17", "utime": **********.071325, "method": "GET", "uri": "/customer/check/warehouse?customer_id=7&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749345316.508816, "end": **********.07135, "duration": 0.5625340938568115, "duration_str": "563ms", "measures": [{"label": "Booting", "start": 1749345316.508816, "relative_start": 0, "end": **********.000448, "relative_end": **********.000448, "duration": 0.49163198471069336, "duration_str": "492ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.000462, "relative_start": 0.49164605140686035, "end": **********.071353, "relative_end": 2.86102294921875e-06, "duration": 0.07089090347290039, "duration_str": "70.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45177000, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00437, "accumulated_duration_str": "4.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.041307, "duration": 0.0028799999999999997, "duration_str": "2.88ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 65.904}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.0565362, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 65.904, "width_percent": 14.645}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.061368, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 80.549, "width_percent": 19.451}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-132099020 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-132099020\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1198384215 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1198384215\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-947691940 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-947691940\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1252041577 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1939 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i31pha%7C1749345246637%7C11%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InNZNVlEVE9xTTR4M0toaVFobVp5R1E9PSIsInZhbHVlIjoieWhVRkdzZndVemoyTmh3OWlRdW9XV1lKd21ST2pnbURadnlPanhoVG1nbEdDZitDSlZvK1RvN0VnS2FKZGEyeEoraENucDlvNHg4ODlycmxIQ0dZaUV6UzJTRktjN3dTeExLSDRnRUcweG5tTGs5cFpUVXJYbno2NHhzNGtBQ1NkcjdvbjlGeUh1WHJCMjRQVEI5c2ovQXpLbExlUVpsc21xejFhTnMraDBpUXNmS25FZVQ1Zkp4TWhOamF1Qy9pV1dBeTdJYytURjNmK0JLRVh6NmJYb3dDSTJWcUFIUmw4ZUIyRkFjbEFTdlBIVjZOL0RuQ1ozZlRINElEN3dWWUJZOVdaMi9nQ2s2UXl3cVhUOHBWVk5PdCtoNTdKblA0ZWRaNG1kOGh6VzJ0QlJrZkIrT2NmMnk2V2JRcFlrb2lNK0VmSHlkQVl5Wnc1bE5OWmlvc25IL2t5T3EvTzRGMFZLVE9NNTNZTGEzNTMyeEJ4OUI4VnZUd0NtYkQwdTI1cGpwQjBzSVFxalRPODFVQ3ZyK0VKT255SVhuRE5FYXlrL3RWVURUbE1aUjVBU0g2TFZabXVUK2FZZG5yM0FTYWU3dDN1UFVPb2swU0tLSHgwNG03czcreU1KR2ZUT0RjWWs1WVNIS3JUdTZNVDdqRU5mcEZEQnZlUzQ4ZGVHYlUiLCJtYWMiOiI3ZDEwMDliOWMyNTg4N2M4NzZjOTNjYWIxMGJmMzU3OTIxODZhOTkzZTY3NWM3MzJiNDNlYzZkMjVhMTJhNTg1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Iks2ekx0YWlGNVBzb1lJZ1BsL3E0T2c9PSIsInZhbHVlIjoiRlFUd0o3V3F4OXUzODJnYVptV2NlUk53aWR0K3NBZVd6VjYvQ1hqNFVzc0tVMjZOOHUzZGdkNFU2U0xLdVF6N2dXbm51eXNjQ3JBRmxETFhjcFo0WVdsKzNXVkg3S2dyT21LcjVyNjBkc1RpZUVXVVdvbjlxRkJCNkE3UEtVMWRWNDArR0s4S3RMQmw5eUJVZmJOdmFQbTlpTEFSa2NkWXhFTWJOdDc5a2M3TFc1N3JOMExYTkNXT2lpZkRNTDNBdVNjakZiWFQrQVZveE16b0ZlZ0FFTnB6WnNmL3dtcy9pd051WWp1T1Y1OFFUNHZTcHVlalpML1lTd2M2VERPK250ZWxqc0tHaUp2ejBodjNtQmgwYWNCUGZ3Z0x4eUt0dEc1TkRwLzNHYlVDK0lRcnI1R0lSVkJWQ2QyMVprSlZ1dVVRd21jcm1FdEE2RkphVVVicmR1VUY3VVh1UUhDWjR0RktJaVFDQytNNk9EbkY1TDN0M3RnRG5ERGhyOE1pcDladFV5bCthRm5EemFBWjl4clpkY2poNE42QjJySFZiczc0UlRkSXhSODJaTU03QXNjNGVNYkU5M1ZlY0lDcnZYV2VwVzduNlAyL1NEUVdvaXVOVmxzS1BzWVZEMTZ1blB3NWFRTFIvUXNIQWNmd2ZRUW9VLytyRlhCVS9ySjIiLCJtYWMiOiJjMDg0MDc5MTYxY2IzNjY1NGZiMDc1YTYzMzlmZDdlMzQ1ZGRiMTRlYWFkZDRjNzMyNWRjZDY0ZDM0ZjgwMmM4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1252041577\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-195836245 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-195836245\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1580946511 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 01:15:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkpOMzJoK2pNcXAzVi9yUHVFbXVYYVE9PSIsInZhbHVlIjoieGxkc2ltb1d0WjI3dkN6THA2S1ljYjhzYWZiU0tmM1dMaCs3RTh5dUJGelUvN0NrbW1yWVBoMjBWTVYxS1RaM3VJdjlmSExYMDF6TGJIeGpSQXNVUlhwNmF6eE82ODZnU2p6WjZRa25vdzhXSTNyZkp5bFJtc3FFWlFxeUdkb1h1bmlTZzdRbTdwS2Qza3pJUXM1Szcxb2ZXUnhnZjkyOW9PYWh6S0tvM2VnNTRZY1lETjMrb1hYTllJaWhNREI2SHl3RncwdlZQMVlLYzE0MFEyZ1JtcnNvdUVKS0FWenkzZ0xTUWRadElXdjE5U3Nmbk1RNEhUVnRrL2NWWXg0Um9JYWl5Q3lSaksvZkRqbXZLRjNqV2NmS1NqeDVxckVrQUxOOWhsQmJ4QVk2ZGN1ZGVMU0tFV2ZlVm5UZGM4Vm5UcnlNaFB5YUVHc0UvVzdGQzROOXVFVXprcWQzM20yQ0JLN1UzVENySVBLcTRNV3NxeFB4QU13c1RqcUsxT09vL3JzV1pucWY1SGw1NVNNdVM0MjJEMWh5U1Z6Smw1dnJEdS9SYXFhVjh2MEpoWDhNbG5ZK2dHUldWa3VlL2lXOEFKNlpZQjlSUlZsRG1tenlHdnd3NlFVV2ozWjNpR1h6eE4rcVR2b1VCdVRWTnN5NFlZZ2paNnpRV3ZtZnVaSGEiLCJtYWMiOiJkOWUyMjZiYjZlMWY4MTFiYTM0NWQyODdmYmY4MDI5MGYxNDBkYjQ5NTQyNTg1ZjUwMzAxYWY3MjM5NjgyYWRlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:15:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Iko2QytCbDNMeStFbTVaWDNuSmRRbWc9PSIsInZhbHVlIjoiSUdKMVNqd1drcjBodFh6TGlxYms2ZUxBdTZMODlMeCs1VWoyU2NTQ3ExUjBzYk5URzY4Y3djOXVXZGpCNjM3dHplV1pYU2lsei9EZkVOMU9rc0h1QjdzRFZPb0FRUG1NUVEySkVXeGdXVFRvOTNSbnUzSjVGVS9RM20raWtxQUVEL3ZOYkZTY0Q4OFhCL2psdFpNMTVRQVVPeTkrdkc2TlA1M0I4U2dFUFJJYjFSYzc0NWw1ek0yWndxVVRFUU1RQWVvYkZRbEJjVmdqT0VLM2U4WCtMM2lZWVJJVE1wZUx6SzVVODBBWE4xKzFDY25RZDBSVHM4eUlHcGkzTzBRbmcxdjhZKzYwQTZmYzJPcUs1OTBadStkVDNCQW5qNitQYkpRLzViNCs0WjRpRHdoTEMwVjZRaHdna2ZNb2s1bTJGZkFUV2p1UUFWUDZTZU4wYnNtM3JQVzVlM0puM1VVMXhGMWxoZ3BUNE5xRHZERkNiRmFKSnJZZDM5NjJVWkh4SFJsVXpwTFk2Q1NUbUZPTldrckJBMFFxNWZKQ09UcHluVmFkVVdaVmUvVzV0ME5oL0kxcjJEQW9zcEM2eERxV0F0VDNqY3k1RDhNNmZMNTZoMFZhY1dJb2lQYVNkdmx0bFdtWEVWQlUzUVpQWDEzMlNFRTBqR0dTUlM0ZDc5R2siLCJtYWMiOiJiYTAxMDlhYzg0MDhjNGJlZmNiMTZjMTZhZWUzMmRmNjdmOWJhNGRkMjgzMTlmNjRkMjUwMTZkYzZkYzkwNmYzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:15:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkpOMzJoK2pNcXAzVi9yUHVFbXVYYVE9PSIsInZhbHVlIjoieGxkc2ltb1d0WjI3dkN6THA2S1ljYjhzYWZiU0tmM1dMaCs3RTh5dUJGelUvN0NrbW1yWVBoMjBWTVYxS1RaM3VJdjlmSExYMDF6TGJIeGpSQXNVUlhwNmF6eE82ODZnU2p6WjZRa25vdzhXSTNyZkp5bFJtc3FFWlFxeUdkb1h1bmlTZzdRbTdwS2Qza3pJUXM1Szcxb2ZXUnhnZjkyOW9PYWh6S0tvM2VnNTRZY1lETjMrb1hYTllJaWhNREI2SHl3RncwdlZQMVlLYzE0MFEyZ1JtcnNvdUVKS0FWenkzZ0xTUWRadElXdjE5U3Nmbk1RNEhUVnRrL2NWWXg0Um9JYWl5Q3lSaksvZkRqbXZLRjNqV2NmS1NqeDVxckVrQUxOOWhsQmJ4QVk2ZGN1ZGVMU0tFV2ZlVm5UZGM4Vm5UcnlNaFB5YUVHc0UvVzdGQzROOXVFVXprcWQzM20yQ0JLN1UzVENySVBLcTRNV3NxeFB4QU13c1RqcUsxT09vL3JzV1pucWY1SGw1NVNNdVM0MjJEMWh5U1Z6Smw1dnJEdS9SYXFhVjh2MEpoWDhNbG5ZK2dHUldWa3VlL2lXOEFKNlpZQjlSUlZsRG1tenlHdnd3NlFVV2ozWjNpR1h6eE4rcVR2b1VCdVRWTnN5NFlZZ2paNnpRV3ZtZnVaSGEiLCJtYWMiOiJkOWUyMjZiYjZlMWY4MTFiYTM0NWQyODdmYmY4MDI5MGYxNDBkYjQ5NTQyNTg1ZjUwMzAxYWY3MjM5NjgyYWRlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:15:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Iko2QytCbDNMeStFbTVaWDNuSmRRbWc9PSIsInZhbHVlIjoiSUdKMVNqd1drcjBodFh6TGlxYms2ZUxBdTZMODlMeCs1VWoyU2NTQ3ExUjBzYk5URzY4Y3djOXVXZGpCNjM3dHplV1pYU2lsei9EZkVOMU9rc0h1QjdzRFZPb0FRUG1NUVEySkVXeGdXVFRvOTNSbnUzSjVGVS9RM20raWtxQUVEL3ZOYkZTY0Q4OFhCL2psdFpNMTVRQVVPeTkrdkc2TlA1M0I4U2dFUFJJYjFSYzc0NWw1ek0yWndxVVRFUU1RQWVvYkZRbEJjVmdqT0VLM2U4WCtMM2lZWVJJVE1wZUx6SzVVODBBWE4xKzFDY25RZDBSVHM4eUlHcGkzTzBRbmcxdjhZKzYwQTZmYzJPcUs1OTBadStkVDNCQW5qNitQYkpRLzViNCs0WjRpRHdoTEMwVjZRaHdna2ZNb2s1bTJGZkFUV2p1UUFWUDZTZU4wYnNtM3JQVzVlM0puM1VVMXhGMWxoZ3BUNE5xRHZERkNiRmFKSnJZZDM5NjJVWkh4SFJsVXpwTFk2Q1NUbUZPTldrckJBMFFxNWZKQ09UcHluVmFkVVdaVmUvVzV0ME5oL0kxcjJEQW9zcEM2eERxV0F0VDNqY3k1RDhNNmZMNTZoMFZhY1dJb2lQYVNkdmx0bFdtWEVWQlUzUVpQWDEzMlNFRTBqR0dTUlM0ZDc5R2siLCJtYWMiOiJiYTAxMDlhYzg0MDhjNGJlZmNiMTZjMTZhZWUzMmRmNjdmOWJhNGRkMjgzMTlmNjRkMjUwMTZkYzZkYzkwNmYzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:15:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1580946511\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1546541353 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1546541353\", {\"maxDepth\":0})</script>\n"}}