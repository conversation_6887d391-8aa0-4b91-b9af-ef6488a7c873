{"__meta": {"id": "X88395575b110d2981d564867c567e745", "datetime": "2025-06-08 01:06:35", "utime": **********.485633, "method": "GET", "uri": "/financial-operations/product-analytics/receipt-orders?warehouse_id=&category_id=&date_from=2025-06-01&date_to=2025-06-30", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749344794.813168, "end": **********.485656, "duration": 0.6724879741668701, "duration_str": "672ms", "measures": [{"label": "Booting", "start": 1749344794.813168, "relative_start": 0, "end": **********.357806, "relative_end": **********.357806, "duration": 0.54463791847229, "duration_str": "545ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.357821, "relative_start": 0.5446529388427734, "end": **********.485659, "relative_end": 2.86102294921875e-06, "duration": 0.1278378963470459, "duration_str": "128ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46079392, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/product-analytics/receipt-orders", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductAnalyticsController@getReceiptOrdersAnalysis", "namespace": null, "prefix": "", "where": [], "as": "financial.product.analytics.receipt-orders", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=589\" onclick=\"\">app/Http/Controllers/ProductAnalyticsController.php:589-660</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.04567, "accumulated_duration_str": "45.67ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.407439, "duration": 0.01783, "duration_str": "17.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 39.041}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4370718, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 39.041, "width_percent": 1.292}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 599}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.441456, "duration": 0.00508, "duration_str": "5.08ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:599", "source": "app/Http/Controllers/ProductAnalyticsController.php:599", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=599", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "599"}, "connection": "ty", "start_percent": 40.333, "width_percent": 11.123}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 600}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.449745, "duration": 0.00442, "duration_str": "4.42ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:600", "source": "app/Http/Controllers/ProductAnalyticsController.php:600", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=600", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "600"}, "connection": "ty", "start_percent": 51.456, "width_percent": 9.678}, {"sql": "select `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`purchase_price`, `psc`.`name` as `category_name`, `psu`.`name` as `unit_name`, `ro`.`order_type`, SUM(rop.quantity) as total_received, SUM(rop.total_cost) as total_cost, AVG(rop.unit_cost) as avg_unit_cost, COUNT(DISTINCT ro.id) as receipt_count from `receipt_order_products` as `rop` inner join `receipt_orders` as `ro` on `rop`.`receipt_order_id` = `ro`.`id` inner join `product_services` as `ps` on `rop`.`product_id` = `ps`.`id` left join `product_service_categories` as `psc` on `ps`.`category_id` = `psc`.`id` left join `product_service_units` as `psu` on `ps`.`unit_id` = `psu`.`id` where `ps`.`created_by` = 15 and `ro`.`invoice_date` between '2025-06-01' and '2025-06-30' group by `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`purchase_price`, `psc`.`name`, `psu`.`name`, `ro`.`order_type` order by `total_received` desc limit 20", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 639}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.458129, "duration": 0.01775, "duration_str": "17.75ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:639", "source": "app/Http/Controllers/ProductAnalyticsController.php:639", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=639", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "639"}, "connection": "ty", "start_percent": 61.134, "width_percent": 38.866}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/product-analytics/receipt-orders", "status_code": "<pre class=sf-dump id=sf-dump-1548716108 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1548716108\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1190571143 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1190571143\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1296508249 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1296508249\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-96106519 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749344755567%7C50%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imd0dWd3TXUyMWdZUUlwZVdMUGtQNWc9PSIsInZhbHVlIjoiUThaL0wxdzF2VXNmU29zem13UTM2ZE1XUHNKZE9yeWVIaUcvejVYRy9zckJzNzJEbHN6alFNNGZFQlZrZitIUUZqT2xhcTBLWnlFSHc5RXNNN0h6enFwU2Y3Mi9iV2tiNjVScGo2S2dQR3d0dllXei9oL1VObDhUSXNDdS80MXdySjN5Z0ZMaXcxT3RjeUd2UE5xYnVkazB5R1EvWm9zd1dSdTRZZDhjcVBrdm5FQjRQMnlsOEdGSXNZd1NvcU5UZnBabVF0dTZyUW1ycW1XbUNZOTl0MFdqTXM0TktqcHQ3bC9FNG5Ic2pSS3hnRmZWOXhacTJjL1BMdkxWOGZSZStGSzJ6UmdnZFZRUkNaam9pUlVOalBxLzRUUjZIQTNnRFJkcUFIS1d2OGxiK1hHRno4M0ZCVkxPRE5rQVIvclVrMFdrRjlqaWplYjEzSGlmTzhxYUUxZm9XNEpMZDRBbDN3UGtVbjBWL0N6bWxoblZkZHFqS3JTSFhnZFIwQ2JsWFRyaHh0bHJVcGtSeTFuYU8xd2l0SkVpS0xiRHdmeUIzR2w5c2tsdjc0dm01ckZiQUdGcXNBQmNOOWg2b3A4M25FZ1RtQlBrNlJxUzFmcXJueDNtUCtPQ2VhSm9LTXY1SFpibWN6emFsa3h3ME1Vd2lrRDlMeUxpNEJFQ0FFY1EiLCJtYWMiOiJhNTcwOTZmODU1ZjYwOWIzOTJmNGUxY2JjYzhjYzkzNGFiMTY3ZGQ0MTUxY2ZlZGY1MjYzMjg4M2MyYjc2ZGJjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InF0ZWFYaVpia3RRbmVpVEtGSmFDWXc9PSIsInZhbHVlIjoiNkVVRXk5MHVJVWIwc01hNG8rNGxDRkFJMFJwQjJpL0xHZzhLeUpBUGtHeGg1TW5ZNkR4R1A0VWFJZThydTFKSVpqaFE0b1pWT1JWdUNsV1YybkRLMTVKZ0JXRDBYaE9yaUZlVlNuM3ZMWmJnbEswWjJyZldUNXlQdTV1elc5T1BURmtpTVYxYmo5dHNIK2dWMHNyZFNQZDhMdzluMFk1SE1TVmFheGlYL2M5ZUJpVVZ0RTBBT25FWFlGVzFpUEhpOHJsN1d4L0JWaXhLRXV0U0dqOFJTVnp0eGF6QUJDTmorbzdlWFhsdGVuK3o2TlVicHZFc3VGRjlVQzhKTm8waitKM2M1SmZrY3ROL3lNRjhLVDNpSFlmSWtTWHUycTNWTk5Sd2QzQjNRaXMxQnZ0UU9COC9VQ0lENjNyczd2M3JWemJ6U2pLK1c1VUlqSm9RRnd1d2I0ODY1a0NucndjYVpUMElpdnYyVlJ0d3lpY1ZXeVhHQTQ4K3RTSzBURFVmSHI4RUVwdThZQzdSTENQcGw4MkNJMGNlakxEUEFoU0dCTWNJZ1piL2NmR1hESlZJKzFxeE9yK3BqRlhCZ3JmQ3BoZVNJQnFKcjI2bFNrSmhHL01DZk5JSldTYWE5SXdNSldWcGZFckpBMjFPMmtWWDJuNDI1ZjcwclM0ZXQzYXAiLCJtYWMiOiJjMGY2ZjIzZTBmMjJmMmRlMjNlYmU1NjBkMzA0ZWQyMTA1NTQzNmQ2OGEzMjEzMWEwZmJmZDIwZTJiZTgxNDllIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-96106519\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1804833160 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1804833160\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 01:06:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ink0ZThIVkJwckMramxubSsxYi9rR0E9PSIsInZhbHVlIjoiL0h0aEtacFFQd3BuY1U3QlRxNHJMN05VOHVxcmliZXZqTHdMSWpnVFYwdWQvMkpWd2Z2T0xzTzRzYXROdnlCODBxQW9LU0RWUFEyeHpXQVE4c0w0Wm43eUZIdjRhQ00zOTk1clc5UzRjQUxCSFVnaksyQmlaaSt2ZFRGL1BHUlB4Z25JcHZlNzBNNWM4U0M2REtzVGtIcDN3aW5Nd0FoZGZyU05ubm5mSFhGdGY0dlV1WHl4RmczRmZacXJrN0crdUZadkFUc0ErL3Ntc3VTQXRmWWFya0RrRVZpTGF3ZVJkN0FTNWdIMFg0SmQwbWdONVBYYWt0bU5IUEpneFY1R2R6K2k5TlBKcDkrVnZKVzRaU1ZCY3lpVk1aazZ2d0FDUmxZaUV5U21XOThzeWFyNmp3MFQ5dkRUeXdBOVJwbWplcllMeVEvVDJ2SFJxTm5LY1Z2V29qcVV1YXYvMGx2dUE4UkdIS0JHbWJBbmtkc1FVWi9ENkhwdytGQWdaNWNYcUlhcjIrbXRXUFBpckxsc3ZjRFlqOFFCN2MwdXY5b1JVZlcvK0syWTlxemdPOXRyRlRjMWsvMjZLak4yZTNCT0xMM2E1UXZCNDlPcjc2MWt1THdnVXZ4MEhEWXJCdndZellLTDlKMFFCc20wd2VNbngrNFZtMjBSOXRmTUkyeHciLCJtYWMiOiJkNTNlNTVjMzJiZDFmZWI4NWY1MDUwMjIzYWU4Y2EwODZmMWM2ZGM4YTBmZDdlMzlmYTQ0OWM4NTI5ZDI2YjVjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:06:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IndYbHZLS3g5SW5VMkZDdy9ZdWpqMmc9PSIsInZhbHVlIjoiTlV2a2xZY3FpUnlmZXgxRXJ2VStkTlhmbFd1V1dOQmtZZWMvVnpZUW1SZzdJTEh4VVIvMVJaUUwyZ1M4Vm41M3VJNGNvdXdRR1FudS9aSXFmNXozbzRIamN2N1hSRUZwYXE3WDhaNXRWS21HZnRvT1ZjelJoM0xIM0E3NGhENzNFc1VueVZCbk1GM3dGc3F4RnlOUnhONjhTdjA5TVdIUmdqbndJdWJQdC9NSXY1VnhFSjZCTjN1SnVGRFN3VGVSVE9XV3k5ZGl0V29SMEN5VVdKclgwR0ZaNzU4VFk3RFdiUUZlMGJyNVpWOGNZVjJPNGxqRzZDd21uV1NHK1lucTcyUnhaL0UvZ0toQXhnWXFURmQ3N1F6MlduMEthaU5aR3RRVGlyWENNblVtUlUydko1eEl3Y2IvSzVQQmdQKzZGL1VzN3FXZ29ZbEVGbEhVeE41Z1UxaVpFTzNuN1hmM3YyUjcxNzhHTVB3Nm1UYkRaY2NyQ1J2SU9YNDI4VkRnM3o2eVJ1ZXhidUQ3QkRXaEpTN1ROS3lNenhYbUx5ODVjU0drYmw0Ui92QnhsMW90STBMWElOZW1TL29FZTN0YWxPa3JlU3VSMm1SWVAyQW80Y1NIemdDL0ZtSGhiOFVsOWpxNm9VTi9UMXh1b3BJektqakdTOU8xcy85ZkF6UjYiLCJtYWMiOiJjYjJmMjQ2MGI2ZGNlZWY5MTM2N2M2N2YzYTc1NjgzYzM5NzkzMzgwNjY1ZjA2NTYwOGEwZjAyNTg0NDBiMTk5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:06:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ink0ZThIVkJwckMramxubSsxYi9rR0E9PSIsInZhbHVlIjoiL0h0aEtacFFQd3BuY1U3QlRxNHJMN05VOHVxcmliZXZqTHdMSWpnVFYwdWQvMkpWd2Z2T0xzTzRzYXROdnlCODBxQW9LU0RWUFEyeHpXQVE4c0w0Wm43eUZIdjRhQ00zOTk1clc5UzRjQUxCSFVnaksyQmlaaSt2ZFRGL1BHUlB4Z25JcHZlNzBNNWM4U0M2REtzVGtIcDN3aW5Nd0FoZGZyU05ubm5mSFhGdGY0dlV1WHl4RmczRmZacXJrN0crdUZadkFUc0ErL3Ntc3VTQXRmWWFya0RrRVZpTGF3ZVJkN0FTNWdIMFg0SmQwbWdONVBYYWt0bU5IUEpneFY1R2R6K2k5TlBKcDkrVnZKVzRaU1ZCY3lpVk1aazZ2d0FDUmxZaUV5U21XOThzeWFyNmp3MFQ5dkRUeXdBOVJwbWplcllMeVEvVDJ2SFJxTm5LY1Z2V29qcVV1YXYvMGx2dUE4UkdIS0JHbWJBbmtkc1FVWi9ENkhwdytGQWdaNWNYcUlhcjIrbXRXUFBpckxsc3ZjRFlqOFFCN2MwdXY5b1JVZlcvK0syWTlxemdPOXRyRlRjMWsvMjZLak4yZTNCT0xMM2E1UXZCNDlPcjc2MWt1THdnVXZ4MEhEWXJCdndZellLTDlKMFFCc20wd2VNbngrNFZtMjBSOXRmTUkyeHciLCJtYWMiOiJkNTNlNTVjMzJiZDFmZWI4NWY1MDUwMjIzYWU4Y2EwODZmMWM2ZGM4YTBmZDdlMzlmYTQ0OWM4NTI5ZDI2YjVjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:06:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IndYbHZLS3g5SW5VMkZDdy9ZdWpqMmc9PSIsInZhbHVlIjoiTlV2a2xZY3FpUnlmZXgxRXJ2VStkTlhmbFd1V1dOQmtZZWMvVnpZUW1SZzdJTEh4VVIvMVJaUUwyZ1M4Vm41M3VJNGNvdXdRR1FudS9aSXFmNXozbzRIamN2N1hSRUZwYXE3WDhaNXRWS21HZnRvT1ZjelJoM0xIM0E3NGhENzNFc1VueVZCbk1GM3dGc3F4RnlOUnhONjhTdjA5TVdIUmdqbndJdWJQdC9NSXY1VnhFSjZCTjN1SnVGRFN3VGVSVE9XV3k5ZGl0V29SMEN5VVdKclgwR0ZaNzU4VFk3RFdiUUZlMGJyNVpWOGNZVjJPNGxqRzZDd21uV1NHK1lucTcyUnhaL0UvZ0toQXhnWXFURmQ3N1F6MlduMEthaU5aR3RRVGlyWENNblVtUlUydko1eEl3Y2IvSzVQQmdQKzZGL1VzN3FXZ29ZbEVGbEhVeE41Z1UxaVpFTzNuN1hmM3YyUjcxNzhHTVB3Nm1UYkRaY2NyQ1J2SU9YNDI4VkRnM3o2eVJ1ZXhidUQ3QkRXaEpTN1ROS3lNenhYbUx5ODVjU0drYmw0Ui92QnhsMW90STBMWElOZW1TL29FZTN0YWxPa3JlU3VSMm1SWVAyQW80Y1NIemdDL0ZtSGhiOFVsOWpxNm9VTi9UMXh1b3BJektqakdTOU8xcy85ZkF6UjYiLCJtYWMiOiJjYjJmMjQ2MGI2ZGNlZWY5MTM2N2M2N2YzYTc1NjgzYzM5NzkzMzgwNjY1ZjA2NTYwOGEwZjAyNTg0NDBiMTk5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:06:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-425623048 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-425623048\", {\"maxDepth\":0})</script>\n"}}