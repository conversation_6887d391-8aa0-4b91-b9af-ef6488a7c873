{"__meta": {"id": "X1f6e745582fd313ec7b6a4caaf631774", "datetime": "2025-06-08 01:14:12", "utime": **********.745283, "method": "GET", "uri": "/customer/check/warehouse?customer_id=7&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.148247, "end": **********.745312, "duration": 0.5970649719238281, "duration_str": "597ms", "measures": [{"label": "Booting", "start": **********.148247, "relative_start": 0, "end": **********.641069, "relative_end": **********.641069, "duration": 0.49282193183898926, "duration_str": "493ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.641082, "relative_start": 0.49283504486083984, "end": **********.745316, "relative_end": 4.0531158447265625e-06, "duration": 0.10423398017883301, "duration_str": "104ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45176968, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02862, "accumulated_duration_str": "28.62ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.684313, "duration": 0.02684, "duration_str": "26.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 93.781}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.729252, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 93.781, "width_percent": 2.795}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.735217, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 96.576, "width_percent": 3.424}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-2139580377 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2139580377\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-164010163 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-164010163\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-221326186 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-221326186\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-241146049 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1939 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i31pha%7C1749345246637%7C11%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Inl5MzcxbXJLaFJneGVETnB5eFFTSUE9PSIsInZhbHVlIjoiZU9HczNpbE5lSUp0dU1HSmR3V1ozeGpuTzF6eitJOWo5SHl4dnRPcElSU0Y1OHhjTVlhTVJ1MlhsdGw1R3BuUnMvVTlRaGFvcjNHREplb1g5K3NhRjlVaW9udDUrby82OEI2RFh6dkhqMkNYOUtvcnlCdzlIWEoyTHdiYjE4M0Jvc3p4SUdqd2ZQN25FM3JyOVBiWHZ0eFBvbEtMQW02MjNuV3NkbkZVc3RBVTQxQU5vanRqeUk3WkJ1dWhsb2tORVRXZEJBREs5WWxuc3R4Q0Z3VDVlbGlFaWxhUE1INFpPODFSQUxHWkU1QWExdFh3Nm1ncEFJdlZMMHgyOUs0elkrcDhVTnZDVnJ0WFRFUmZQL09wUWRpN2NGVE1SMkl5Rzc0Q0xDcnpLczZSU0cxR1poQWtLZy9jOWo2UWdWckhXOWppRXZ3UUxrVFRER0wzRGlySFpBUjExQkRhV1Y4cW95WVNmZWhRRjh0aGpCTCtZK0Fha3libWxqOXRKS1BaY2JubkVCVTg2ZnNIZkt0bTM4YzgyTzZ3azJMbjB0Qm5RRmR3aTlBZlJqblhkUXlFdVFqK2o0NloxdllLSmtMdytyZS93ZzRvVVRFVkc5M2g0UW9XaGt2TzRpTHpZNVpOZUFTQ2xGSXdQRWkzSHc2cWZDNHd1OVZ0Rk1HK3VmY1giLCJtYWMiOiI5ZDc3ZWE4ZDcxYTZiMDc5NGI1OGU0MTg3NzFjNzU2YmMxNTYzYTg4ZWE1NjA2YTJjMjc4ZGVjY2E3OTIzYzM3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InNnbzdnenJlZ2dOaDdiTUVuWXhRdXc9PSIsInZhbHVlIjoiR3dpUG9CSlkwMWR3UFpMRFREVTdUMmRnOWVMbWs2T1RYU0RSMTBRYjZ5eHc5K3lDbm92Q3c2c0ZOZlFrbURNZXY4VVVYUDJGd3ppR2h3azlBT2NQK1BjRnppL0FqcWhhMkZOMFVFb3NvNEJJRkJKNjZsY1E3L0syelhwajVlZ3VKUFF5OTNrYmt3N0xOUkZqaG1NcjlzTjV0NGhZUDJiK3Q1bW12TkRtSlBRbnZJenhPcXdsVnczUnRleFVSeE92ejVSTnJUUzdIMVZvNFkvS3JuZ3Y3dWVIOTBhVEhJeFBWWkx6OHpaRmU5aFd5bldvazVyUEdURVRLOVJBeHZnMTJvelFSaDAzSU84T1F1amN4UlZVUWwwSW1pTU9HU0t3N1dtY3JDaE50aE1wNVoxM3dtUW1VWE9GRE1hK1R2NHBsUHlYd01IQUc4UzMxdE1iMUo0QjBsSGpSWWpacEUwWHdlaHZNYWJmdGdUWVJhLytRcWk4TU5XUlc0OGNCYkM3ZStnaXBCTHNFNDdwNExFelA0ZkZZYkZ6TGlBQ2hkMndTMWtoajVZSE9PNGhnQzlML3YwRVVJbXJVQ2F4Qk9BaFhSQURIbFdiZmV1eithTVNQaWo1bFRlZGpWdXBxZm53Z05lelJsZGI3cTQvVGxmTzJZcFJGME1wNndyQXZjQzQiLCJtYWMiOiIyNjQxYjAzYjk5ZjZiZjU5ZjdlMTFiYmZjMjBhNWIyYWE4NGQyNzVlYzg1NTBhN2E3ZDdjZWY2NTAwZWMwNjY4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-241146049\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-516543310 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-516543310\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1934706566 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 01:14:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im1iY0ZES3FwSlk4MS9FOVI1YVF1cXc9PSIsInZhbHVlIjoiaDZ5R2dESDBiVU8wY3d4RldmWEFzdXRUSGJ1UzI3NGlFMGZNKzByZFhQdmFJbTZ3cE0xdnZrVXduZFNvdVhWMUZjaWdFQ3daaWExT1ZCYzZGS0tUQ1Zabk5zOTY2Z0MvaHRMdnErQTNQdVdaUkhGanFwaVBBMWZrUDlITFZqRTVFMnNhUDJVb3AwMStXMThzcFdUTEZlVUYzN3RCcUlQL0JjYSt1V3pOYnVmOWZUR0JrSFZTODZiVHRLN282dmExRWh0YTVyVjJzQ0I1dnJjMVVWVGpoSk5rYmx4WkdlQ1hvcDNnMkREcXE5Z3lWaEliYjB1anVSbGRNK3h6TnlsN0doYmJJMXJyNWwzbUUzNUtha3lGTkw5bmc1QitCQzF6L0RtN1FHM3pWTThvbVRyN051anYzZy9OT0dVWmQ0YVJrSzNya0RhYWU3ZzZXR2dyZC8xalE1ZytkL2tUbERLc3BTUjdXV0VJcW1YL2pBcWdsZUlWb204U1VmcUhZSDZ3ajM3QXlSbVRTbk5aYlNCbmxsR3YzM2l6WkZTR1dYS2t3SkdrWTZ1NEt0enFTOHBZNjlvMVBTVDVuK3dDZXBjN2JqN09MMmxjVDd1dVlhWisrSG4zRFprbVRJeTVGVFF4SExwUGk3MHZSb29WWWlHR1RkQU1vR2srYXBVV3dBYWsiLCJtYWMiOiJjMzc5ZmFlZDYzYTFkMjkyOGQ2Mjk4ODllMjM5ZjRkNWIwNGIzN2NjNmEwNmY4Yzc4M2JkZmUwM2VlMzMzNjE5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:14:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImlDR21uS0xTNS9DZmVCei9kakdVM1E9PSIsInZhbHVlIjoidHZqNldsbEdKNzltWThLeWZ4RTRUaXJjb2lWT1dONEFqV2hiY2dGdEl3VGdVY2kyd0xMSU9YcDdEZWVCaEVST1hld2xBQlBxZ1ZlZzllVGgzMWFFUEZwN2p0S1lGU2tycXZ3V090c0t2QXo2TnhzaURubTFpa21MRVh6RHBnaFBpZEc3QmdBeDByMUU1WnU3aFNTaFhmSGFWU0dKQzhVQjlmQUJJOG9EVWJ0SERGNFBTcVVQSkczbEVQbElEYUFscnZZVk0vT2RpY0N5Z29rNytwbU9BSVhDRWd4UlVlKysybzFadlgrazlKZnQ3QWtvR25NNTNKTFpOTzBZZTF3ejBZYStHTUhEUWlwczQ2VllxMmxpV3ZaUDIrRUE3WEEwbXJyZjNOcDM0ZE9jaE5WOWpaTXliNHU2L3NpSVpWeUQ2dTFxc2VucXJ1ZjEyM2xlbGVtOHRxbXlTSXVBcVVmWmpGZ3NIQ0tvVkJUbEVRMmdVNXp3SGY0R3BjVW55akMzbWhNZnhBMi9iR0xPbnB1QW1DOUdBbm5oVzdHQWRkVXduaFg3eFZCVStrT1N5UzdEVmJwUUlQTkxCSldScVV4aWVKMndEZ3g1WHF1bkhHS1hLdU1xUlU4Z0hwUVJYN0ozRnpnL1NnbWQ0WEFIMGJON3M3M0dwcUEvcDBuaFZBTnYiLCJtYWMiOiI4ZTc0ZGZjY2IzNTdkYWY0Njk2OTA1MjdhMDM0OWIzZmMzMzczODZjOGM0YzgxZWM1NjVkN2M5OTQzNThhNWM5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:14:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im1iY0ZES3FwSlk4MS9FOVI1YVF1cXc9PSIsInZhbHVlIjoiaDZ5R2dESDBiVU8wY3d4RldmWEFzdXRUSGJ1UzI3NGlFMGZNKzByZFhQdmFJbTZ3cE0xdnZrVXduZFNvdVhWMUZjaWdFQ3daaWExT1ZCYzZGS0tUQ1Zabk5zOTY2Z0MvaHRMdnErQTNQdVdaUkhGanFwaVBBMWZrUDlITFZqRTVFMnNhUDJVb3AwMStXMThzcFdUTEZlVUYzN3RCcUlQL0JjYSt1V3pOYnVmOWZUR0JrSFZTODZiVHRLN282dmExRWh0YTVyVjJzQ0I1dnJjMVVWVGpoSk5rYmx4WkdlQ1hvcDNnMkREcXE5Z3lWaEliYjB1anVSbGRNK3h6TnlsN0doYmJJMXJyNWwzbUUzNUtha3lGTkw5bmc1QitCQzF6L0RtN1FHM3pWTThvbVRyN051anYzZy9OT0dVWmQ0YVJrSzNya0RhYWU3ZzZXR2dyZC8xalE1ZytkL2tUbERLc3BTUjdXV0VJcW1YL2pBcWdsZUlWb204U1VmcUhZSDZ3ajM3QXlSbVRTbk5aYlNCbmxsR3YzM2l6WkZTR1dYS2t3SkdrWTZ1NEt0enFTOHBZNjlvMVBTVDVuK3dDZXBjN2JqN09MMmxjVDd1dVlhWisrSG4zRFprbVRJeTVGVFF4SExwUGk3MHZSb29WWWlHR1RkQU1vR2srYXBVV3dBYWsiLCJtYWMiOiJjMzc5ZmFlZDYzYTFkMjkyOGQ2Mjk4ODllMjM5ZjRkNWIwNGIzN2NjNmEwNmY4Yzc4M2JkZmUwM2VlMzMzNjE5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:14:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImlDR21uS0xTNS9DZmVCei9kakdVM1E9PSIsInZhbHVlIjoidHZqNldsbEdKNzltWThLeWZ4RTRUaXJjb2lWT1dONEFqV2hiY2dGdEl3VGdVY2kyd0xMSU9YcDdEZWVCaEVST1hld2xBQlBxZ1ZlZzllVGgzMWFFUEZwN2p0S1lGU2tycXZ3V090c0t2QXo2TnhzaURubTFpa21MRVh6RHBnaFBpZEc3QmdBeDByMUU1WnU3aFNTaFhmSGFWU0dKQzhVQjlmQUJJOG9EVWJ0SERGNFBTcVVQSkczbEVQbElEYUFscnZZVk0vT2RpY0N5Z29rNytwbU9BSVhDRWd4UlVlKysybzFadlgrazlKZnQ3QWtvR25NNTNKTFpOTzBZZTF3ejBZYStHTUhEUWlwczQ2VllxMmxpV3ZaUDIrRUE3WEEwbXJyZjNOcDM0ZE9jaE5WOWpaTXliNHU2L3NpSVpWeUQ2dTFxc2VucXJ1ZjEyM2xlbGVtOHRxbXlTSXVBcVVmWmpGZ3NIQ0tvVkJUbEVRMmdVNXp3SGY0R3BjVW55akMzbWhNZnhBMi9iR0xPbnB1QW1DOUdBbm5oVzdHQWRkVXduaFg3eFZCVStrT1N5UzdEVmJwUUlQTkxCSldScVV4aWVKMndEZ3g1WHF1bkhHS1hLdU1xUlU4Z0hwUVJYN0ozRnpnL1NnbWQ0WEFIMGJON3M3M0dwcUEvcDBuaFZBTnYiLCJtYWMiOiI4ZTc0ZGZjY2IzNTdkYWY0Njk2OTA1MjdhMDM0OWIzZmMzMzczODZjOGM0YzgxZWM1NjVkN2M5OTQzNThhNWM5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:14:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1934706566\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-951407201 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-951407201\", {\"maxDepth\":0})</script>\n"}}