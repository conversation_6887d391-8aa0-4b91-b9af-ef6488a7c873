{"__meta": {"id": "X081495fb7e3ef5e0e5b3c1ee77143523", "datetime": "2025-06-08 01:05:55", "utime": **********.953018, "method": "GET", "uri": "/financial-operations/product-analytics/top-selling?warehouse_id=&category_id=&date_from=2025-06-01&date_to=2025-06-30", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.20126, "end": **********.953046, "duration": 0.7517859935760498, "duration_str": "752ms", "measures": [{"label": "Booting", "start": **********.20126, "relative_start": 0, "end": **********.824017, "relative_end": **********.824017, "duration": 0.6227569580078125, "duration_str": "623ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.824033, "relative_start": 0.6227729320526123, "end": **********.953049, "relative_end": 2.86102294921875e-06, "duration": 0.12901592254638672, "duration_str": "129ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46074152, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/product-analytics/top-selling", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductAnalyticsController@getTopSellingProducts", "namespace": null, "prefix": "", "where": [], "as": "financial.product.analytics.top-selling", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=206\" onclick=\"\">app/Http/Controllers/ProductAnalyticsController.php:206-364</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.026350000000000002, "accumulated_duration_str": "26.35ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.886188, "duration": 0.01611, "duration_str": "16.11ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 61.139}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.918122, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 61.139, "width_percent": 2.429}, {"sql": "select `ps`.`id`, SUM(pp.quantity) as total_sold, SUM(pp.quantity * pp.price) as total_revenue, COUNT(DISTINCT pp.pos_id) as order_count, AVG(pp.price) as avg_selling_price from `product_services` as `ps` inner join `pos_products` as `pp` on `ps`.`id` = `pp`.`product_id` inner join `pos` as `p` on `pp`.`pos_id` = `p`.`id` where `ps`.`created_by` = 15 and `p`.`pos_date` between '2025-06-01' and '2025-06-30' group by `ps`.`id`", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 235}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.923608, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:235", "source": "app/Http/Controllers/ProductAnalyticsController.php:235", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=235", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "235"}, "connection": "ty", "start_percent": 63.567, "width_percent": 5.199}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 240}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.927727, "duration": 0.00584, "duration_str": "5.84ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:240", "source": "app/Http/Controllers/ProductAnalyticsController.php:240", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=240", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "240"}, "connection": "ty", "start_percent": 68.767, "width_percent": 22.163}, {"sql": "select `ps`.`id`, SUM(pp.quantity) as total_sold, SUM(pp.quantity * pp.price) as total_revenue, COUNT(DISTINCT pp.pos_id) as order_count, AVG(pp.price) as avg_selling_price from `product_services` as `ps` inner join `pos_v2_products` as `pp` on `ps`.`id` = `pp`.`product_id` inner join `pos_v2` as `p` on `pp`.`pos_id` = `p`.`id` where `ps`.`created_by` = 15 and `p`.`pos_date` between '2025-06-01' and '2025-06-30' group by `ps`.`id`", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 260}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.93712, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:260", "source": "app/Http/Controllers/ProductAnalyticsController.php:260", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=260", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "260"}, "connection": "ty", "start_percent": 90.93, "width_percent": 3.643}, {"sql": "select `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`purchase_price`, `psc`.`name` as `category_name`, `psu`.`name` as `unit_name`, COALESCE(SUM(wp.quantity), 0) as stock_quantity from `product_services` as `ps` left join `product_service_categories` as `psc` on `ps`.`category_id` = `psc`.`id` left join `product_service_units` as `psu` on `ps`.`unit_id` = `psu`.`id` left join `warehouse_products` as `wp` on `ps`.`id` = `wp`.`product_id` where `ps`.`id` in (3, 5) and `ps`.`created_by` = 15 group by `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`purchase_price`, `psc`.`name`, `psu`.`name`", "type": "query", "params": [], "bindings": ["3", "5", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/ProductAnalyticsController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductAnalyticsController.php", "line": 319}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.941066, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "ProductAnalyticsController.php:319", "source": "app/Http/Controllers/ProductAnalyticsController.php:319", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductAnalyticsController.php&line=319", "ajax": false, "filename": "ProductAnalyticsController.php", "line": "319"}, "connection": "ty", "start_percent": 94.573, "width_percent": 5.427}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/product-analytics/top-selling", "status_code": "<pre class=sf-dump id=sf-dump-1356763452 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1356763452\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-446918978 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>category_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-446918978\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1361620420 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1361620420\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1868432538 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749344713396%7C49%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjV1WmN4ZzdzOU9RU3A5WEp6SVBuTWc9PSIsInZhbHVlIjoiai9mVlpMVnVnMVFlNTVmcGJKZ1RDS043NVlvQWpHb1ZUVlRPbmF5WjdCUGQzMzVSWjRaOXpmOUc3NnFRM1NIcEkyUDBVZUNNTndZbGlZVHJ3ZVk5Ylg1UTdxMzVaQnJLRzc1MDNCaEVub2FBUFQrcEMwWmducllWS3QwSGhRWTdFN3hFZi9GZE1hSTVuOG1hV3F0VWlLRTY4YjNBdXFLdmFRb2NheHRvM1plZWxVemdBU0pkNDAvZlBJMVpQb01semtzVHdJZGZCdkh5dDZMOGd2MnY5QlRORk4yb3hMY1dLYmJINnJ0cjVQNEZud0J5bSsyNnp2SHcybW1sZklCNk96a0tyeDh1MHBCN3hORFBDSFo1VTRvRXBwMy9TYytXY29mZ2RlNzJWTnU1ODExbzQ1QnJiVjRBRFhYLzhsMnRudm9jOUFza2l0Tm51eGMvMllQZ2NXTzhMSkpEa05ha1YrVjZGZzVYUXphaitlSm1vY2YyRDhTQUhUbU04VUdLb3ZRU0FJRG5SN21lKytjMHRDVHZKSDVsMTMwRVBPTGI2ZXBhQ1VxdHNVMCtvc0gyZXVoTm8yaHRPTkc5ZVFJaHdQUXdiRnluWGpCS1JPa0dLdCt3TWs0TDlzNnhmRlhUZktKK3NjbnhpUDN2SzJ4VEpCRUdzYy9zTFRIRWdQak8iLCJtYWMiOiJiYjZmNGEyM2EzYzE5M2QzZTkxZDM1NmE3NmYyZDFmNjVmMjNmM2Y0MDVjYjU3N2M4MTc3OTVkYjU3NTNmMDhmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjdDc0ltUFhMS0tpelE2dTQrMmNhZkE9PSIsInZhbHVlIjoic2F2bzQwR0QySStEem1yRlY5eDBDRXA4eDR1dTRGZ29YZFFXbkd1SzdJeG9IdmlNYVZBSTdnbExLSlR2d003eTZiYWJsanJvRDFGOHV5cWtaWnFIdmsxVzRsLzZRZmRaZm1wVUdpOFYwcVFEakNmZ2JnYWFkdWgxbEtRWEhiUmt1UElEaDIzTjFxcUxlemxPMUlJYmRaWXFrNm5NLzVOZjhHNVhnQ1dra1pWaTdPTm16WTBFNFZuUGRaUXlwTW0wU3Eza05QeDhiUVpvZ0xLYVdPU0hYRTYxQ3R1YVpxa1dJN2dtbHpvbEY3VGQ3Vmk1SkcxYXJHc3dQRzhrRmtZZDRCNnZFb1lBWmhQZ29JNkpzTjI2Q1Z4dUFYYTZsVGxGcVgya0lBTEluL2hMd1AvRVAvVjJEejJ4a2N3Z0hKSUMreERKMCsvMk5sZktCbStTUHhuMEszd2creEdHa0JuT1ZHN2NSUHlhWEFGWXNjRXlSSE1ldzVNdEdWSTlqZ0VBanBTK3dweCtic0VWK1ZKeTdlaitFcUdLVWE3M3pITmVmbmdLK2JBV1dFdnQ1V1NyQ2pvMXh4UEJnYXdEbzh4cDdlNEt0cm5JMEZpVGJXWlVsa2JubkZiSEtCV2dMWElYQ2tsTm9MQmJPaE5MNWw4aXpza3lLTG91bnJZODhldGsiLCJtYWMiOiI1ODUwZTQxYTQ3MzUxMGY5MWExNzc0ZmEyNTBlYTgzZjJmNmQ0OWNlMDQxMjhkYTEyYmE2Y2IyODgyZTE1NGRmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1868432538\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2065512424 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2065512424\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1251654266 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 01:05:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjlCQzRxWHVUa0ZRbnBaNTBXNGJQYlE9PSIsInZhbHVlIjoiQXMwL0JEU3lJVFFXYklqQzlsVmthTnNZU0JSaDhGa3ZHWnFzeHAzMk4ycDc3U2Z5c0djdWlzenRyVFl2UGo2K2NVVDEybDRWdExFSVNUSU9yNE00SnhhdE4wbnhGeGJEN0dzNjdHWENjcTdYcDcxdmdNTlpjcTNaTWU3MVpUbzdEcUZkK2V4U1B0NC9wYWJvd0ZoV2lXTkd1dXYxdGEwRjJheGQ1Ukx0ZURmRFFGbnVuOUZKaTRHaXBQTjV0aG42OTZKS04wZkprL0ZvZHNOTTdhNzJUN1c5NUw2a09JR2V1U0F6dlRpOEt0Y0pkZVl5b05rejUzZ3N1c2txNDh6NndKaDYraUZNbVNpM0MzOEQzVFYvNlZzelBlUTFiakdycDcxS05UZTdYVTN0VlhOa09IOUM5d3p5TjJnZENZTHVZdTVBc3QrQ25iRW4ySTVmaFJNY1Z4K3NUeGg3MHZoQVpyNWF3ZjQ5YS91V1djaE43VGtxTE42cVdtWE9xd1lVZzRGNGRVMU1XK0lJTUxqVllwalkyQjBzYlhmNThWd3NPaXhjcDBPQkRLQ0ptNk82V2F3VzJKemR4MDduaUcwdWJiNFBhbjgrQzRaNGlNMk56aUVHc3VQdnVZOFBPMFhaSUxvUFVLNndSc2xPVE9EVVBBNW5keklZVldadU8yWEsiLCJtYWMiOiI1NzE4ZGYzZTY1NTQ3ZDc2M2NlNWQxN2UxN2Y3Y2FiZGI5NTM2ODg4YWQwMzg4OGYzODc0ZjEzYTY4NzI5MmUwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:05:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ikh2d2hWeThxMCtKTVltZ0NVbDZ1eFE9PSIsInZhbHVlIjoiSUxWS2N3VGpSVGRiVW9VTU05OFJNazY4eHRTNXhjbGh3cTBSVmx3WnoxRTV6TDMzb0RQcURSNTZxdE94NG1tRWYvakRPZ3pFSUpGaG5BZStHRFdSSlpaTDdVY0Z5blJPVWlLd3ZOUGJZTmtDUUthN0NGSFErUDFwQTFaL1FUcWN3TUN5TEF5THN1RVpMZkd0MEdNdEtrNnZLR0w5VWdlaEVrK0s1MTg0YWxMNXN5TG9aYlNIbVkxZnViK3FyTnNHTzh5bjByNGt4MlpVK0RrUmFVbGk3NXMyMnc0TnVtbW1jNUZmRmhQZDFEWEVweGg4RkRNVlZQd1c1dDJoNjZIbGM5dERPNXJHZjIrU3BqditxZlBHcjRKeDBERk8rOEF3amVvWDc0L0Y5WE05enJwc3NvY0lGZkhSTythcjNHV0pUeWZSVytOOEMvcXBNYzVJN1Vmd0kyT0VGRzdpNm5FQ3NBZlBudTBKWXVxOWxxekkwbnhYbFNQL3NudmM3QlZ0UWprMnZwWUJJcXFFcFVKR2xlRmg3MmY0dmFiaWkvMmJndDgxMkNHc2g3MXEwTWcxTWc2WldFNXJHMTBDdkZXbnZ3TmRYdVpUd2pETU5wRVVNaXFUZE13alJOdTBtYjFQZWh6ek5FVUV4WEFXQ0EwYmdxRE9pWHZ1V1RONENZbGYiLCJtYWMiOiJjYmU5YzFkODVkZmZhOTgyZjVhNDcwYzVjMmQ0MzEyYzhmZWFlMDQ1YWMxY2FiMjMzMDM3ZWVhYzFiZGY2YzhkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:05:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjlCQzRxWHVUa0ZRbnBaNTBXNGJQYlE9PSIsInZhbHVlIjoiQXMwL0JEU3lJVFFXYklqQzlsVmthTnNZU0JSaDhGa3ZHWnFzeHAzMk4ycDc3U2Z5c0djdWlzenRyVFl2UGo2K2NVVDEybDRWdExFSVNUSU9yNE00SnhhdE4wbnhGeGJEN0dzNjdHWENjcTdYcDcxdmdNTlpjcTNaTWU3MVpUbzdEcUZkK2V4U1B0NC9wYWJvd0ZoV2lXTkd1dXYxdGEwRjJheGQ1Ukx0ZURmRFFGbnVuOUZKaTRHaXBQTjV0aG42OTZKS04wZkprL0ZvZHNOTTdhNzJUN1c5NUw2a09JR2V1U0F6dlRpOEt0Y0pkZVl5b05rejUzZ3N1c2txNDh6NndKaDYraUZNbVNpM0MzOEQzVFYvNlZzelBlUTFiakdycDcxS05UZTdYVTN0VlhOa09IOUM5d3p5TjJnZENZTHVZdTVBc3QrQ25iRW4ySTVmaFJNY1Z4K3NUeGg3MHZoQVpyNWF3ZjQ5YS91V1djaE43VGtxTE42cVdtWE9xd1lVZzRGNGRVMU1XK0lJTUxqVllwalkyQjBzYlhmNThWd3NPaXhjcDBPQkRLQ0ptNk82V2F3VzJKemR4MDduaUcwdWJiNFBhbjgrQzRaNGlNMk56aUVHc3VQdnVZOFBPMFhaSUxvUFVLNndSc2xPVE9EVVBBNW5keklZVldadU8yWEsiLCJtYWMiOiI1NzE4ZGYzZTY1NTQ3ZDc2M2NlNWQxN2UxN2Y3Y2FiZGI5NTM2ODg4YWQwMzg4OGYzODc0ZjEzYTY4NzI5MmUwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:05:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ikh2d2hWeThxMCtKTVltZ0NVbDZ1eFE9PSIsInZhbHVlIjoiSUxWS2N3VGpSVGRiVW9VTU05OFJNazY4eHRTNXhjbGh3cTBSVmx3WnoxRTV6TDMzb0RQcURSNTZxdE94NG1tRWYvakRPZ3pFSUpGaG5BZStHRFdSSlpaTDdVY0Z5blJPVWlLd3ZOUGJZTmtDUUthN0NGSFErUDFwQTFaL1FUcWN3TUN5TEF5THN1RVpMZkd0MEdNdEtrNnZLR0w5VWdlaEVrK0s1MTg0YWxMNXN5TG9aYlNIbVkxZnViK3FyTnNHTzh5bjByNGt4MlpVK0RrUmFVbGk3NXMyMnc0TnVtbW1jNUZmRmhQZDFEWEVweGg4RkRNVlZQd1c1dDJoNjZIbGM5dERPNXJHZjIrU3BqditxZlBHcjRKeDBERk8rOEF3amVvWDc0L0Y5WE05enJwc3NvY0lGZkhSTythcjNHV0pUeWZSVytOOEMvcXBNYzVJN1Vmd0kyT0VGRzdpNm5FQ3NBZlBudTBKWXVxOWxxekkwbnhYbFNQL3NudmM3QlZ0UWprMnZwWUJJcXFFcFVKR2xlRmg3MmY0dmFiaWkvMmJndDgxMkNHc2g3MXEwTWcxTWc2WldFNXJHMTBDdkZXbnZ3TmRYdVpUd2pETU5wRVVNaXFUZE13alJOdTBtYjFQZWh6ek5FVUV4WEFXQ0EwYmdxRE9pWHZ1V1RONENZbGYiLCJtYWMiOiJjYmU5YzFkODVkZmZhOTgyZjVhNDcwYzVjMmQ0MzEyYzhmZWFlMDQ1YWMxY2FiMjMzMDM3ZWVhYzFiZGY2YzhkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:05:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1251654266\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-222763728 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-222763728\", {\"maxDepth\":0})</script>\n"}}