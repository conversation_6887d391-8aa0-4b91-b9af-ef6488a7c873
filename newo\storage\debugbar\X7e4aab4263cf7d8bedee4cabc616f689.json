{"__meta": {"id": "X7e4aab4263cf7d8bedee4cabc616f689", "datetime": "2025-06-08 00:30:06", "utime": **********.908285, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749342605.77494, "end": **********.908314, "duration": 1.1333739757537842, "duration_str": "1.13s", "measures": [{"label": "Booting", "start": 1749342605.77494, "relative_start": 0, "end": **********.751225, "relative_end": **********.751225, "duration": 0.9762849807739258, "duration_str": "976ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.75125, "relative_start": 0.9763100147247314, "end": **********.908318, "relative_end": 4.0531158447265625e-06, "duration": 0.15706801414489746, "duration_str": "157ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45269856, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1671\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1671-1681</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00641, "accumulated_duration_str": "6.41ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8519402, "duration": 0.00521, "duration_str": "5.21ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 81.279}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.883697, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 81.279, "width_percent": 18.721}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-477798845 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-477798845\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-655194345 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-655194345\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1200823230 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1200823230\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1186109897 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1834 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749342593005%7C4%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InZBcTVZaCtQaTEvempJdDgxWHJZZmc9PSIsInZhbHVlIjoiandWSzFsRUVMZGEyWU93MUU5L1NBclc0aEx2eGt4MS82R0pDNWxBNXR3cE9NeDMvNGFZc21PUzI5SWFMRjFSb1VkdEVHeTZUSldVWHVBVHFoUzB0OVBnUVRxb3B4cW9jOVcwOU4va3UyVEZUc29nSmxNRmRYeFM0RGhWVitzVnpEQ1ZjSWk1Qms0NWJPVHlnQlV4d3A5Q09YNTl0UlhQWEloU3lqZVp3UzhDMkJSbmtmaDNIUllzeTFzcHl3L2pWVGFvOU5hYzNiTXZtYzZQMXBzUy9TQlA2YlExOEEzTjdqekhCUnRKaXpaUUJMMzRyaEhoZ0p1QWp1aWcvMkY0dTA1STV1SEhvd2p6Y3RMVk83L2JMcnZCUlhJc3QvZ1JVeDZKMnQ2Sm8xMWNIck1WZlNNc1V1eklvT0V0djFCcm9sVjdaOERqZGlHY3VSMCtZcWVkWUZiZk5CVW5FYVNWUEZVSVhSUTloTWViTDlaaWNkZkhBakpiNDRpUnBWeGx5RjJpVUtoc0hqU2JkTDQ4RGxLRXAyY2xZR25oZVZxR2FwUDdrTlB1TjZyWG9JcEdPWTY1cXg2NmVJT01EaVZkSytmZE1ZTlhLQVZaM0o5cFkrZW1NMEFNcUpwOHJGaERBdXhNcDBXSklWQUZtN0h5SU0xVy9ZTmVaandPUnVBVVEiLCJtYWMiOiJkMTQ2NTExNjEyY2M4NzU0YWUxNzliN2MwNTVlMDZkMzZkMDVmNTY5YzA3NjQ3YjBmMTc1NmJlNmVlOGVlNDgwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlR6azFockM5UVpQdzJ6LzdpS1RwVGc9PSIsInZhbHVlIjoiZEZwa0ptNlpDL2Rpd2NuMzFhSWtmQ3ZQV1lCR28wZlVYMXZsTjFZOWRSbi9Iek5TcmM2dDhQS0tyaXc5VkJONWR0K1h3MWw5QUVSVExlZ1RnOUlnL2FETE44WmMrTXRGanlmd3M4SkgwbmR6NkR3THdMYTI2alE2M2tTeUhrS2hCa28wczlHcm1xdTFYQVlPODhVWnZHTmFwdjdTaE5vZDJZL1hxcDZGc09ubWtZOEsweWNmQjg1dzRHd0VHbi85SmJwL2hEcytOeHNxaUJZU1VFSnpEKzRvSDd1MFB1cktvNXE2R29vZXRld3dveExObjVLTUNRN1FucXdtYVl1TE1yQUZoalhqRWxFOVBsSkJaNXczdnFsSkN5RkxZbFB5MHFuZjNuakNKRVFnM3U2MEQ4U1NRS090LzlyekVSbWt1NEdsa2lnTkYxWEFweFlJUGZUQTlNVFpRcktlSWpzemhpZzNQcHFITngwa3lmdmpNZWVKYWFETzd5UVp2aEhYRFFMVHVmZlZ3TkRoSHlkZkdQbUYzVFFxZ014alZuQ1d1ZUkyVElLSWJQYzhLV0sxWmdpSWhBY1RNU0UzRkd3WDdpSFpGb2RLRVYvQ1kvVGg3VnozMy9yWU1ERDZsSlNKa2FEQk5LK0NLSnllTFJOaXgyOUpvNHpHWThQaVJLUWgiLCJtYWMiOiIzNjQxM2MwYTc5NDUwNDFlOGMxMTdkM2U4Y2FlZjgwMmI4MGEzYTdmOGRhY2QyYTFmYTVlOGVhNmU3NGYxMjQwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1186109897\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-767438021 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-767438021\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1147787679 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:30:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IitpUElCejlvTG9MSzNYSVBCWWlRTlE9PSIsInZhbHVlIjoiclNmaG84OUFKYWJWSC9QcGhINlJvclVrZ2NJclU0OFZIdkhHaXo4SDFLeFRqU2VCUU03bm1yTVJzODlTQ1JpUE1LbkRLSWdia0tTWUJCc1lUS25GYkwvOXUzbCtncDdOV1F0WFJYamxYdGdkdjNwbjQzMW5hZ1F2Vjlva1RuUzh1WFVNMGc4WURlbHNYeVh2c0FVYVJUdFZ6SG9qRVBHa3lSbS9Mb1FLc1hmaWxHMUNvclhJUXMyME4reHlva2ppWWd2VzNmTWlzRkFrMkN1ckVhR1lOYXNvU2lpeHFjNW9pNWFFcnhaVEJLL0Y4dTFjdkJGQ2V3em5jVHZjWWtMZnhlTWF6aktpM2FpVnUwVlQyOGV0ZnRtWVA3RWxaREd1U2VTN01NODJLOHVOUWNBN3NiU1RGZkYzemRQbWh2RDNzR1VWZTkxNnBLbHU1M3VyWksyWEZGN0RSVEc3L1JaTVZCaUUxUFVxQSs1bzR1NEtlUVVFUGMxVkdwV3VHZ1VpMnFTL0JiT3RwaXhxZ3JiUWVFZE9XaXh5VWZYTDZIRVlyUWV6Mk1oVnRBKzFYWWUwL2FRd1hXRFZrVXdkemd2RkFhcjFNSk9ZMGJqVG9XQ3JVbHZVQzNVTkpNUjdoQ3piY1ZBTWl6cGsrdGRUQjJIUThWQVJqcG9IQW9NRU5SSkYiLCJtYWMiOiJlMGY4OThiNzM4OTkxM2E3NzQ3YjZlZDE3YjNmMTE4MjNmYTkxNDYzZmI4YmJkMmE5MzY1MTUwZWJmYzIyOTk2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:30:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik1lVDdoOW5NOE0yRVRPK0ZNOXU3alE9PSIsInZhbHVlIjoiZzIxVnFjVWJQMEg3YWhxOExDTVJzY216Rk5lWG5HSEFUWlZqMzFiUTVPRHJlN2wwVzhpR0FvNXZoaDV6YWFtK2doREdpOFZMdXBMQUpIUWpaYjQwSDgreldHV1F6V3M2UU5FQklvQThuSHlWK0ZKVzE4ajQ3ZHRudGtZRndTUlc3Y01rdlBXS1JqNEpHUllyMCtkUmZaMGRFRVEwaGhQZloyOUJmQWpxRHBpRytZem0ydnhjN3BkODhaM3dBbEV4NlVTM29TcFRmaG1Hakp1My9leEVRQ3hmcG1NcG9mNVQxUVZHalgrazRiSHVtWm5YVEZBa2txTnVXeU4wbkt0ekF1RjNvdzZON3RSQ014TzVmNjNtUzZNZ2pkM2wxUEM0dFphbWVvV3pwTEgzS3BUNHIxZ3Y2OXU1L2dtR3p5M1hpSWh6cXFpVnV1ZHAva2p1MmV3MG1CYXlhM3VTN0FhbzlRRGEwZCt0bklWaktPTlorblhiWEt3TDZqNzVSNkFqT3pKa3F4bkpwMU5seVBuRWMrTFBuMm1jZFpsTG5pV2tYUjVjVS96czdpWVdydCtQUnI5dFVlUXJZSlI0RFpqSThndFpqZ005eXRZRUFoQ09iUXRwSzVlUkFFbmFXZjRmSVdqYmEyczdaaVg2WHh3M3lMK3lwNE1CZnhXK3M4M2MiLCJtYWMiOiJlZjc3NjA0Y2FhNTAzYTJiNTU2MjA0ZDM4YTFmYWNkYzI2OGEzYjY2MDFmZDJlODhlOGMxMWQ1ZDM3YjgyNzFhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:30:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IitpUElCejlvTG9MSzNYSVBCWWlRTlE9PSIsInZhbHVlIjoiclNmaG84OUFKYWJWSC9QcGhINlJvclVrZ2NJclU0OFZIdkhHaXo4SDFLeFRqU2VCUU03bm1yTVJzODlTQ1JpUE1LbkRLSWdia0tTWUJCc1lUS25GYkwvOXUzbCtncDdOV1F0WFJYamxYdGdkdjNwbjQzMW5hZ1F2Vjlva1RuUzh1WFVNMGc4WURlbHNYeVh2c0FVYVJUdFZ6SG9qRVBHa3lSbS9Mb1FLc1hmaWxHMUNvclhJUXMyME4reHlva2ppWWd2VzNmTWlzRkFrMkN1ckVhR1lOYXNvU2lpeHFjNW9pNWFFcnhaVEJLL0Y4dTFjdkJGQ2V3em5jVHZjWWtMZnhlTWF6aktpM2FpVnUwVlQyOGV0ZnRtWVA3RWxaREd1U2VTN01NODJLOHVOUWNBN3NiU1RGZkYzemRQbWh2RDNzR1VWZTkxNnBLbHU1M3VyWksyWEZGN0RSVEc3L1JaTVZCaUUxUFVxQSs1bzR1NEtlUVVFUGMxVkdwV3VHZ1VpMnFTL0JiT3RwaXhxZ3JiUWVFZE9XaXh5VWZYTDZIRVlyUWV6Mk1oVnRBKzFYWWUwL2FRd1hXRFZrVXdkemd2RkFhcjFNSk9ZMGJqVG9XQ3JVbHZVQzNVTkpNUjdoQ3piY1ZBTWl6cGsrdGRUQjJIUThWQVJqcG9IQW9NRU5SSkYiLCJtYWMiOiJlMGY4OThiNzM4OTkxM2E3NzQ3YjZlZDE3YjNmMTE4MjNmYTkxNDYzZmI4YmJkMmE5MzY1MTUwZWJmYzIyOTk2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:30:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik1lVDdoOW5NOE0yRVRPK0ZNOXU3alE9PSIsInZhbHVlIjoiZzIxVnFjVWJQMEg3YWhxOExDTVJzY216Rk5lWG5HSEFUWlZqMzFiUTVPRHJlN2wwVzhpR0FvNXZoaDV6YWFtK2doREdpOFZMdXBMQUpIUWpaYjQwSDgreldHV1F6V3M2UU5FQklvQThuSHlWK0ZKVzE4ajQ3ZHRudGtZRndTUlc3Y01rdlBXS1JqNEpHUllyMCtkUmZaMGRFRVEwaGhQZloyOUJmQWpxRHBpRytZem0ydnhjN3BkODhaM3dBbEV4NlVTM29TcFRmaG1Hakp1My9leEVRQ3hmcG1NcG9mNVQxUVZHalgrazRiSHVtWm5YVEZBa2txTnVXeU4wbkt0ekF1RjNvdzZON3RSQ014TzVmNjNtUzZNZ2pkM2wxUEM0dFphbWVvV3pwTEgzS3BUNHIxZ3Y2OXU1L2dtR3p5M1hpSWh6cXFpVnV1ZHAva2p1MmV3MG1CYXlhM3VTN0FhbzlRRGEwZCt0bklWaktPTlorblhiWEt3TDZqNzVSNkFqT3pKa3F4bkpwMU5seVBuRWMrTFBuMm1jZFpsTG5pV2tYUjVjVS96czdpWVdydCtQUnI5dFVlUXJZSlI0RFpqSThndFpqZ005eXRZRUFoQ09iUXRwSzVlUkFFbmFXZjRmSVdqYmEyczdaaVg2WHh3M3lMK3lwNE1CZnhXK3M4M2MiLCJtYWMiOiJlZjc3NjA0Y2FhNTAzYTJiNTU2MjA0ZDM4YTFmYWNkYzI2OGEzYjY2MDFmZDJlODhlOGMxMWQ1ZDM3YjgyNzFhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:30:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1147787679\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1502666797 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1502666797\", {\"maxDepth\":0})</script>\n"}}