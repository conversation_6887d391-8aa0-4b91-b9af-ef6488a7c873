{"__meta": {"id": "X61f618deddb843dc76a32b8b426551e3", "datetime": "2025-06-08 00:31:05", "utime": **********.823344, "method": "GET", "uri": "/cookie-consent?cookie%5B%5D=necessary", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749342664.222619, "end": **********.823379, "duration": 1.6007599830627441, "duration_str": "1.6s", "measures": [{"label": "Booting", "start": 1749342664.222619, "relative_start": 0, "end": **********.097018, "relative_end": **********.097018, "duration": 0.874398946762085, "duration_str": "874ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.097044, "relative_start": 0.874424934387207, "end": **********.823383, "relative_end": 4.0531158447265625e-06, "duration": 0.7263391017913818, "duration_str": "726ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51517464, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET cookie-consent", "middleware": "web", "controller": "App\\Http\\Controllers\\SystemController@CookieConsent", "namespace": null, "prefix": "", "where": [], "as": "cookie-consent", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=2370\" onclick=\"\">app/Http/Controllers/SystemController.php:2370-2422</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.007169999999999999, "accumulated_duration_str": "7.17ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 341}, {"index": 21, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 70}], "start": **********.219423, "duration": 0.0060999999999999995, "duration_str": "6.1ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 85.077}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 71}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\SystemController.php", "line": 2373}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.234958, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 85.077, "width_percent": 14.923}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/cookie-consent", "status_code": "<pre class=sf-dump id=sf-dump-2120047446 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2120047446\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-806977268 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">necessary</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-806977268\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-627758427 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-627758427\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2041996407 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749342658952%7C6%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkhTWWRIL3ZxdVdEWSs4VVdLdzIzYUE9PSIsInZhbHVlIjoiY1BOd1Q2Zk5IcXRuWFpHK3ZrZHdENkdGWlFwVkZwMlF6UGhXZ1RibGEzTDRaUDA3UjBxYlNFRjg5Qy9KeUJaU0w1dmpmT0pWZ1owWGd0TDZCeTZyT1ZaaVBCSldKUStpMTdZb0FxaURYUnVwR2NPWlhyRXIvVHZZQXZFaGk4YVRuYW5YdTVYTGJycXg1L0RDUG00SXNyTkNweEptQk5KYmpQbkhNbGdVRHppa093Wjd6MG9qNmZ4b1owZXF6SEovSEY2aGZsTGE4QkczOXpmQlpZZUlZOHExZ2NtWTg0eWFVOGFQMmVMZllhSExmR3VRZlNFTGlBRGRpNnNaUUxEQUg5N2NFbHNSMkZueEtzMlRzR3FsV0tmcHZ4SVJ2WmdxMlVRZTFKYkRHT1FVNThaWWh3SXo5OHEybm9GRjVuOW1NSlZKWFU2b3V6bjl4UW5LRENpWWlkalR3cHlNQjcyZjQ0SG8vTlo2ZlF1VVR2NmZGVUU1RWtzOFc5QnJPUUx5R2d2dTRraHJialhvck9NY3gxK01iR0VOTVNvNVBKOTllc3RvZ1ZWZEZ2NERmZ3RwVHJ6Y3lNaTlxVDJqMkNYTjYxZWpkNkF4M3QyczZKTHZSM2JzZWhLYTdMczdIM29kMDYrTExBTHZ4QlQ0Z0ZhSVZqY0tmL3FFUHZ4SnZwbkciLCJtYWMiOiIzNGQ2ZDE4MWY5MDRjMDkyYWYzMjg2NWYyMTlmMjE2OWI2NWU4MmViNmNmZjAyYzdlYmMzYTFiOGVkNGUzOTdlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Im9XNloxRDN6dmovbUcxU1A3cjdkVmc9PSIsInZhbHVlIjoiMmhDY2dtSXB5eUY5djAraURpL2VBQThRZktuVmIzK1hnUHRSc1FySUg5bngyMVQ0SU5STVUwVFEwYUVJTS9CYmo5d1JHUjFzUGNKMm9hcHZWcDJmdzROaHFVc0dLSzZBV1M1M1VKU2ZseTNLT2k1S2Q3R2k4cXZ2Qys5eXZLYWdvUHBOcVZ4MEIrditMMjRRb1Z0M3o3QWZ6MkpReUE4U0E3REc0ckQ4K1BCMDRTZEZPT2tuNThJbWdmNXNVOHd0MXlNK2JMSk9nVzlnTlNYU2NRNHdFc1Q3ellhYlFwWDlvcjVlaUJpdThQT2xtTFRHUTczVVgzN3NjS05SWXFlSDlBNm5NQU5sdnlUVUo2cE9BTDNmU3VzREZ4Y0w2ZVBoVUgyOHAvTWhiS3Z3dWlhRmVOVGc0RTA2VWNKaG1Eak5mNnFhaktWSUxFc1Nmb2Nqc1FmYksyc3JpV0FGbGhpRUNGNG91VFdJdStXUU5sMENzZk9pQVhCSUpYSlpiZTlRTjdLbGNZd1lvUzF6YVoxZGlQRHJvbGRWTThlSjhibnREeTFHMTZoODFjRG1HdXZWbkR0bHpWS2QzOFFLRHh4M2psRGFwcXFQMldsUVNDQkowVlJtRy9NSTdDTkQrVjRqTXhQVXZWU2NGNkJkZjRDcGNGdFROU0ZGcjlFUHdPVmgiLCJtYWMiOiI2NmJlNjRlYTAxMDdhNWQ4ZGMwODk2MGE4NDU0YjI5ZmUwYzBmZTg5ZjE0MTllNDJiNTFiZTM3NGFhM2RmYTdiIiwidGFnIjoiIn0%3D; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2041996407\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1138545128 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1138545128\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-548340849 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:31:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IldJMk5NTm9hVnozbVdaMEdCNWJydGc9PSIsInZhbHVlIjoiM2RFM3Z0dUVUUURsM2REdU1SMGdJTmQ1alVXeVM5M0ZtcWpwSmlMT2E5cTU2dTZZd0U0MHNvbFlNZ3Zab0t6R0VBemlvY0h1VUNQVUgwdDcwN3AxNnBkaW9LUTdDaWVvbzdGZUhWbW9xaDZpRm5QbHBUUG9lM3pZeG9Td1ZjUm44OFBCVkFVbElPNjdrV1ladWJWRVBqQUROS203ZXQxZVpxS0lZQWxvTGc0OTZMMDhSSEtTUDRmbURPUTFVaVJBN3ZlcHl3MC8yMnNlVTRrUktzVU1ONTNQdjVXVE9uS1pFYlZIZUVLcS9aR2U4cG1LME0rZXhWeU1aVG9GNDJzbkk0ekF4K3F2azhGRUN3Zzlrenpqc1RFcmUwdWNLdVVJZGJiSHVJK2xWeXRhMEdwVis2Mkp0RjNuQUpYYkZXSVBwYjVtbFlxOXg1b2NCY1p4SXlJVS9zQm1pOXhoUG5zREdkaXlpQmpuWC8xTC9VU283QzlPUGpGLzRSbG9zMmhyaWQrNG9yaDkxRHEyWk8yLzNoUGZRbnRUeXBhTWpUN2dKdk5Zd21nVUV6ZVp6ZWE1VTVtRGtsYVR2bG0rQSt2Y0hHZ2hsUmhONmgwend0NFJVMnhlWGYvRWRaOXJ3VXJpa1BUQWF1akMvWDB5UnlvNFc0ay9KWW9UZkI3VGZkeW8iLCJtYWMiOiIzNzk3YjZjYTZmZjJhMDI3M2Y3MTQ0YjQ3NDNlNjFhNDk5YjM3NDhkMmJkNzQzNjI4MTk3MThhOTQwNDFiMWRiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:31:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Imw3UjFUNHVxN1ExWlFCeE9RMUJnNFE9PSIsInZhbHVlIjoiM0NkMjFpTUZkOFQycmdHOExBbTUxWEZEUy81NHV5ZERKRzlGNXdyU2VRT3doWTJrb2pITUY0ZktwbzlYdXdwWnBCSjBKdVF6UVNEazNFaDdnL1B1aVREdFR2RFUyYmJzaWpDSWVBdzhtU0RzU3hDNUhIeTRrcjUyUlU4V3o1SzVGNkt0cnJvTDJpM3R0TExKUW82SUJNeXRRWWQ1enBSUlhXVkd2Qk81VlArMmJHcDc4VnRuWVBxckFEU20rTkdNMVZoSlBvSlYvelFJZ3pIb0NEckVQV0FTeWkwQnRTRFl6d1FzUW9zT29UVmFocjAwMmRRM0cwV3VhR1FMUS9TbGdsYXZYenRPM01hc21zSUV3Y2dHOUtXbmhvMzNJakc5d2lSNnJvaDJIbW84aDhzanZZaG55WGJhcHZadEFHYk9IS2hnTUZ4SW5jZVFrQk5mS1R4MDlQbzhnVkVzOUN0R01SRjNKeXg2TlFsMVg1TTJlTS9OdFM4QXpHcXBNaW8rb1VJQjNuRk1mSnBiWlFJdkFZYVBwK1ltcDhYRkRLNm5QQUdTK20xRGJseWNLYzl1RmNMTERsRm1NSXFCa25UVGxjVlVDR0lkZWpTdVh4aCt4eGszWHFCMTdWdUEvNDdxQ1ZtUFl2Z256QXpJUkljbTllc0kwUEVmdlhOTUF3aWoiLCJtYWMiOiIwZGU1OGM0ODRjZDAwZTU5MTJmNzFkN2U4ZjkxNmZmYjQyNTNlNWM5ODk5M2QyMTNiZjY5MjMzZTNiYThlZTMzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:31:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IldJMk5NTm9hVnozbVdaMEdCNWJydGc9PSIsInZhbHVlIjoiM2RFM3Z0dUVUUURsM2REdU1SMGdJTmQ1alVXeVM5M0ZtcWpwSmlMT2E5cTU2dTZZd0U0MHNvbFlNZ3Zab0t6R0VBemlvY0h1VUNQVUgwdDcwN3AxNnBkaW9LUTdDaWVvbzdGZUhWbW9xaDZpRm5QbHBUUG9lM3pZeG9Td1ZjUm44OFBCVkFVbElPNjdrV1ladWJWRVBqQUROS203ZXQxZVpxS0lZQWxvTGc0OTZMMDhSSEtTUDRmbURPUTFVaVJBN3ZlcHl3MC8yMnNlVTRrUktzVU1ONTNQdjVXVE9uS1pFYlZIZUVLcS9aR2U4cG1LME0rZXhWeU1aVG9GNDJzbkk0ekF4K3F2azhGRUN3Zzlrenpqc1RFcmUwdWNLdVVJZGJiSHVJK2xWeXRhMEdwVis2Mkp0RjNuQUpYYkZXSVBwYjVtbFlxOXg1b2NCY1p4SXlJVS9zQm1pOXhoUG5zREdkaXlpQmpuWC8xTC9VU283QzlPUGpGLzRSbG9zMmhyaWQrNG9yaDkxRHEyWk8yLzNoUGZRbnRUeXBhTWpUN2dKdk5Zd21nVUV6ZVp6ZWE1VTVtRGtsYVR2bG0rQSt2Y0hHZ2hsUmhONmgwend0NFJVMnhlWGYvRWRaOXJ3VXJpa1BUQWF1akMvWDB5UnlvNFc0ay9KWW9UZkI3VGZkeW8iLCJtYWMiOiIzNzk3YjZjYTZmZjJhMDI3M2Y3MTQ0YjQ3NDNlNjFhNDk5YjM3NDhkMmJkNzQzNjI4MTk3MThhOTQwNDFiMWRiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:31:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Imw3UjFUNHVxN1ExWlFCeE9RMUJnNFE9PSIsInZhbHVlIjoiM0NkMjFpTUZkOFQycmdHOExBbTUxWEZEUy81NHV5ZERKRzlGNXdyU2VRT3doWTJrb2pITUY0ZktwbzlYdXdwWnBCSjBKdVF6UVNEazNFaDdnL1B1aVREdFR2RFUyYmJzaWpDSWVBdzhtU0RzU3hDNUhIeTRrcjUyUlU4V3o1SzVGNkt0cnJvTDJpM3R0TExKUW82SUJNeXRRWWQ1enBSUlhXVkd2Qk81VlArMmJHcDc4VnRuWVBxckFEU20rTkdNMVZoSlBvSlYvelFJZ3pIb0NEckVQV0FTeWkwQnRTRFl6d1FzUW9zT29UVmFocjAwMmRRM0cwV3VhR1FMUS9TbGdsYXZYenRPM01hc21zSUV3Y2dHOUtXbmhvMzNJakc5d2lSNnJvaDJIbW84aDhzanZZaG55WGJhcHZadEFHYk9IS2hnTUZ4SW5jZVFrQk5mS1R4MDlQbzhnVkVzOUN0R01SRjNKeXg2TlFsMVg1TTJlTS9OdFM4QXpHcXBNaW8rb1VJQjNuRk1mSnBiWlFJdkFZYVBwK1ltcDhYRkRLNm5QQUdTK20xRGJseWNLYzl1RmNMTERsRm1NSXFCa25UVGxjVlVDR0lkZWpTdVh4aCt4eGszWHFCMTdWdUEvNDdxQ1ZtUFl2Z256QXpJUkljbTllc0kwUEVmdlhOTUF3aWoiLCJtYWMiOiIwZGU1OGM0ODRjZDAwZTU5MTJmNzFkN2U4ZjkxNmZmYjQyNTNlNWM5ODk5M2QyMTNiZjY5MjMzZTNiYThlZTMzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:31:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-548340849\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-846521473 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-846521473\", {\"maxDepth\":0})</script>\n"}}