{"__meta": {"id": "Xf97287aad043cc9b40600ed885e1f9f2", "datetime": "2025-06-08 01:18:39", "utime": **********.994756, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.259499, "end": **********.994779, "duration": 0.7352800369262695, "duration_str": "735ms", "measures": [{"label": "Booting", "start": **********.259499, "relative_start": 0, "end": **********.91637, "relative_end": **********.91637, "duration": 0.6568708419799805, "duration_str": "657ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.916383, "relative_start": 0.656883955001831, "end": **********.994782, "relative_end": 2.86102294921875e-06, "duration": 0.0783989429473877, "duration_str": "78.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45053640, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00565, "accumulated_duration_str": "5.65ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.955896, "duration": 0.00323, "duration_str": "3.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 57.168}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.9713469, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 57.168, "width_percent": 10.442}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9813368, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 67.611, "width_percent": 32.389}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1066954314 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1066954314\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-207484414 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-207484414\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1842575648 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1842575648\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-434838029 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749345508047%7C56%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikx6bTRydVpEdUlVRVVzU2FGenZhOHc9PSIsInZhbHVlIjoiQ0ZpSWZ5YVBWOEhsbGZZTHR0YktIVE91S25DaWNoQSsxQUpPNVZFdG11czNweU1xQzlzNlZpQndjRmVOc1BjdzE0VUlPcElJTGdGZmNsSFd4R01zQmJVaGd1cHp1dWdPbWZ5ZTVFd1cwNlM0TG4zK2VTSDN3TEhqZXlTa2FVWjNYWGlPYm82dXhoMUdlQlRXSXM1cHAzQTVrQlRWT1VGYTR3N1dHc1ZFMXMzb2diWkNwNWQ3OWlUMHU3Zjk0eUl5QU5mY21BWVV6RGpZU1BjVUU0M0FZNXpULzQzSGQ3RnVETW9IeEFCeFVSbUpZUmc1dDFTUXZWMGR1Z2tSYnVObHhGV0U2eUdoTjI2eWRPNVgvenI4K0MxWFVOejZUenBjbWhmYUd6QnJjWk1XMlJyejB6NStTbFdLQSswM0U4V3RxVHhlaVBBaDAxRkpHeWZPelJkcTNZQ241TlBHRXJLamNHR24rTmpzbWVnK2k2NU9PWHZXckdWbU8zdVJPc1Z2bjBFZWVkdTZGR1ZqSlJjRk9rWWJiakpNVXFEWFF2dHhpbGxPaktBOHorWVhqaEYxQzNpcCswdmUrTDJpZ0loVGk1VnRJWkU0NU5aemFFb3pVdWduSzV4QnFNem5TUjVHOUFsTkE5S0lIUmxMNENQQmFFUjNQbDhyVlk0NVhRVDEiLCJtYWMiOiIxNzhkNWU3NGFmYjkyNTU4OTIyNmI1YTYwNmQ0OGVjNDk0Y2MwYTY0MjJlYWZiMTYzMWE3MzgxMjU0OTU2ZTRmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImFXbURSeGl6Q1lZTERzOXBRQTRXaWc9PSIsInZhbHVlIjoiSWdvc1kwZ1Qzc1Y3WXozTytEcGVZb2JFNG1YbU9tS0kxcVJ6b0hyOURqRTZKRVAyZi9hWEhWUmRZTVBMK0VxcFhtSC9JMzEwaS9ieFVGMXk5RkhaWEZOQU0yUkZ3THQzVm9lbllGaFRpV1BuK1d2Um9RMXFPcGgrWFp1a0c3SGtibVdKelpvMWFCZTZXMWgxQVNFWHNrUEdYcUpqc1ZNMzYwTE45SVdpTVdwVTFHS2ZkWFRHRHhva090WkhhQURCZysrSHV5OW1BeS9KWGU2aHcyRnVJaDNUQ2kyNU9aNGluSzJjZEE1MjRJaWlwT1RlTlo1Ly9McG1GMHV5MDlXMjJFeUlYQ3M1c2lPUEZ4TU1qc3IwZFJCRUZIcHJuaTJxeTN0azF1a3Q5cWh0R2lpODhMeFpDZkI0WFRXd2FsQkxMdHJEamZpNDNaZzgxczhOb2Z3NGpENVowT1dmWC95cklUOUJKZFNOblp0RXdQWmlXM21EY0JOeTRrQWFodEg1b0FibXBrdVYzZ3puR2VIaXlsR1hobmRZSVNRQXJiU1h6NHBkSisvclVtbHZqelFDeG14Q0plbDZHSllpdVlONzFtci96VXhWR29JQmlVZ1BkZjVleitIM1d5bnM1V2h2R0thZHhDQm9NVnliWHJ4YjVjZnFjTmlwZ2NHUHNVbWUiLCJtYWMiOiIxYmI5NTQ2NDU4OTMzZGFkNzVhNmVjMGVmMDk3ZGViNjQxNWI4NTMyY2I5YWQzMTFhMTIwN2M1MjEyMGEyYWFlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-434838029\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1831821369 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1831821369\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1980414096 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 01:18:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkVvVGR3V1h3SHhkc0VUc1ZYSlpkbmc9PSIsInZhbHVlIjoiVGFwbVljS3JzVjdzbkpzanpLZVJETEFRK0tTekhXUUVRTFFHejh5elNqUm04dHppNW95bHpKTHRqV1VIdVllK0NEK0pOM0YwVEVEMkNPb1VVcG1CdTFjS3J0UVdpb3U4YzBLNWJDZFFKcFlqeERWczMwYllmN2kyZWw4bkJsYlV1V3daV2RkaFNMSnhOdUZlM1VGalE2ejQyQXRPYU9ZRHJoZXlnWHFqZ21xVlNDS2YyUEx0N2Ivcm5vMnJOOGg1UHozUGFBQnpaYzFwNXhCY3h5YWpZeUxhdm1MZ203UnIyR3ZSN1RpSy9aQk9xcFM2VWc3NTh6dVJ6UnZvQ1Zucnk4YTMzdHhFQStWSlhkSzg2bzFBM3NyTGw1d2t0aDdURGlkdDNPQUltV2lPa3cxMDEzMnYzOEZaRmdGSVVtNDVONlJjV0NZdmRNbGFML2NYSGczbmxZOGJBTkRweFFxTlNJWFE4ZmJmWlB0M3NpanZ4eGRWQ3RlNEdzeFk3RjYya1N1MWJNY1JVUHhMNUdmbmVvRVlaTkQvWER1NkVwdWNKMGFVLzU1N1lDNjduRy9tZ0ZCWjZFZVdzSW9NTkFySGVvci9EZVhvMU5UMGRRTDJTdnUrK1J2eGdXVTE0THJGNjRiQ3BJTVZQYWpRcTFhZ1pJOXNCNVczbUsra0Z2YWIiLCJtYWMiOiI3Y2QwN2JmMTY5YzMxOGExYzZhMGVjNTdiZWYyMGRkYjFmYjRjZGNiNTViMzZlMjYwNmU1YTg2NDAzYmI3YjA0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:18:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Imd4QTljVlI4bWl1SWxVMENEc0d4SVE9PSIsInZhbHVlIjoidUlHNlFtNnJqaHk0Q2FqdzRkbEgzU2IzdDdlNnd4d3h4QnIxeU1qRVR0QWNNaXprbzVyblhPS1Z6a1FLZmdWRWk5NTlYdUFhazlHK2dHRDRuMUhUbkpKa1RTNmtTOXJPQzZESTduVStXcnE5UUtnd2VmNW94OTNRbmtEaVh1djNJTGFIRmJibDM2QjFQLzdQbnlXU1NUdFlVWFptWFpPa3JydVFnL2g5UWdNZU1LYnlkbnJjeHlIT1VnalMwUm8vbSszd0VsQ1o0VHZWa0JEVm9MSTJETzZ0dDJmeFE3SHpibkppb201N2lhbFlJMld4dUhBRDh0VXlVclZDOTJRalVXYTk0OTNBNVNTNUwzTTZ3cVNzcGRZdjhzZHdIR1BLQVNINWZNTEhod1VkdUJtNGJBd2RzMUFlZ2IwdTdtSE5WM2NlYUJJc0RIaW0zMWIxMC8yaTZTR2t1Z1RzME5hQ2pxcDJNTURnZUlOOVQwQy9aZnpMNUljcm1aNVZ3Y3RkUE5KTUJtM0tyQUs4OTNJSTYrbXRkU3d1Y3RWZGJoSW8yeWpnL3dmMU5zbExRT3dTbXJYajlJZ3hmRFhDMmlhaVhLQVlGQXFTcllzS1Bkb05zbWRnN2loZzZJcllTRUFubEJVT08yNkdDalpWMW5NcFFLNUgvTEhvdHRoNlJuOTAiLCJtYWMiOiIxNmZiZGFlNzgyYjQ1ZGYxZDg2NGM2OThlNDI2NDdlNDQ2ZTliYTMzODZkNTZhNTk1ODlhZmFkODRjNTRiNmY5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:18:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkVvVGR3V1h3SHhkc0VUc1ZYSlpkbmc9PSIsInZhbHVlIjoiVGFwbVljS3JzVjdzbkpzanpLZVJETEFRK0tTekhXUUVRTFFHejh5elNqUm04dHppNW95bHpKTHRqV1VIdVllK0NEK0pOM0YwVEVEMkNPb1VVcG1CdTFjS3J0UVdpb3U4YzBLNWJDZFFKcFlqeERWczMwYllmN2kyZWw4bkJsYlV1V3daV2RkaFNMSnhOdUZlM1VGalE2ejQyQXRPYU9ZRHJoZXlnWHFqZ21xVlNDS2YyUEx0N2Ivcm5vMnJOOGg1UHozUGFBQnpaYzFwNXhCY3h5YWpZeUxhdm1MZ203UnIyR3ZSN1RpSy9aQk9xcFM2VWc3NTh6dVJ6UnZvQ1Zucnk4YTMzdHhFQStWSlhkSzg2bzFBM3NyTGw1d2t0aDdURGlkdDNPQUltV2lPa3cxMDEzMnYzOEZaRmdGSVVtNDVONlJjV0NZdmRNbGFML2NYSGczbmxZOGJBTkRweFFxTlNJWFE4ZmJmWlB0M3NpanZ4eGRWQ3RlNEdzeFk3RjYya1N1MWJNY1JVUHhMNUdmbmVvRVlaTkQvWER1NkVwdWNKMGFVLzU1N1lDNjduRy9tZ0ZCWjZFZVdzSW9NTkFySGVvci9EZVhvMU5UMGRRTDJTdnUrK1J2eGdXVTE0THJGNjRiQ3BJTVZQYWpRcTFhZ1pJOXNCNVczbUsra0Z2YWIiLCJtYWMiOiI3Y2QwN2JmMTY5YzMxOGExYzZhMGVjNTdiZWYyMGRkYjFmYjRjZGNiNTViMzZlMjYwNmU1YTg2NDAzYmI3YjA0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:18:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Imd4QTljVlI4bWl1SWxVMENEc0d4SVE9PSIsInZhbHVlIjoidUlHNlFtNnJqaHk0Q2FqdzRkbEgzU2IzdDdlNnd4d3h4QnIxeU1qRVR0QWNNaXprbzVyblhPS1Z6a1FLZmdWRWk5NTlYdUFhazlHK2dHRDRuMUhUbkpKa1RTNmtTOXJPQzZESTduVStXcnE5UUtnd2VmNW94OTNRbmtEaVh1djNJTGFIRmJibDM2QjFQLzdQbnlXU1NUdFlVWFptWFpPa3JydVFnL2g5UWdNZU1LYnlkbnJjeHlIT1VnalMwUm8vbSszd0VsQ1o0VHZWa0JEVm9MSTJETzZ0dDJmeFE3SHpibkppb201N2lhbFlJMld4dUhBRDh0VXlVclZDOTJRalVXYTk0OTNBNVNTNUwzTTZ3cVNzcGRZdjhzZHdIR1BLQVNINWZNTEhod1VkdUJtNGJBd2RzMUFlZ2IwdTdtSE5WM2NlYUJJc0RIaW0zMWIxMC8yaTZTR2t1Z1RzME5hQ2pxcDJNTURnZUlOOVQwQy9aZnpMNUljcm1aNVZ3Y3RkUE5KTUJtM0tyQUs4OTNJSTYrbXRkU3d1Y3RWZGJoSW8yeWpnL3dmMU5zbExRT3dTbXJYajlJZ3hmRFhDMmlhaVhLQVlGQXFTcllzS1Bkb05zbWRnN2loZzZJcllTRUFubEJVT08yNkdDalpWMW5NcFFLNUgvTEhvdHRoNlJuOTAiLCJtYWMiOiIxNmZiZGFlNzgyYjQ1ZGYxZDg2NGM2OThlNDI2NDdlNDQ2ZTliYTMzODZkNTZhNTk1ODlhZmFkODRjNTRiNmY5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:18:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1980414096\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2134882842 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2134882842\", {\"maxDepth\":0})</script>\n"}}