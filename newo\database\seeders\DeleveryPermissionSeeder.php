<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class DeleveryPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $arrPermissions = [
            [
                "name" => "manage delevery",
                "guard_name" => "web",
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                "name" => "show delevery",
                "guard_name" => "web",
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                "name" => "create delevery",
                "guard_name" => "web",
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                "name" => "edit delevery",
                "guard_name" => "web",
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                "name" => "delete delevery",
                "guard_name" => "web",
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
        ];
        Permission::insert($arrPermissions);

        // إعطاء صلاحيات الدليفري لدور company
        $companyRole = Role::updateOrCreate(
            ['name' => 'company'],
            [
                'name' => 'company',
                'created_by' => 0,
            ]
        );
        $companyPermissions = [
            "manage delevery",
            "show delevery",
            "create delevery",
            "edit delevery",
            "delete delevery",
        ];
        $companyRole->givePermissionTo($companyPermissions);

        // البحث عن شركة موجودة لإنشاء دور Delivery تحتها
        $company = User::where('type', 'company')->first();

        if ($company) {
            // إنشاء دور Delivery
            $deliveryRole = Role::firstOrCreate(
                ['name' => 'Delivery', 'created_by' => $company->id],
                [
                    'name' => 'Delivery',
                    'created_by' => $company->id,
                ]
            );

            // تحديد الصلاحيات لدور Delivery
            $deliveryPermissions = [
                'manage delevery',
                'show delevery',
                'create delevery',
                'edit delevery',
                'manage pos',
                'show pos',
                'manage customer',
                'show customer',
                'show pos dashboard',
                'manage financial record',
                'show financial record',
            ];

            // إعطاء الصلاحيات لدور Delivery
            $deliveryRole->givePermissionTo($deliveryPermissions);

            // إنشاء مستخدم تجريبي بدور Delivery
            $deliveryUser = User::firstOrCreate(
                ['email' => '<EMAIL>'],
                [
                    'name' => 'Delivery User',
                    'email' => '<EMAIL>',
                    'password' => Hash::make('1234'),
                    'type' => 'Delivery',
                    'default_pipeline' => 1,
                    'lang' => 'en',
                    'avatar' => '',
                    'created_by' => $company->id,
                    'email_verified_at' => now(),
                ]
            );

            // تعيين الدور للمستخدم
            if (!$deliveryUser->hasRole('Delivery')) {
                $deliveryUser->assignRole($deliveryRole);
            }
        }
    }
}
