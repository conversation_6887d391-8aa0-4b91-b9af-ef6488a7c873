{"__meta": {"id": "Xf26ab836c268c14136ad68fa2e509989", "datetime": "2025-06-08 00:58:04", "utime": **********.367677, "method": "GET", "uri": "/customer/check/delivery?customer_id=7", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749344283.646063, "end": **********.367706, "duration": 0.7216429710388184, "duration_str": "722ms", "measures": [{"label": "Booting", "start": 1749344283.646063, "relative_start": 0, "end": **********.280096, "relative_end": **********.280096, "duration": 0.6340329647064209, "duration_str": "634ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.280106, "relative_start": 0.6340429782867432, "end": **********.367709, "relative_end": 2.86102294921875e-06, "duration": 0.08760285377502441, "duration_str": "87.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43910560, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02624, "accumulated_duration_str": "26.24ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.326783, "duration": 0.02554, "duration_str": "25.54ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 97.332}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.357269, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 97.332, "width_percent": 2.668}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1146462265 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1146462265\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1247749667 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1247749667\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1023418820 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1023418820\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-495223012 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i31pha%7C1749344276347%7C8%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ii9yelQyc05OY0V4VHlmR2xZcGNudkE9PSIsInZhbHVlIjoiakc0bmZmMFp6RVYxUGJlb0NOQy9CamJydGZWUTYxY1JFWlp3dVdTYjZUNEN1MSs4TERKVUZvK2NmZmhJZzI0TWloVUhPZHdmNGdUVDg0MytFZUdnVmZCbXRiQ2pFcWdkTUZQTmZmeGlaOUluUm9oaGQ5UGtZMUpENVliQ2xtalJZbzcvS2Z2T2xKM1dmWUxjOXFYaFp5alJIc2lPcVYvR2g0NFlvdUp4UDZjV3RuU1JSZlV3YmdBUmVDamJ6WlcvM3JGWnlzdTRIMUhvVlR3TDNkSE9WcENlcGIwbXo4OE5BQmsyZkpQNEdBMjZWK2pJb3pWQTZKaDhLb3dJWDhZS3pOTlA5UmtuNkJWZm5IT0JXckVSV2Zicnp5MS90eVI5YmpZQUpTaFpGWEVFOWZ4WDVISU0rdTYxYjQvbU91RUdrQmRSVGw4YjNTN0ZNdkFFc3MrR3J1ZUVWb05xbkZzVElWUDVxWXF5UERyQlhHei9maXdCT0tkdllEbFJ1Z3RQclRaTHlwQURIVFRWUGJEWWpFVitXTnJ4Y0ZXQnVpRWFVY1NVUGFSSjZQekZhRE9HMXRXRTFIOE1rUDFIRTFjUXE4RDI3WVBCYVVPQk5ZOFJBYjIya3JMTThDSlp6U3RGcVM0dXQ0SmtHaTRibEFXcysxeU9hNEZ5YnZWRzlsWm4iLCJtYWMiOiJhNThmYWFmNTAzOWZmNjQyMzdkODNiNDFjNTFhNzU3ZDkwNGM4MDkwNDFiYzMzYzA2OWFkZDYzNzZmNjlkZDdhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImpDVWw3cXR4UnpraHhiaG50UHlXS3c9PSIsInZhbHVlIjoiMlduOHIzNHFjNjlyY2U5cldYS3JKZlQ2SXhuZ0swbW9CRklINlVpcHlMK0FVZUltOUxqMkZDZGNPUzBDa3dGallnSVZaaGJqaWNtaFc3NjZrbEJqdUJzMXhVMUt0TkdkWVViU2tqU3MxMGxiUHFVTk9zWGpVSmJnSHppMjNZR05BdndmekZRR3d4a01MZ2syQ0pIb0xTUGdKNDIxSEhiUSs3M2JMKytBVnBhWEFtc3BDMVk4Snc5aWUwTGJ5WXlWbXh6TytRWHB2R2orWSt4aEMzLzVIdGZWU3hYUTRkTHhlYlQ1bUdqbDFiUUJvTVlDTnM3bnZ4elNrNU9mV3NxcDVIdTlnd1E3TEhBaDdCK1ViTW01OWxNb3lnRFExVHdRNVJONXFZY0hsVVFRcVB2bVdQL3hXLzdwNmF3RDlIcTE0eVFVUnptMCsrM25zcFg2SXVCc1pyeHpESHFsdmgzaEEzb2dtK0dPbVJKdERFdS90bDdPVWRhUWVXQzBhWklYWjFDT00zWDZML1RkdFJXVllxMlZSY3hIRkJJZERzSVlFT29TckdZK3N3c1diTzN0RDFWcjdDaWN3YW9UWVBxWmVpOEVkYksxbFVEanc3RnZqV3lteEdUeFlReHZJeTdhTVVxVXA2bCttV2I1ak1YcGNkVzR2T3JPbW1rczhZRGYiLCJtYWMiOiIwMWE2MzcxNzA3MDkwOTM0YTBjYTIzNjFiZGM0NjQ5ZWY3MWMzYzcxZGM2YjcwMDY1ZTM4Y2ZkYWE3NmIyOGE3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-495223012\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-967498432 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-967498432\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-114170987 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:58:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZPMHBTMGFDbXFKV0M3NkRReTBFWEE9PSIsInZhbHVlIjoidjJPTEFPK01xcmhWNXdNcVI5MGs5V0FQS0VXTWRlUFpIRnBhQmRuSllyUG5Fci9YaFJuMjJ3MVJHRHlXSmRqWjdvcUNzMnJJcUg5czAyOWR3U0RFNmZxdjFOelVnbWE1QmhuTXh4eVl2ZU1DUmk2L0dUU2ZwTHhCNXZ0bnJENGtXZ3ZHTEowaEpnWk12czJaWnpITzF4SVNabVRwQWg2RGhPR0Z3d0hhaGpJUE9nS0NKdVE0S0x2cjczQVRqTGxIaGpLOStGMnFwSTc4MGJDTXZoUkVHdUZ6b2c4c25iVGRIV3doZ0phazZHOWtzVXQ4cmZrZzVjYldXTEFzWkFiQzVCQ0lNUm5YM1VhT05GcEJLU0NxS1VobW1JejRYNXJ1SGEydkQxSFNaQStSRTFRd1RzeVQrQ2QreHhNTlNKNWp1SE9VeCtvUy9sRmcxYTJiMjRiWS9GRDhhd00raWlKZlBNR1V0RjdTNEMrQnhSQVh0UllzQzFBbXJBQnF5SDB3K1EvUnBHVURUNkYwaEhoajhHOEpVWUV6Vld3Z3ZrSXJYTUcydXpYOXdyTkNKM0dxNk93NUI3aXE3SlFmb2JOMWQ5SUdFMk9BdklIZDEvNGNQVldEdHJZWmtLc0Z2eDNsOFE2QWhmN2crdlhZdng4SGVLV25ra2xPWXZab0l1ZDkiLCJtYWMiOiJjZjM2M2EzNTQ2ZTM2M2U0NTZjYzcwZGJmN2Y2YzU2MDkxYTE3YmE5MGM0YTg1MDg2ODdhYmRlMDM4N2E3YmExIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:58:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkFnSGkzOG45NnBSdHltRGxKbUo0Qmc9PSIsInZhbHVlIjoiMWZmK05HcXdud25Za0ZwaE9mNzZOdE5GUDdQUVBFYmk2eTNWcFRCSjBSbkR5bmluUzE1SGN0a3JCbUNxNUIrS0NucTBDSnhLcnpvWEhtL3FReXlDNnFnMEltc1h0MVgxSlhtenZFSWtqS3B0Q2R3dnpkWDluUDFwblpuVTBhTDhyYUxPbmFQSTlkc1V1YnRiVVdWTEZXQm16QVE1MzdubzZJUkZOTi9vR1JIOFJTTWNyVEVZNnl4cXZERXpHVktsY0UvQytKQXB3NDhid1ZkQzNLK1VwSXVERnIrY3c1c3B3V21EY3JpdTI0VGRJaWFJVUZCUHNOcWZ5NWxLN3FPZkZWN2IvaXhPWGt0Wk9HRi9DaEw4T0p3UkVkWGJYb1VjTHZwUmxZaWplSWdiQWdUbW9UejlQOXRvQzVPTVRIK0hqanF4VVMvdHo5cXpBMVBqc0F2d3BKdWtOWFBwZFQySTdVK2Y2S0FaOWVMT2xiemhsQ29SakNPZzZvUDllSmxnRnc3NUhFMk1aYXNIUWtPeGw1NWM0STNmb3dLUEFoeXBRdllzcUNoK3FmbTJYcVF2Z2pFc0lUT1poWUk3MEtkRlUvTUtjTSt5ekN6ai9Ma0FRdVRxbTVCMkN0TXNXbGtLQlloVE84aThONlFLL2FlT2FhcFNVR0U3Y0l0anNNaTUiLCJtYWMiOiJkZmQ4MzI0N2NmZTM1NGU2ZjIwYThiY2JmYTI1ODA0MGEzNWI4MDUxOTkyNmExNzc4MTMyZmRjMzEzY2QwNDgzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:58:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZPMHBTMGFDbXFKV0M3NkRReTBFWEE9PSIsInZhbHVlIjoidjJPTEFPK01xcmhWNXdNcVI5MGs5V0FQS0VXTWRlUFpIRnBhQmRuSllyUG5Fci9YaFJuMjJ3MVJHRHlXSmRqWjdvcUNzMnJJcUg5czAyOWR3U0RFNmZxdjFOelVnbWE1QmhuTXh4eVl2ZU1DUmk2L0dUU2ZwTHhCNXZ0bnJENGtXZ3ZHTEowaEpnWk12czJaWnpITzF4SVNabVRwQWg2RGhPR0Z3d0hhaGpJUE9nS0NKdVE0S0x2cjczQVRqTGxIaGpLOStGMnFwSTc4MGJDTXZoUkVHdUZ6b2c4c25iVGRIV3doZ0phazZHOWtzVXQ4cmZrZzVjYldXTEFzWkFiQzVCQ0lNUm5YM1VhT05GcEJLU0NxS1VobW1JejRYNXJ1SGEydkQxSFNaQStSRTFRd1RzeVQrQ2QreHhNTlNKNWp1SE9VeCtvUy9sRmcxYTJiMjRiWS9GRDhhd00raWlKZlBNR1V0RjdTNEMrQnhSQVh0UllzQzFBbXJBQnF5SDB3K1EvUnBHVURUNkYwaEhoajhHOEpVWUV6Vld3Z3ZrSXJYTUcydXpYOXdyTkNKM0dxNk93NUI3aXE3SlFmb2JOMWQ5SUdFMk9BdklIZDEvNGNQVldEdHJZWmtLc0Z2eDNsOFE2QWhmN2crdlhZdng4SGVLV25ra2xPWXZab0l1ZDkiLCJtYWMiOiJjZjM2M2EzNTQ2ZTM2M2U0NTZjYzcwZGJmN2Y2YzU2MDkxYTE3YmE5MGM0YTg1MDg2ODdhYmRlMDM4N2E3YmExIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:58:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkFnSGkzOG45NnBSdHltRGxKbUo0Qmc9PSIsInZhbHVlIjoiMWZmK05HcXdud25Za0ZwaE9mNzZOdE5GUDdQUVBFYmk2eTNWcFRCSjBSbkR5bmluUzE1SGN0a3JCbUNxNUIrS0NucTBDSnhLcnpvWEhtL3FReXlDNnFnMEltc1h0MVgxSlhtenZFSWtqS3B0Q2R3dnpkWDluUDFwblpuVTBhTDhyYUxPbmFQSTlkc1V1YnRiVVdWTEZXQm16QVE1MzdubzZJUkZOTi9vR1JIOFJTTWNyVEVZNnl4cXZERXpHVktsY0UvQytKQXB3NDhid1ZkQzNLK1VwSXVERnIrY3c1c3B3V21EY3JpdTI0VGRJaWFJVUZCUHNOcWZ5NWxLN3FPZkZWN2IvaXhPWGt0Wk9HRi9DaEw4T0p3UkVkWGJYb1VjTHZwUmxZaWplSWdiQWdUbW9UejlQOXRvQzVPTVRIK0hqanF4VVMvdHo5cXpBMVBqc0F2d3BKdWtOWFBwZFQySTdVK2Y2S0FaOWVMT2xiemhsQ29SakNPZzZvUDllSmxnRnc3NUhFMk1aYXNIUWtPeGw1NWM0STNmb3dLUEFoeXBRdllzcUNoK3FmbTJYcVF2Z2pFc0lUT1poWUk3MEtkRlUvTUtjTSt5ekN6ai9Ma0FRdVRxbTVCMkN0TXNXbGtLQlloVE84aThONlFLL2FlT2FhcFNVR0U3Y0l0anNNaTUiLCJtYWMiOiJkZmQ4MzI0N2NmZTM1NGU2ZjIwYThiY2JmYTI1ODA0MGEzNWI4MDUxOTkyNmExNzc4MTMyZmRjMzEzY2QwNDgzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:58:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-114170987\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2002090187 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2002090187\", {\"maxDepth\":0})</script>\n"}}