{"__meta": {"id": "X0869d63c91a65059ddae619e46dd0842", "datetime": "2025-06-08 00:30:11", "utime": **********.381353, "method": "GET", "uri": "/add-to-cart/5/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749342610.271391, "end": **********.381385, "duration": 1.1099941730499268, "duration_str": "1.11s", "measures": [{"label": "Booting", "start": 1749342610.271391, "relative_start": 0, "end": **********.162652, "relative_end": **********.162652, "duration": 0.891261100769043, "duration_str": "891ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.162672, "relative_start": 0.8912811279296875, "end": **********.381398, "relative_end": 1.2874603271484375e-05, "duration": 0.21872591972351074, "duration_str": "219ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53600472, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1322\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1322-1579</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.03056, "accumulated_duration_str": "30.56ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.241827, "duration": 0.026269999999999998, "duration_str": "26.27ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 85.962}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.288181, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 85.962, "width_percent": 2.389}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.32025, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 88.351, "width_percent": 2.716}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.324798, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 91.067, "width_percent": 2.749}, {"sql": "select * from `product_services` where `product_services`.`id` = '5' limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1326}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3360279, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1326", "source": "app/Http/Controllers/ProductServiceController.php:1326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1326", "ajax": false, "filename": "ProductServiceController.php", "line": "1326"}, "connection": "ty", "start_percent": 93.815, "width_percent": 3.043}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 5 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["5", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1330}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.345655, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "ty", "start_percent": 96.859, "width_percent": 3.141}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1297049840 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1297049840\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.333734, "xdebug_link": null}]}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:1 [\n  5 => array:9 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"id\" => \"5\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"originalquantity\" => 24\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/5/pos", "status_code": "<pre class=sf-dump id=sf-dump-1342249113 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1342249113\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1872270583 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1872270583\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2132599703 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1834 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749342593005%7C4%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImJMbU1SRkQ1LzZGbHcvb3pLTDNQekE9PSIsInZhbHVlIjoiWEI2SlYzdHRyc0RVWGQybkpLRURGNDkxNmZQZEcycStvVmVGbFRoTUtqbVFia2V3bStzeUxVVThBenpvaVVQeGwwTHVKWVpWYXhFRVVQekhBL1BJS09Jakd5dzFkTitBMVROZHlHUzVRdWhtbThFNVRaYTErM2ZxcFVrV256S2hWVlBaSjY2V2VVQXpiNFV0TVZzTFBDeHdhMG1CYTZ5a3h6L3ZoRzFlSkFWcW90QndDQTY1MmcxaUIrcnFkdThwbkZkb09ibHBjQVhGTXRjRkpKK0JhVDZuUFNDVVJjMDBzNFpMRGJHcFUvZ1JzY2RNRVRDNXNxZVVzQ3JGRVorMDFiWHdOYUZ1K25jVmc4UnYzVWZ6emtPYXZiQ09rUGltemVud05IZXpBekVua2tnR0cyZlpZY09xSkZYNXUzdzA2dGZ2cHRJUTVMQWJHWGxmcUNVTWI4YkFGYXZiVUNUUy95RnovZjBkY082TjNWaXRGK2Z4Q0M4NGNSOUZjZEUwbkZGcWpGWjlMS0tJU3pXdk9xVmE4UkcwdWIzdnlqUTJRZ0hlNFBWRFJacUN6TWNQSVl6T0JPS1lRNUFIK1pTZTVPd2preGxveTA4ajN5OEdNa21kQ2o1ZXNkdlZzSTQ1SzRHa2ROS2ZhNGFNWExqSHkxVmRVcHlZT0VwZVpNOEQiLCJtYWMiOiIwNTlkZDFlNGU5OTdkNWNjNGQ4YzkwNGYxMmUxNDlhZjhhOTRkZTBiZGFlMTUwMGU5YmU1MWNiZDQwY2Y0Y2U4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImxJWUthRFU1QzJlZ01FdVZWUGtWakE9PSIsInZhbHVlIjoiLzJ6TmFkOGw2U2hEblZnRmtla0JYRUpZTStNN1crcmdxdnc4cklvRDJOZEtzcVdNTXpubEpDUER6R3VzQjB1N1pPUDhNUkM1WkZWaFJCZnp0NDhZb0JTRHZjcWMzck4yV1VMSlNUczFpcTc1cUYrNXdQMExXbGtVQVJYUlBHekQ2MUdwQ3lqc1FIYUdOVjRIMitDNFRMdE93Q2JPRGxpdXRxYkEwOUpGenZ6NFZzaEg1NW9RWGhEZ0JTTjF5VXh4ZHJLenA2WlBQRzZyVUI5eWZaOUZDdHBTS1lzM2tWcmN5SGJNbEplbjZCTW5TWXRaR2h4TFFuYWJ3V2t6bUJ2K0JqYWxJb0hjVThKYmtFTFhXTzU4YllmOXdnZ1dMa1FxSUxzRXYzV3lFWWhWNWdSUFdZSXNpMEJPZitFcEtqbm41OEZLeE1HdXpwRzd5OFAzK2JvY0pPUEkzM3NweVJuYzcxN3Jkd2EyaExkZFFsb1B0WXU2TmdLSis5ODFVODZoY05JNVplR3Z0dDVnRUlVMFJvNllrV0NNZUVoQnZUNkRRbzZ4SzUwY0xjYlZZdnp2WUFZOEduNDVtYjBmVkFERjhyMVlNVXplZ1RXbzVCU1pPcjZHTXltbWdnenlSRGk3WnIyUjhIak5DTDZSMUozZ3N0N3BVM0VDenlic3lCcEUiLCJtYWMiOiIyMGVhZmJiOTFhZDU2NmY1MzBhMmM0NDg0ZDc5MTAyYTllNGMxZDNjMzBkYTM3YzA2OWY0NTI4YTg3YjUwZWEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2132599703\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-157759303 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-157759303\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:30:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IndadnNveG90OTNGbENoZVFXN3gvcXc9PSIsInZhbHVlIjoianhrYzYwVStpV1Q2cXNDUXYzeGhudWVPMFpCMlJNandvYUovSGlEQ3BnQVZxc0ZSS1luVURkNHhrNzlicTNWM3oxSFhReTAwR2pDNGU1dWZNNWltTnlmMGVhS0dZMVB3a3h1cFNYK0pnYmdhZHkxVlBPVUZuMHFPb3Y3NDdjZzhXdk55YVYxSEF4aHhpWWZ0bE9QVmV2M25jazNyMW56SVdKa2hBa2JVQ1FMQ3NDM3FJa3pOdFhJT3g1N3VlSEF0Vm1KSTJkejM3a05sczc4NGhQUURqK0IyOTcyYjhFaG91VStObldKcUdva1ZvNDM4SFkrYnJycmdTdW9lSEcxb3dDaW81Tkkwd0R4SGtUejVjbndTamYrZDhFWkZhOEtVeTBkU1hSQmhZQXNTUVZHK3dtc0c5dFRwWS9uOUp5WlBWZXJveThiYXJPcjZFMWNpanpkRVpPQ3pEZDN3bDQxV0hSWkZzKzRKUm9DeUZVaVNIbWY1UEdXNS9XYzdyU3NxTGtSWGh2eUFlaWtWeE5DRWVwK25CUTZub0txdUgzeEQ2QTJNcUJhWWxJbVhrVXZlcjBmSm03aWp3aWc2SWJ5SUtxcXNlZGd4aHlrRFZjVERyajhzaXdMNUNhL3hidFhOdXd6TVh3NXR4N053Uit2WC9KRUhoVHRUTVlDbkFhSlIiLCJtYWMiOiI4YzJhZmU5NjBkNmZkMmU2OTM4YjExMDYzMzA4ODgyMTljZjY0MTNkMWJjOWQ5ZTcwM2QyMTkyOTIwNGI1ZWMyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:30:11 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InVTVitnUHdsb01zbkJDc01iVFRYTmc9PSIsInZhbHVlIjoiY0I2cHo3L01XTzR4djd2WldHOW9XMVZsM01MMUpGaDk4VHNGRFhUM3JJWXY4K1pVTkpRRHlVcFZDQ2VMTjhTcXNjZi8xbFlLKzF2Qm42cWE5d0JtZWRxbFFtTmxYME9QMFlvUDE2bUJUQVhRaitGV25Bb2xwaWFrVHIvbGY0UFRHY0V6TzEyT3gwbDlzb3ErWUxRNm9RN1M3Uk51WkMybStpdWhYOFNJY3g4M0I0Wm5SeStyZ29FSGNlNysyODBZRUdOU014VnBGME9kWm4wOTBPTzVkUDA2NHBZUkd6N2lIT0kvMUZiQUkydm5sQlUyMVlGYS9vcVhvRU13amV4TVBibmJhTXZRUFJvVXdEelpWOFFBNXJ0NnhlL0dONE1rSW56clpQVGxyQVQzaVp1bmpqQ1dKaVVKSHh6Ymk3aDhPRVVWZTFFYWdCSk5VMFpaNEcwL1VFN1dySDVpR2ViMWxVYmhYankwcU1SL25oSGNTM1ZxRm45YjZITy9ucGZtT1llekdqY3YrN1pFQ1RxT2t4ZmJGNHVuanR2MWNjTVpzSlpYYWJ2Q2RYZzl5QWFxZG9scUUzT2dlVnZMZTlzSEpEUmlSTThhZ21tL0hiaUw2WWVaVzVNWmVaSjdVL0NiUHppRnN5VGZmT0ZYNXhuNkQzaGcraFVORXN5eU5WRW8iLCJtYWMiOiIxMTEzYTkyMWExYzUyZmQyMjZiZWMxYzRiMmE0MTE0M2EyNWE4M2Q2YjMyNmY5NGEzZDBmNTIwZWNlZDM5NWIyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:30:11 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IndadnNveG90OTNGbENoZVFXN3gvcXc9PSIsInZhbHVlIjoianhrYzYwVStpV1Q2cXNDUXYzeGhudWVPMFpCMlJNandvYUovSGlEQ3BnQVZxc0ZSS1luVURkNHhrNzlicTNWM3oxSFhReTAwR2pDNGU1dWZNNWltTnlmMGVhS0dZMVB3a3h1cFNYK0pnYmdhZHkxVlBPVUZuMHFPb3Y3NDdjZzhXdk55YVYxSEF4aHhpWWZ0bE9QVmV2M25jazNyMW56SVdKa2hBa2JVQ1FMQ3NDM3FJa3pOdFhJT3g1N3VlSEF0Vm1KSTJkejM3a05sczc4NGhQUURqK0IyOTcyYjhFaG91VStObldKcUdva1ZvNDM4SFkrYnJycmdTdW9lSEcxb3dDaW81Tkkwd0R4SGtUejVjbndTamYrZDhFWkZhOEtVeTBkU1hSQmhZQXNTUVZHK3dtc0c5dFRwWS9uOUp5WlBWZXJveThiYXJPcjZFMWNpanpkRVpPQ3pEZDN3bDQxV0hSWkZzKzRKUm9DeUZVaVNIbWY1UEdXNS9XYzdyU3NxTGtSWGh2eUFlaWtWeE5DRWVwK25CUTZub0txdUgzeEQ2QTJNcUJhWWxJbVhrVXZlcjBmSm03aWp3aWc2SWJ5SUtxcXNlZGd4aHlrRFZjVERyajhzaXdMNUNhL3hidFhOdXd6TVh3NXR4N053Uit2WC9KRUhoVHRUTVlDbkFhSlIiLCJtYWMiOiI4YzJhZmU5NjBkNmZkMmU2OTM4YjExMDYzMzA4ODgyMTljZjY0MTNkMWJjOWQ5ZTcwM2QyMTkyOTIwNGI1ZWMyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:30:11 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InVTVitnUHdsb01zbkJDc01iVFRYTmc9PSIsInZhbHVlIjoiY0I2cHo3L01XTzR4djd2WldHOW9XMVZsM01MMUpGaDk4VHNGRFhUM3JJWXY4K1pVTkpRRHlVcFZDQ2VMTjhTcXNjZi8xbFlLKzF2Qm42cWE5d0JtZWRxbFFtTmxYME9QMFlvUDE2bUJUQVhRaitGV25Bb2xwaWFrVHIvbGY0UFRHY0V6TzEyT3gwbDlzb3ErWUxRNm9RN1M3Uk51WkMybStpdWhYOFNJY3g4M0I0Wm5SeStyZ29FSGNlNysyODBZRUdOU014VnBGME9kWm4wOTBPTzVkUDA2NHBZUkd6N2lIT0kvMUZiQUkydm5sQlUyMVlGYS9vcVhvRU13amV4TVBibmJhTXZRUFJvVXdEelpWOFFBNXJ0NnhlL0dONE1rSW56clpQVGxyQVQzaVp1bmpqQ1dKaVVKSHh6Ymk3aDhPRVVWZTFFYWdCSk5VMFpaNEcwL1VFN1dySDVpR2ViMWxVYmhYankwcU1SL25oSGNTM1ZxRm45YjZITy9ucGZtT1llekdqY3YrN1pFQ1RxT2t4ZmJGNHVuanR2MWNjTVpzSlpYYWJ2Q2RYZzl5QWFxZG9scUUzT2dlVnZMZTlzSEpEUmlSTThhZ21tL0hiaUw2WWVaVzVNWmVaSjdVL0NiUHppRnN5VGZmT0ZYNXhuNkQzaGcraFVORXN5eU5WRW8iLCJtYWMiOiIxMTEzYTkyMWExYzUyZmQyMjZiZWMxYzRiMmE0MTE0M2EyNWE4M2Q2YjMyNmY5NGEzZDBmNTIwZWNlZDM5NWIyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:30:11 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1012234555 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>24</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1012234555\", {\"maxDepth\":0})</script>\n"}}