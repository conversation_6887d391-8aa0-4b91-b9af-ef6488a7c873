{"__meta": {"id": "X970f32f7aa3b1986b4d4479266e1b7e8", "datetime": "2025-06-08 00:58:03", "utime": **********.634453, "method": "GET", "uri": "/customer/check/warehouse?customer_id=7&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.080073, "end": **********.634478, "duration": 0.5544049739837646, "duration_str": "554ms", "measures": [{"label": "Booting", "start": **********.080073, "relative_start": 0, "end": **********.554436, "relative_end": **********.554436, "duration": 0.474362850189209, "duration_str": "474ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.554449, "relative_start": 0.47437596321105957, "end": **********.634482, "relative_end": 3.814697265625e-06, "duration": 0.0800328254699707, "duration_str": "80.03ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45176968, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.014890000000000002, "accumulated_duration_str": "14.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.593452, "duration": 0.013560000000000001, "duration_str": "13.56ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.068}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.6189141, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.068, "width_percent": 4.164}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.623772, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 95.232, "width_percent": 4.768}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-980276592 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-980276592\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2143324884 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2143324884\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-469114340 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-469114340\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-21457974 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i31pha%7C1749344276347%7C8%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InRaYWw1ekoxYUVwam45b2N4bUREUHc9PSIsInZhbHVlIjoiRTF3cURHKzU5WmVwRXdQNmQvUnZmUWxCRXlCN1QySzdDN0JObHRiT0E3TEkrQ2YreTdxTndhS2I1dUJEcUNVWmR3bVRSMEo1Z1IvcUhxK0hnd1lzeUUzb0htL1BJNnVuNEt4YXZ0RmJtcnUrYXVLQnRBSTNCSy9lY2R3YVI5eDl1Q1RRSGMzTUdGREJPU1d6UGlpSUloRFdJZVAwaW9NRHVyMXFOWVVGRU5aRlE4OHFGUkxyeHdwVzlkWXJVTmxhQUdXSU9NNi9hNnhxKzFFTk00L3VUMDVRMWdRb1JFVWV6WExjNTd4L2VlSW9JcWlGMkxoTUVOc0pId1pkcXdQRWhEM0dCbm56RDF3Z2ZleksxRksyVEJuZldNb1hHbXF6d1QrTm4rZm9TamNsWC95eDdtQWZsQi9EQjhRYnFScmRpMnpOTkNHNm0yMk5tWWdKUlc2MHRjVThPL0FvSXBXVmIrZ0Njd0FsaVFpRHdkMHoxV3hKQXB1Q0ZGODFVZGZTVjlRdzdEdE56a25SNU1oaWx1V2xCN3FNVXF5a3pYaWZ2T1k3YUdoZnpic3RlYXpXRjJiWk9YVmVjNGtGT3dLRWlvRnBYUlljaDRtSGJ1K0lkZEY5RnRkVkJZYXBvS0xjNCtWcUdSNHRlbXNYbStkZlZ1a1ZxZ3lCdm9KM0dVbjEiLCJtYWMiOiJhZWY3YTk1MjA2MWM2MzViNGQ5YTIyZjI1YTBjZmQxMzBlMjE2MGQwYzBmYmRjZTNiNzA1MmExNzBmMDM3MzEwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InB5QnB2NVNkRVp1Nnl6RVRxZm1tVmc9PSIsInZhbHVlIjoiOUVoaW5ZRE9LZDdCd2oyT3Q2ZWI4Yk9iSDJqeUNjTVFuOHhnRGlqNW5EWnpvZ2NSQXFrS1ExVm5UV2ZQWTVaY01DbzRuZkpsZnZqTDAwZ3NtN0xKUXI0MU96b0R2UGl3cHVwSU9wKyt3bDlQbTJzV0tyaGxPWC9QQUZmN1EzNlpEVTZ2a3lXVUZEKzdOMjNoeGhBbFpaT1dKeXBLS3dJYlUxeXIxRGh5aW5EMndwYzJFczkyaXRXb3loaGVDQ1RRclNqamNyeFlvaVpDdkM4Y1l5RytEbXo2QnQvOUVkTjlDU05aVUN4dlA3UThHTFNyU1JmNVFaSVBZNUtXQm56N3Q2L2FPYmZHaTdhU2ZxSVFUZTJOSzdESXRmREt3QVFSM0t2LzdSVWU0dFYyMndDZ3ozKzREMTVSSDVPaVZwR3g5NVhWWFJzY1pQcmFsUUMrUUorY21scXVEWHBFNHlNL1piOXM2WUY0a2wrWEttQ1V5Um90TlV4angvVTZ0ZUJSOTRzbk9oR05sRGkwdllaUDhYTXFxOUlpVDZZMXdFR3U0YmIwNldvUXRUUVVkMndwNnN3TkttSVJQd3p0VzR2dFV4d3AxQ1hmbXBLbFJ5QW5IcVVhTzJYSHowa3BuL01MZnRGWWxsejNFNHlmeWpXckRLS3M3UDRSTXJvYStTYzAiLCJtYWMiOiJhZTJiNGNmNzg4ZjYzMTIyM2NlNjk0MTNiMjQxN2ZlZTE4NGZiNjdiNzU0MWM1MmVhNGE2YzE0MGMxMjUzMjQyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-21457974\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1842797286 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1842797286\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-709460307 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:58:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii9yelQyc05OY0V4VHlmR2xZcGNudkE9PSIsInZhbHVlIjoiakc0bmZmMFp6RVYxUGJlb0NOQy9CamJydGZWUTYxY1JFWlp3dVdTYjZUNEN1MSs4TERKVUZvK2NmZmhJZzI0TWloVUhPZHdmNGdUVDg0MytFZUdnVmZCbXRiQ2pFcWdkTUZQTmZmeGlaOUluUm9oaGQ5UGtZMUpENVliQ2xtalJZbzcvS2Z2T2xKM1dmWUxjOXFYaFp5alJIc2lPcVYvR2g0NFlvdUp4UDZjV3RuU1JSZlV3YmdBUmVDamJ6WlcvM3JGWnlzdTRIMUhvVlR3TDNkSE9WcENlcGIwbXo4OE5BQmsyZkpQNEdBMjZWK2pJb3pWQTZKaDhLb3dJWDhZS3pOTlA5UmtuNkJWZm5IT0JXckVSV2Zicnp5MS90eVI5YmpZQUpTaFpGWEVFOWZ4WDVISU0rdTYxYjQvbU91RUdrQmRSVGw4YjNTN0ZNdkFFc3MrR3J1ZUVWb05xbkZzVElWUDVxWXF5UERyQlhHei9maXdCT0tkdllEbFJ1Z3RQclRaTHlwQURIVFRWUGJEWWpFVitXTnJ4Y0ZXQnVpRWFVY1NVUGFSSjZQekZhRE9HMXRXRTFIOE1rUDFIRTFjUXE4RDI3WVBCYVVPQk5ZOFJBYjIya3JMTThDSlp6U3RGcVM0dXQ0SmtHaTRibEFXcysxeU9hNEZ5YnZWRzlsWm4iLCJtYWMiOiJhNThmYWFmNTAzOWZmNjQyMzdkODNiNDFjNTFhNzU3ZDkwNGM4MDkwNDFiYzMzYzA2OWFkZDYzNzZmNjlkZDdhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:58:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImpDVWw3cXR4UnpraHhiaG50UHlXS3c9PSIsInZhbHVlIjoiMlduOHIzNHFjNjlyY2U5cldYS3JKZlQ2SXhuZ0swbW9CRklINlVpcHlMK0FVZUltOUxqMkZDZGNPUzBDa3dGallnSVZaaGJqaWNtaFc3NjZrbEJqdUJzMXhVMUt0TkdkWVViU2tqU3MxMGxiUHFVTk9zWGpVSmJnSHppMjNZR05BdndmekZRR3d4a01MZ2syQ0pIb0xTUGdKNDIxSEhiUSs3M2JMKytBVnBhWEFtc3BDMVk4Snc5aWUwTGJ5WXlWbXh6TytRWHB2R2orWSt4aEMzLzVIdGZWU3hYUTRkTHhlYlQ1bUdqbDFiUUJvTVlDTnM3bnZ4elNrNU9mV3NxcDVIdTlnd1E3TEhBaDdCK1ViTW01OWxNb3lnRFExVHdRNVJONXFZY0hsVVFRcVB2bVdQL3hXLzdwNmF3RDlIcTE0eVFVUnptMCsrM25zcFg2SXVCc1pyeHpESHFsdmgzaEEzb2dtK0dPbVJKdERFdS90bDdPVWRhUWVXQzBhWklYWjFDT00zWDZML1RkdFJXVllxMlZSY3hIRkJJZERzSVlFT29TckdZK3N3c1diTzN0RDFWcjdDaWN3YW9UWVBxWmVpOEVkYksxbFVEanc3RnZqV3lteEdUeFlReHZJeTdhTVVxVXA2bCttV2I1ak1YcGNkVzR2T3JPbW1rczhZRGYiLCJtYWMiOiIwMWE2MzcxNzA3MDkwOTM0YTBjYTIzNjFiZGM0NjQ5ZWY3MWMzYzcxZGM2YjcwMDY1ZTM4Y2ZkYWE3NmIyOGE3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:58:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii9yelQyc05OY0V4VHlmR2xZcGNudkE9PSIsInZhbHVlIjoiakc0bmZmMFp6RVYxUGJlb0NOQy9CamJydGZWUTYxY1JFWlp3dVdTYjZUNEN1MSs4TERKVUZvK2NmZmhJZzI0TWloVUhPZHdmNGdUVDg0MytFZUdnVmZCbXRiQ2pFcWdkTUZQTmZmeGlaOUluUm9oaGQ5UGtZMUpENVliQ2xtalJZbzcvS2Z2T2xKM1dmWUxjOXFYaFp5alJIc2lPcVYvR2g0NFlvdUp4UDZjV3RuU1JSZlV3YmdBUmVDamJ6WlcvM3JGWnlzdTRIMUhvVlR3TDNkSE9WcENlcGIwbXo4OE5BQmsyZkpQNEdBMjZWK2pJb3pWQTZKaDhLb3dJWDhZS3pOTlA5UmtuNkJWZm5IT0JXckVSV2Zicnp5MS90eVI5YmpZQUpTaFpGWEVFOWZ4WDVISU0rdTYxYjQvbU91RUdrQmRSVGw4YjNTN0ZNdkFFc3MrR3J1ZUVWb05xbkZzVElWUDVxWXF5UERyQlhHei9maXdCT0tkdllEbFJ1Z3RQclRaTHlwQURIVFRWUGJEWWpFVitXTnJ4Y0ZXQnVpRWFVY1NVUGFSSjZQekZhRE9HMXRXRTFIOE1rUDFIRTFjUXE4RDI3WVBCYVVPQk5ZOFJBYjIya3JMTThDSlp6U3RGcVM0dXQ0SmtHaTRibEFXcysxeU9hNEZ5YnZWRzlsWm4iLCJtYWMiOiJhNThmYWFmNTAzOWZmNjQyMzdkODNiNDFjNTFhNzU3ZDkwNGM4MDkwNDFiYzMzYzA2OWFkZDYzNzZmNjlkZDdhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:58:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImpDVWw3cXR4UnpraHhiaG50UHlXS3c9PSIsInZhbHVlIjoiMlduOHIzNHFjNjlyY2U5cldYS3JKZlQ2SXhuZ0swbW9CRklINlVpcHlMK0FVZUltOUxqMkZDZGNPUzBDa3dGallnSVZaaGJqaWNtaFc3NjZrbEJqdUJzMXhVMUt0TkdkWVViU2tqU3MxMGxiUHFVTk9zWGpVSmJnSHppMjNZR05BdndmekZRR3d4a01MZ2syQ0pIb0xTUGdKNDIxSEhiUSs3M2JMKytBVnBhWEFtc3BDMVk4Snc5aWUwTGJ5WXlWbXh6TytRWHB2R2orWSt4aEMzLzVIdGZWU3hYUTRkTHhlYlQ1bUdqbDFiUUJvTVlDTnM3bnZ4elNrNU9mV3NxcDVIdTlnd1E3TEhBaDdCK1ViTW01OWxNb3lnRFExVHdRNVJONXFZY0hsVVFRcVB2bVdQL3hXLzdwNmF3RDlIcTE0eVFVUnptMCsrM25zcFg2SXVCc1pyeHpESHFsdmgzaEEzb2dtK0dPbVJKdERFdS90bDdPVWRhUWVXQzBhWklYWjFDT00zWDZML1RkdFJXVllxMlZSY3hIRkJJZERzSVlFT29TckdZK3N3c1diTzN0RDFWcjdDaWN3YW9UWVBxWmVpOEVkYksxbFVEanc3RnZqV3lteEdUeFlReHZJeTdhTVVxVXA2bCttV2I1ak1YcGNkVzR2T3JPbW1rczhZRGYiLCJtYWMiOiIwMWE2MzcxNzA3MDkwOTM0YTBjYTIzNjFiZGM0NjQ5ZWY3MWMzYzcxZGM2YjcwMDY1ZTM4Y2ZkYWE3NmIyOGE3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:58:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-709460307\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-782074745 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-782074745\", {\"maxDepth\":0})</script>\n"}}