{"__meta": {"id": "X5902f7e071ee4eb1bc04cc79cf0a29ba", "datetime": "2025-06-08 00:29:47", "utime": **********.850574, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749342586.75247, "end": **********.850607, "duration": 1.0981369018554688, "duration_str": "1.1s", "measures": [{"label": "Booting", "start": 1749342586.75247, "relative_start": 0, "end": **********.699801, "relative_end": **********.699801, "duration": 0.9473309516906738, "duration_str": "947ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.699821, "relative_start": 0.9473509788513184, "end": **********.85061, "relative_end": 3.0994415283203125e-06, "duration": 0.1507890224456787, "duration_str": "151ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45576240, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00721, "accumulated_duration_str": "7.21ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.772072, "duration": 0.00474, "duration_str": "4.74ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 65.742}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.801172, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 65.742, "width_percent": 13.037}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.820117, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 78.779, "width_percent": 21.221}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2070918411 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1834 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749341283326%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjRmUWx5eVZSQ2lOOHd6eVhKMkMzU1E9PSIsInZhbHVlIjoiVDZFSjNFalVEOFNmQ2x5N1dpMCtlSStYb0xKVUhCdXdmVDhnUnBiZGZ2cmZaMmpBcWw5cDkxeGcxdXROeThHY2twS2doVUFLNG44bnlxNjlMa1liZFF4bWtCUHBPZjZmUnRCZjhsWkdoVjR2ZDJ2Wi9seWhjSVQ3UDUyc1owYjdpTG9IT3hkbzA2d3hhSHI3MkRLeEFwcjk5bGN4b2Z6U0MrRHVhQURVQzFma0l2Uzhya1c3OXY0WmFTUXFiMHMzRUtrcGRlbEJRQjFwYkt6anM2dERSbllIK1cwdWFBOWM5eWlYQXI4M3FxenM1eHpDSktmcXZ5VDFHK3YraUVnMTl4N1BVU0FUc2liRkpWVjV1OWdLQmVtbDBwQ2hUYURFYlFyUk1yOU1GUW5oVjNXRHg3UFBLUjlUYmo4SXhGYTV3blMycWpwTU1OdVZXSnFsMzJyeVpVM2RKUG5HdVhzN25Vb0paT1kvN05UQVZUT1d5Tlk5T0R6dDNmWXMzVjh0YUVWd2RoYXFJVTBIbDJ1UzNQVmRrZFVTbStNTEpDNVhCR0pkbHN6RVkwcVJyRm1nYmJKdFM4clMvV0ZCdHZGWFQ2MFpkOENQSFdtd1haclBDYkdZOU9GbUhEUmQ4SThpdlp4NEh4RTNBNTBxbzEwbUZyM0RhcE9obFM4cGxCeGYiLCJtYWMiOiI4Y2MyOGVhNjM3YWM1MjA4NGMyN2U5ZGFmMGZhYjRhMTU5ZTJmMjhmNmM2MDJhNTVmNTZjYTBhYzA2OTA3Nzg0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkZrN3I4NnFZU1RYbTZMZUYyaUNXelE9PSIsInZhbHVlIjoiTUN5c2U0M0R3YUpLNE1NOHIyYi8zbVpKM0RIbVNwbjdmQnJWVmdHdUpnWjFOcSswVy9FYXVHTjJQZVdBazBWYjZCQ0hJWjNkbURybUNocExpbXY0YnJqMFVIeXA5dmdyUGNRVG8yOER6U1JMQUVUZGZwemdiSEk5SUdqVnhqRy9GVUowWWhZQ2tWSk5MN1ZXbHY2UWlyS1BtZUdMSTNLNFBHUjJDMTZlK0Q2TjJhM2ZKYjZ1MjJOYUFlTW1kS0U1N3ZwZ0hTcnN2Nk04ZmtDREx5TFk1ekowUTA0U2VBY2tvaWh1dGI3a0tYRzVOMGZjU0dIc0s2eXU4QVVlcHJNSi9qR0lrWUExWG9zckczcSswdVd6Ty8wQ0wxM3hzVVNYdExPL2tsSFc2V3dCWk9rbU0zVmZmekhnOUlMTnYzaGlBK3ZoTGNhOVZSV2dVQ1BoYXloeXdjbTZDeXhSVGNIT20xL05FK0dpVm5pNUZHckxvamFUa3E5TnhKQUpZNW4rbE9iRktveWoxUVB3eFR3SXV6d2lBUEkvRFZCMTcrTmhhbkxJWWh5eEpYOEtKSktUN1FkVUpJWFlZU1B3aTlwaWxRYklnNFZVbkdPRWk4QTRabnNjUjJ5MDRDVTAxeVkwWnlCWWJUQTlKc1NvQ1NCOE1TVTlxcWlKVWZXakJMSE8iLCJtYWMiOiIzZTJmYTNjMjcxNjYwMzJiMmRkM2ExOGEzNDFhOTQyNGI1NWQ2MWQ1YjJhZjRkOTZjMzRiNDg0ZWU0OWMyMDBlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2070918411\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2142113302 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:29:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFocEhuOHg4cmJneFM1TWpPWFpWcWc9PSIsInZhbHVlIjoiZHFOYWozakFXcy9zMFBKSlBXbm5meHdpUVVZR2doTEZMMHNvMGIvSC9YUGdORlRRWVd6eGJBUDJQTGJKYUFMa2RvTjlTdzFrZUxSbCs5VGd5THdkNWZDZVh3TXR6Z3Y5MVVxTzZFWTVTQ2swRDVBdVBadk1jUTgrNDRkY2VJWk9oN2wxcTFBblQ5amk5UFJCd0VPRjY1dTZjbUtucUxwNmFETnlRMkxPazMza2dTTHRpRXJSYlp2c0hRZTlWV3N5ajk0YkhYeGdHRlJ5RFB3c0FSVUNKU2RQRStmZEI1a3IzVm0xdWFqUmhqcFcxT202ZjBtQzhBZXB2TitHRXRqMlVHRTU0NFV6ZnRydzJqb2JUQTJKVzFidlNYK1VDUEVFQlRNdFFKSjF5NG9KSDJBTE5idzNjMXIzdTR1NnJ0Vk1lOUY5Q0pEYzhvc2pnTHR2UWlRZDB5UjZWZGVPSGVkdjR4V0szdnNnQTdRZTZQbFZiZTVHVGZzaGdRN2xPNUQ4bkhsdzNnbzRCSm14NitEbGQvMSt4RWZzdFg1T3lmSkRQYWw0RTY4dGduejYrckdDZ3FhaVo2YnZJL3N3SHlhbVYxdzE3YlU1Y2FUanBOdnlCdGlZWUxPa2xsTXpJTnJwamg3RFo1c21EL0tNY0pQMC9DdWtDMG92dG16ZWowVnkiLCJtYWMiOiI4NGNkY2ZkNmUyMmQzZjdjNGNlYzhjN2EzODA2ZDJjMDBkMGM1MGQyMTEzM2VmZDdiYmM4MGI3Njk3N2QyZjVmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:29:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlA4ZHhSSDBwampLa3l0aXlGWkJwR2c9PSIsInZhbHVlIjoiM1pEeDhpbmE5UjhWNGdkMjlObnI4aUtWMlJhUGZRWlZvMW5FSFBhL1dRZmw1Y3ZFVlc1ajYxaWxIUWRhU3lkdlZCbXFMM0dMYkY5SXlvejl5cm9ta3JzUlFtYzVydnBENEdTT0pVQnJqTFRrZHpabkpjUmFoeWJSYitqVi8xNmZ5cEpaVEQ0ckl3WHloTXpmWVFWWjdHWmFuYnZXRU5lVDU0OEkvZ3l6amxsWEFXenZrLzFHVEROdzZGaWVRN0lVL0I2MkJhOXdXbXkyRnB6MTJVZUt5RisxSS9obVd3dU5CR2l6bkg2MnVvaytLOWcyZDRsMXF6S1prbzh0ZkVXQ2VTTU1FUTVhd3JLdHE3WEtaNjBmT3pRYjg1bmpybUtPTnduamoxYk9BZWtZaGQ3R0NrKzdCSGZTcjRTTkJPRitSZ0lyRjFxVzJWYXlCSzQvSTNReFVBUk5RLzluWkdCUk11Q1I5YWhMbWp3ckRNYnhPVUJiaENOdU14NXo1UjdWOWlTVlh5R21EbERuNjRtNGxmbTJYM0lyV3hxNGJOZ3RUYmMxRFIrYWEvYUFoRHFXZXA3enpQSjU4RXh2WEprR1FtSjdCUElibUpkNkNjRXFHUTBGc3Y2UEtNdVdyWFlDSUNPakhBV1NmZ1RvakhvN1B1aURoMkZFWFRBZFp1MzgiLCJtYWMiOiI4YTFhNThhMjJhMDNhYjI0NzU0MjZjODQyYTVkYmViOTM3M2E2ODdjMzk5NjY3MDQzZDJiYmY0YzljMGUzNTYxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:29:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFocEhuOHg4cmJneFM1TWpPWFpWcWc9PSIsInZhbHVlIjoiZHFOYWozakFXcy9zMFBKSlBXbm5meHdpUVVZR2doTEZMMHNvMGIvSC9YUGdORlRRWVd6eGJBUDJQTGJKYUFMa2RvTjlTdzFrZUxSbCs5VGd5THdkNWZDZVh3TXR6Z3Y5MVVxTzZFWTVTQ2swRDVBdVBadk1jUTgrNDRkY2VJWk9oN2wxcTFBblQ5amk5UFJCd0VPRjY1dTZjbUtucUxwNmFETnlRMkxPazMza2dTTHRpRXJSYlp2c0hRZTlWV3N5ajk0YkhYeGdHRlJ5RFB3c0FSVUNKU2RQRStmZEI1a3IzVm0xdWFqUmhqcFcxT202ZjBtQzhBZXB2TitHRXRqMlVHRTU0NFV6ZnRydzJqb2JUQTJKVzFidlNYK1VDUEVFQlRNdFFKSjF5NG9KSDJBTE5idzNjMXIzdTR1NnJ0Vk1lOUY5Q0pEYzhvc2pnTHR2UWlRZDB5UjZWZGVPSGVkdjR4V0szdnNnQTdRZTZQbFZiZTVHVGZzaGdRN2xPNUQ4bkhsdzNnbzRCSm14NitEbGQvMSt4RWZzdFg1T3lmSkRQYWw0RTY4dGduejYrckdDZ3FhaVo2YnZJL3N3SHlhbVYxdzE3YlU1Y2FUanBOdnlCdGlZWUxPa2xsTXpJTnJwamg3RFo1c21EL0tNY0pQMC9DdWtDMG92dG16ZWowVnkiLCJtYWMiOiI4NGNkY2ZkNmUyMmQzZjdjNGNlYzhjN2EzODA2ZDJjMDBkMGM1MGQyMTEzM2VmZDdiYmM4MGI3Njk3N2QyZjVmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:29:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlA4ZHhSSDBwampLa3l0aXlGWkJwR2c9PSIsInZhbHVlIjoiM1pEeDhpbmE5UjhWNGdkMjlObnI4aUtWMlJhUGZRWlZvMW5FSFBhL1dRZmw1Y3ZFVlc1ajYxaWxIUWRhU3lkdlZCbXFMM0dMYkY5SXlvejl5cm9ta3JzUlFtYzVydnBENEdTT0pVQnJqTFRrZHpabkpjUmFoeWJSYitqVi8xNmZ5cEpaVEQ0ckl3WHloTXpmWVFWWjdHWmFuYnZXRU5lVDU0OEkvZ3l6amxsWEFXenZrLzFHVEROdzZGaWVRN0lVL0I2MkJhOXdXbXkyRnB6MTJVZUt5RisxSS9obVd3dU5CR2l6bkg2MnVvaytLOWcyZDRsMXF6S1prbzh0ZkVXQ2VTTU1FUTVhd3JLdHE3WEtaNjBmT3pRYjg1bmpybUtPTnduamoxYk9BZWtZaGQ3R0NrKzdCSGZTcjRTTkJPRitSZ0lyRjFxVzJWYXlCSzQvSTNReFVBUk5RLzluWkdCUk11Q1I5YWhMbWp3ckRNYnhPVUJiaENOdU14NXo1UjdWOWlTVlh5R21EbERuNjRtNGxmbTJYM0lyV3hxNGJOZ3RUYmMxRFIrYWEvYUFoRHFXZXA3enpQSjU4RXh2WEprR1FtSjdCUElibUpkNkNjRXFHUTBGc3Y2UEtNdVdyWFlDSUNPakhBV1NmZ1RvakhvN1B1aURoMkZFWFRBZFp1MzgiLCJtYWMiOiI4YTFhNThhMjJhMDNhYjI0NzU0MjZjODQyYTVkYmViOTM3M2E2ODdjMzk5NjY3MDQzZDJiYmY0YzljMGUzNTYxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:29:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2142113302\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-14******** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-14********\", {\"maxDepth\":0})</script>\n"}}