{"__meta": {"id": "Xb0b93e4867d7b81f44e70e0b1734f166", "datetime": "2025-06-08 01:14:08", "utime": **********.035312, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.423297, "end": **********.035333, "duration": 0.6120359897613525, "duration_str": "612ms", "measures": [{"label": "Booting", "start": **********.423297, "relative_start": 0, "end": **********.907811, "relative_end": **********.907811, "duration": 0.4845139980316162, "duration_str": "485ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.907822, "relative_start": 0.4845249652862549, "end": **********.035335, "relative_end": 2.1457672119140625e-06, "duration": 0.12751317024230957, "duration_str": "128ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48123160, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.026869999999999998, "accumulated_duration_str": "26.87ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.952146, "duration": 0.023129999999999998, "duration_str": "23.13ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 86.081}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.986805, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 86.081, "width_percent": 2.159}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.008754, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 88.24, "width_percent": 3.089}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.012071, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 91.329, "width_percent": 2.866}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.019624, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 94.194, "width_percent": 3.759}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.024416, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 97.953, "width_percent": 2.047}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1691345636 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1691345636\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.018159, "xdebug_link": null}]}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1120614488 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1120614488\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-487447501 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-487447501\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1262451239 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1262451239\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-343375807 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1939 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i31pha%7C1749345246637%7C11%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InhQdW41d1M1T1FleXk3V3RvVVRRN1E9PSIsInZhbHVlIjoiZUZkbGQ1MXR4UHd3aUk2UHhLS0hBSkNPcnJoNzhNNVZ4T2hKVWtacEl5cVNrZTBXUkhMU3ZaY3NVK29nYUsyb0FCSWNJVmJpU0M1R3FQZk1wSWdYZ21HZCtSZzQzTzJhdlhBQUducWxDd1JzemdPQ3ZZNFFSUlZtMmRKZlJxck9UZTh2alpDWUd1Wi8rajBtaGU3b3NOc2I4eGdYbWRCVE4rWUtuandvY2p5TEFLSVNNT0diUzlIekhvVWZveGVDNDdKRHZ2UjBiVDVSNkd0aDEwSnU5bXc0SCtQbWtxdGlMeWNjUzlVVjZoSEtuOGExb25NRkt5eHVHZ2VyUGpyUldUcDlEaTlRZ0xQSEdpV3pTbWhMSlVWd0VlbzdqWXhIdnlkWFIxVEtVMytzRHA5MEtHRVpaYUdOcE1VeHlEL1dSYXY5UjZja0JuTkRRN3ozU3FFSmVMVkVtbE5yQTJQcE5aNkR0eFZaYndKK3Y5WlV3S1c2OVNHR09LSWR0UEVGb3ZjcTNYNlYyYVk5QllMUi9hMVRWQ0thNTVzd0ZPaEllZjlnQlNTK1BIWG83RXdHZHFtakxzVWJ5eU1LTERiaVhhaVU0bXFGVGg2a0p5d1NzbGpWMEowRDhkWUptbmx3Q045MkZGQ2N3Wi91eVZKNDg1em9GVlRxS1QzdktvQ0wiLCJtYWMiOiJmNWUyZWNiZGM1ZDIyMWJmOWU4OTYyZGJhZDE3OTA1NWYwOTliZGI2ZThiZTVhYWI5NjQwNjFjOGQ5ZDgxZmJiIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Im51amZyQm13NHVRK3lyTHdsb2hoMmc9PSIsInZhbHVlIjoiYTZUdmkrb3d6SXhTU3hSOUpKUlNZb2t3N0ZBc3dUa1ZMSTFRS0U5NUFyLzZKT2xpZXViekRKVDVuQUI4bU1IbWVOMWJiOFhyTVBDcFRDVUJDUnNHMkJtRmpkeVE0WFFLTnNMdGRsaVZLZWQxazR5NnQrZXRnMFpxVkFmUjNrTTVxZ1FjOFNOZytuRDhQMExJdC9sTUVSYW5BY2NBdEN6U1dXRVVHcHVnMDZkNWVENitycnd4amliYTN3NndLS1g0UEtGODBpU0dLV3ZHOHp5OStUdGQzVVBoUGNQRE5YcWVodHJkQjN6T3FPckxRdHdlcmoxK0lscDZOaEpmSzNTMVh3VjB0eUIwTWtJY1ZjMUdIYm1aYlRoczZmaUdPM2Jpd3B6eFYzOUhIYXpqMlptYUdpd2FJa3pESUw2T2dIR1AvS25IRFhkWG8zcmFwczhvSWdBT2hObHBoZ29JZVJoSE1KZHJoODlsODA3akxRam1XRE96N3o3bzBid0d6WDEvaFk3a01GdWlEQ0xnSXhIc2gyZkY3cHZKS0pZMmJFR2xnWi92VWRGb29CcHNGMlczWTd3eGlxbU94Zm9pMkdkTjhpcTV6ekM4d3NyWkZibHNaTlJXa3BQV2l6dHRyZWI3d05TYTBKK3dmUXBPRFhUVU9pMGx6WFMwWlMvYkNheHciLCJtYWMiOiJkMGMxNDkxMDFmMTkwNThlZWMzMmM0NTY5YjhmYmE3N2MwYTFkZDQyYzJlMjhhNGYxMWIzYjczNTEzOWUzOTNmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-343375807\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2110319432 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2110319432\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1044834655 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 01:14:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFzdklqVTBab3RlRkNTMUxEWjBKcnc9PSIsInZhbHVlIjoicFZrdnlpUG16NVFEeEZGbEMyemRaRFUxNkdnbG0rbGMvTG1tS3kvMlc0STVtYllNYjJEdnR3QVZ1S1RrOUNmZHFrdVhtYVZYTlovVEI4b0RaMHVjZjh4clM5UEFhSXdKdW5YSEE4N2Flcm1VSm1BVnVKZi9uN1B1YmpDODVySGpHeFQzVDFiam8rVXRjZ0poNXhFZGg2YVBxazJKL3ZHWE5VSFBETzBCQlc0a2J5dnd1VmZsSXZqblp6QjdDZDFINFpYcHZjOW5Sdm5qTnBwNzBLMUVDRlNvaWZRNG8zR2QvQWxYekNMVjZYY0pCUVRjM2M0VHk4UjhSZ0tzcGNpUWdHeTMyZFRlVHhHUS9wQjRRRnRURHdFeDgyVnlWa2Ewc01GcVk5VEY3VEdvZml1aGdxYWdvKzhjam1LWlZiTGE2MFNteUIrWjJGRG9pa2dkZ0VEdkNVUlRBQjRhTytPYVM1VUU3VVl2Qm0xTDR0NEt4Lzh4c0hKTmw1bmxjV2d2S29zTUJYTkNyY2NKWXYyK2FBMVZKWUU2QUg1bmh0MU85bmF3M3BKWjcwbzdZdTBhTk5KR0FZMUdoSEpSUnpSRWlaYmpnNVpWWlo4TEttNGJhc3h1cDRPakFWempES05BSW9KeW9DV1k3QU1Ma2hmdldVTEcvUFVhbHJnenVZVVkiLCJtYWMiOiJhOTJjZDMxMjIzOTRiMGI2NGYxMDgyYTMxNGY3OTZhMGJhNzI4YTU3MGFjYjY0NTFmMTJkMmIyNzZmZDUwZWY4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:14:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImlFQnBhazhvQkR4RUlIcGxJOEU0VEE9PSIsInZhbHVlIjoieVFWdDBLVWJFd0FFSDNXV1JiR2N0NERyWWNZNmpld2VUaGZEQXMyY2VidWlNNVc1VERPMUc5ZUpLaVk5STJUZVY5dVlVVGZyTTBtR0E2THdKUnFwNjdENkM0S3VYTmdjYXY0OEVDUDk0aU1hVkUwYTlPRkp6L1F4VEpuWW05YzNUV3dIUVZsL29UalFSOHlSTnZrbGtXeHY5ZXlpbU5KOGpmWHYreGkxbkFEZWtIK3RwNHpIRktBZEc4QW9JTTdvV2dUcjc5STNhVFZyYTNxVTF4MmdQVGdCcUJ0UWtOTWcxS0dIM1lWNi9acDErUjlPc0gzMlowVmJJN2c3N1ZNNlNZLzBNL2J6dGdPbWhoNS9XVkdEYnR6anZDVHV6QUJQUkkxWG56aFltaWdQUXZxWC9YTUszbmNkS0JPVlpWeFhLZ2RJZEtZSjdWMy9CYmkvVWFaWGNKWWdnN3UrcFBxOFUybUtNQlZ2cFpRaG56SjN6aVVZMEdNQUsxd3Nnb0s0UjRtanJxeTM0bTJaS1UyWHAzaXBzaXBueHowM1dUSlJTUFUySnZ3alhHdHp3Um04WW53UnNZaWxJaVRlZndrSHRYMHR1NzAwcTZiZlBkcyt3QUhRcXVZZVh5M1RnMlk4TFljUGt5VjZhcHcrbVQrSStaQ1Y1bm9qeTVGVUl1ZngiLCJtYWMiOiI3NDdmYTc1NjdiYTE0MTJjMDNjNjZiYTkwNDk3Mzk1MTRlZmVjZjliNTMzODgxY2VkZDIxZGIxYTFhNzVkZTRkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:14:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFzdklqVTBab3RlRkNTMUxEWjBKcnc9PSIsInZhbHVlIjoicFZrdnlpUG16NVFEeEZGbEMyemRaRFUxNkdnbG0rbGMvTG1tS3kvMlc0STVtYllNYjJEdnR3QVZ1S1RrOUNmZHFrdVhtYVZYTlovVEI4b0RaMHVjZjh4clM5UEFhSXdKdW5YSEE4N2Flcm1VSm1BVnVKZi9uN1B1YmpDODVySGpHeFQzVDFiam8rVXRjZ0poNXhFZGg2YVBxazJKL3ZHWE5VSFBETzBCQlc0a2J5dnd1VmZsSXZqblp6QjdDZDFINFpYcHZjOW5Sdm5qTnBwNzBLMUVDRlNvaWZRNG8zR2QvQWxYekNMVjZYY0pCUVRjM2M0VHk4UjhSZ0tzcGNpUWdHeTMyZFRlVHhHUS9wQjRRRnRURHdFeDgyVnlWa2Ewc01GcVk5VEY3VEdvZml1aGdxYWdvKzhjam1LWlZiTGE2MFNteUIrWjJGRG9pa2dkZ0VEdkNVUlRBQjRhTytPYVM1VUU3VVl2Qm0xTDR0NEt4Lzh4c0hKTmw1bmxjV2d2S29zTUJYTkNyY2NKWXYyK2FBMVZKWUU2QUg1bmh0MU85bmF3M3BKWjcwbzdZdTBhTk5KR0FZMUdoSEpSUnpSRWlaYmpnNVpWWlo4TEttNGJhc3h1cDRPakFWempES05BSW9KeW9DV1k3QU1Ma2hmdldVTEcvUFVhbHJnenVZVVkiLCJtYWMiOiJhOTJjZDMxMjIzOTRiMGI2NGYxMDgyYTMxNGY3OTZhMGJhNzI4YTU3MGFjYjY0NTFmMTJkMmIyNzZmZDUwZWY4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:14:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImlFQnBhazhvQkR4RUlIcGxJOEU0VEE9PSIsInZhbHVlIjoieVFWdDBLVWJFd0FFSDNXV1JiR2N0NERyWWNZNmpld2VUaGZEQXMyY2VidWlNNVc1VERPMUc5ZUpLaVk5STJUZVY5dVlVVGZyTTBtR0E2THdKUnFwNjdENkM0S3VYTmdjYXY0OEVDUDk0aU1hVkUwYTlPRkp6L1F4VEpuWW05YzNUV3dIUVZsL29UalFSOHlSTnZrbGtXeHY5ZXlpbU5KOGpmWHYreGkxbkFEZWtIK3RwNHpIRktBZEc4QW9JTTdvV2dUcjc5STNhVFZyYTNxVTF4MmdQVGdCcUJ0UWtOTWcxS0dIM1lWNi9acDErUjlPc0gzMlowVmJJN2c3N1ZNNlNZLzBNL2J6dGdPbWhoNS9XVkdEYnR6anZDVHV6QUJQUkkxWG56aFltaWdQUXZxWC9YTUszbmNkS0JPVlpWeFhLZ2RJZEtZSjdWMy9CYmkvVWFaWGNKWWdnN3UrcFBxOFUybUtNQlZ2cFpRaG56SjN6aVVZMEdNQUsxd3Nnb0s0UjRtanJxeTM0bTJaS1UyWHAzaXBzaXBueHowM1dUSlJTUFUySnZ3alhHdHp3Um04WW53UnNZaWxJaVRlZndrSHRYMHR1NzAwcTZiZlBkcyt3QUhRcXVZZVh5M1RnMlk4TFljUGt5VjZhcHcrbVQrSStaQ1Y1bm9qeTVGVUl1ZngiLCJtYWMiOiI3NDdmYTc1NjdiYTE0MTJjMDNjNjZiYTkwNDk3Mzk1MTRlZmVjZjliNTMzODgxY2VkZDIxZGIxYTFhNzVkZTRkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:14:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1044834655\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-148779212 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-148779212\", {\"maxDepth\":0})</script>\n"}}