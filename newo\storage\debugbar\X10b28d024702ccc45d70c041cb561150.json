{"__meta": {"id": "X10b28d024702ccc45d70c041cb561150", "datetime": "2025-06-08 00:58:12", "utime": **********.495784, "method": "GET", "uri": "/printview/pos?vc_name=7&user_id=17&warehouse_name=8&quotation_id=0", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 163, "messages": [{"message": "[00:58:12] LOG.warning: Implicit conversion from float 160.79999999999998 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 186", "message_html": null, "is_string": false, "label": "warning", "time": **********.453252, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 1.4 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.453677, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 3.5999999999999996 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.453845, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 3.8 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.453966, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 7.199999999999999 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.454089, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 7.399999999999999 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.454208, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 13.2 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.454334, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 13.399999999999999 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.454452, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 16.799999999999997 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.45457, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 16.999999999999996 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.454698, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 20.399999999999995 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.454847, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 21.799999999999994 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.454986, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 26.399999999999995 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.455106, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 28.999999999999996 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.455238, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 31.199999999999996 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.455357, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 33.8 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.45549, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 37.4 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.455638, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 39.6 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.455765, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 39.800000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.455913, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 44.400000000000006 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.456086, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 47.00000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.456235, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 49.20000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.456359, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 50.60000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.456476, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 52.80000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.45661, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 54.20000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.456733, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 56.40000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.456852, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 59.000000000000014 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.456968, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 61.20000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.457127, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 61.40000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.457262, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 66.00000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.457382, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 66.20000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.457498, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 68.40000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.457616, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 71.00000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.45775, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 73.20000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.457883, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 77.00000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.458096, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 79.20000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.458267, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 80.60000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.458398, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 82.80000000000003 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.458522, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 84.20000000000003 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.458657, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 87.60000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.458822, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 89.00000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.458968, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 92.40000000000005 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.459098, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 93.80000000000005 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.459229, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 96.00000000000006 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.459356, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 97.40000000000006 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.459503, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 100.80000000000007 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.459627, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 102.20000000000007 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.459745, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 105.60000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.459865, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 105.80000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.459995, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 108.00000000000009 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.460122, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 111.80000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.460241, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 114.00000000000009 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.46036, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 116.60000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.460487, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 118.80000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.460625, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 121.40000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.460743, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 123.60000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.460872, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 123.80000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.460992, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 127.20000000000009 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.461131, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 128.60000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.461264, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 132.00000000000009 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.461385, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 135.8000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.461505, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 138.00000000000009 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.46164, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 138.20000000000007 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.461788, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 140.40000000000006 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.461907, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 143.00000000000006 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.462026, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 145.20000000000005 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.46215, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 146.60000000000005 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.462286, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 151.20000000000005 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.462407, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 153.80000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.462525, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 156.00000000000003 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.462649, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 156.20000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.462793, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 158.4 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.462969, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 159.8 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.463157, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 160.8 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.463308, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 159.8 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.46345, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 160.8 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.463571, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 159.8 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.463688, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 0.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.470957, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 1.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.471206, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 2.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.471341, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 3.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.471479, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 4.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.471659, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 5.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.471841, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 6.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.472009, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 7.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.472154, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 8.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.472292, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 9.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.472425, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 10.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.47255, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 11.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.472716, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 12.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.472889, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 13.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.473051, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 14.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.473182, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 15.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.47331, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 16.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.473471, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 17.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.473599, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 18.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.473724, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 19.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.473855, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 20.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.474002, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 21.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.474135, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 22.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.474262, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 23.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.474387, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 24.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.474532, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 25.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.474737, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 26.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.474922, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 27.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.475133, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 28.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.475324, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 29.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.475561, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 30.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.475747, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 31.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.475881, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 32.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.476008, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 33.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.476145, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 34.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.476288, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 35.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.476417, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 36.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.476545, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 37.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.47667, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 38.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.476807, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 39.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.476943, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 40.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.477067, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 41.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.477216, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 42.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.477391, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 43.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.477532, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 44.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.477662, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 45.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.477789, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 46.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.477921, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 47.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.47806, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 48.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.478199, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 49.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.478329, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 50.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.478497, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 51.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.47863, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 52.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.478759, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 53.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.478887, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 54.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.479025, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 55.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.479161, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 56.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.479288, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 57.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.479426, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 58.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.479552, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 59.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.479693, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 60.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.47982, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 61.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.479947, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 62.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.480077, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 63.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.480245, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 64.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.480428, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 65.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.480631, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 66.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.480819, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 67.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.480964, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 0.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.481098, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 1.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.481233, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 2.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.481377, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 3.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.481506, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 4.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.481633, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 5.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.481759, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 6.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.481906, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 7.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.482039, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 8.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.482175, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 9.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.482311, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 10.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.482451, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 11.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.482582, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 12.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.482714, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 13.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.482841, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 14.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.48298, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 15.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.483223, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 16.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.483417, "xdebug_link": null, "collector": "log"}, {"message": "[00:58:12] LOG.warning: Implicit conversion from float 17.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.483607, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1749344291.715185, "end": **********.496038, "duration": 0.7808530330657959, "duration_str": "781ms", "measures": [{"label": "Booting", "start": 1749344291.715185, "relative_start": 0, "end": **********.270126, "relative_end": **********.270126, "duration": 0.5549411773681641, "duration_str": "555ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.270142, "relative_start": 0.5549571514129639, "end": **********.496041, "relative_end": 3.0994415283203125e-06, "duration": 0.22589898109436035, "duration_str": "226ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 54528552, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.printview", "param_count": null, "params": [], "start": **********.439887, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/pos/printview.blade.phppos.printview", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fpos%2Fprintview.blade.php&line=1", "ajax": false, "filename": "printview.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.printview"}]}, "route": {"uri": "GET printview/pos", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\PosController@printView", "namespace": null, "prefix": "", "where": [], "as": "pos.printview", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1306\" onclick=\"\">app/Http/Controllers/PosController.php:1306-1408</a>"}, "queries": {"nb_statements": 10, "nb_failed_statements": 0, "accumulated_duration": 0.010870000000000001, "accumulated_duration_str": "10.87ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3355732, "duration": 0.0029100000000000003, "duration_str": "2.91ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 26.771}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3507829, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 26.771, "width_percent": 8.556}, {"sql": "select * from `customers` where `name` = '7' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["7", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 1314}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3562982, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1314", "source": "app/Http/Controllers/PosController.php:1314", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1314", "ajax": false, "filename": "PosController.php", "line": "1314"}, "connection": "ty", "start_percent": 35.327, "width_percent": 10.672}, {"sql": "select * from `warehouses` where `id` = '8' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["8", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 1315}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3607662, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1315", "source": "app/Http/Controllers/PosController.php:1315", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1315", "ajax": false, "filename": "PosController.php", "line": "1315"}, "connection": "ty", "start_percent": 45.998, "width_percent": 6.072}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.383341, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 52.07, "width_percent": 8.28}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.387377, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 60.35, "width_percent": 10.12}, {"sql": "select * from `pos` where `created_by` = 15 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 517}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 1318}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.3971572, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "PosController.php:517", "source": "app/Http/Controllers/PosController.php:517", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=517", "ajax": false, "filename": "PosController.php", "line": "517"}, "connection": "ty", "start_percent": 70.469, "width_percent": 7.452}, {"sql": "select * from `product_services` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 1393}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.422888, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1393", "source": "app/Http/Controllers/PosController.php:1393", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1393", "ajax": false, "filename": "PosController.php", "line": "1393"}, "connection": "ty", "start_percent": 77.921, "width_percent": 6.624}, {"sql": "select * from `pos` where `created_by` = 15 order by `id` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 1402}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.427084, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1402", "source": "app/Http/Controllers/PosController.php:1402", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1402", "ajax": false, "filename": "PosController.php", "line": "1402"}, "connection": "ty", "start_percent": 84.545, "width_percent": 5.52}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "pos.printview", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/pos/printview.blade.php", "line": 5}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.441828, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 90.064, "width_percent": 9.936}]}, "models": {"data": {"App\\Models\\Pos": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 7, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-398544998 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-398544998\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.395598, "xdebug_link": null}]}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:1 [\n  5 => array:9 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"id\" => \"5\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"originalquantity\" => 23\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/printview/pos", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1580628386 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>vc_name</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>user_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">17</span>\"\n  \"<span class=sf-dump-key>warehouse_name</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>quotation_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1580628386\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1065222984 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1065222984\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2039520930 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i31pha%7C1749344276347%7C8%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlJWakdLTFN0NHlWdG9BQVo4SU9ENGc9PSIsInZhbHVlIjoiUFE2OEd4NUtST0tPMVRsSFNncXJlV3VxSXlzQktlZitIYko1ZGVvUzRIa1hGWkN3YVRHdWdEVGlZTVo1RWFGN0JNNWZtdW4wcXlHMlFlN092cXZNa0NQbVBpM0FCSldtcWxCQnkwaEF2bjJQVW81a09NbThwR1c3dUdabi92TmMrRFcwdXMwV2Y4YUU2di9EQy82VVMxdjN3NTA1ZjQzbGhsYXgzU1VFTkFROTRpbVNBblFmdnlLbnRHcHd5dHVoMXZtSCs2Mkhic2sxV08zdE40WUhqMDY3cEtGbjJZOWtMZVdqSXJEOXN3UGJRRVV0Wm8zaDhHLzVJdldrUEFQZDluRDJYdHFwWC8wS01lclRCaTgzUjd3UHJjZUpzVitCRTZYSzZuZUQyMHhHL2h3Q0lEVW1NenFXQ0tndlk3TXhjS2F1RUVjcGw1d0Fpa3YwZVlzN0laOFRFSW1HTkJteVlnQTV5Y2V6bG0wOFdpaGF3ZTFHN1JWdEx1d1VrWGVLTmVlUUozeTM2c0xyaVZweFN2NnJvN1E3bExIYWRVaUhLREdCTk1NVDkxZTlWdGlBV0cycEU0MEhJL2FoMTUzWWh4ekV1dStiMy8vSHZhd2ZKbklSc0ZKc2JPMUk0ZFNDeGtzQTBvdWZreFp1aDlGTEYwVGdYMjEzYU4wbkZEczIiLCJtYWMiOiI5ZmNkNDNjMWI3ZThhYzQ2MDU0ODc5MjE5MTUxNTUyODRjOWU4M2M2ZjI4ZmY4M2RiMmM2ZWYzMDFmZTIzZTdjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImFiTDZQLzVQckNGMEJsRUxhOC9GK2c9PSIsInZhbHVlIjoiR1RYMEF1OUp5RERoa0NseWJtZ0FLNmxKbDZvK3NZOTQ2WGJzRGU4KzNubDJyS3d3SG1KUkhFM3J3dTVEdHlydVpmUEVFWHlmTnk0bGM3SXlpSXZuMVhrdVFSSEp6QU1pYUxOSS9zUmprU0lUTTkrR2U1NGloNndmYy9lci9ibk5JTHJ0b2tSNml2ZDdJdG9BOGxTU251YjVybkFaaDlCR1JWS2FkZDZFOTNPVDhXbmJLVkJMSmFFNm15bXFLeklqaXhDRzZON21yS3JSVytGV25IaUp2YiszQjUxUS95dTl0T0dlZUF2c0RDVTNpVm9rdGQrS1Z2cEsvSUIyOEFCdEo5Q0xhQkpQN25lVkU1OWI3d25MVnVPWndwK0tGWjY1dnQ4R3h2VzZnaTV5TDVzalRTL2tPVWtXdFZOaXl4MFk1QzBkQUU2ZmhXNmFYREJRVG9FdXFUK3p6dGIzMDFKSzNPOGtmay9nU1lkKzBKcHhya0czOGZaTlJGOHlYYWhjTWo5SXdRSERRbzdDMUkzbHJRKzlTdXNoMzUrdzFNMHl1clBLZjVyNDlyNVh0cGhzUGErS21SVk9RMnBBRU5hVEUzMElmWDZ1bFZONzFNeFFXNzZxa29TRmw3OXpFR01EczJUdHIxODJJZURvNk5BUWJ0Nm1ZZjRBSnNUVGNPZE4iLCJtYWMiOiI3NmI0OWMwNTczNTgyNzYyN2I5ZmQxYzEwMmRlNjg4MmViMWU4MGU1YmZhYWU2NmVlN2JiNDMzMGU2NzMxMGU0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2039520930\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1590327314 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1590327314\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1725250279 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:58:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNwcHdWNzZXMTV1aFY1dUw2OEJvZVE9PSIsInZhbHVlIjoiTC9GZjMvQVNVbHBrZzZhakttWmRCSEN0Ymp0ZHVEeW9MUUY2dTBSb05HM0dBbnZmb0h2MGY0NVRhM0JzbURlbktuQThWdzhCelMyWVRsM3duWW9zSnVsZ2ZKTk5FN3JTS004bjRNVGRlZTZIZlNhK2V3dTg2WWl5a0tSc01VOEpTRDI4Z3lDRjBUdUxPTGRvN0FzdGlIMWFxc1YySnBpUVpJbDl0RmEwaXN1TytBWFNDTjk0Yk5GNE8vdGhvR0UxWUlERmdHMXAyK0pOWHBIQkovRTV1WGk2ak9CeE9KMnVuSDF6R3hjbTFzSmpRRTBwcS83aTA4ZzluSXFHRUF6YmNOY1oyZ3o1bXA2NXNiOUVsLzNobkpnL1RMTjhtZFFlcUtmTlZEUVRSNHYyemFyUjRra3lpRHp4MVhUN05qclltaGUyS1FzUHBoN1kwZ1VzOU9PbHA5eVJOQk1sNmdMTlhlZEtiNVVvbSsxc1ZiMkdhamJOY25WdmQ5MlZtZmY0R1hhbXpaZjA3UjAyeElnbXF5dngzak5CYjNvOHhMYXpRcmVpamo2eEl3bWFIY28zTHFzR1lhYzdFVjhHeFU0Wk5MM2dFNThINm81VGtWQzUyc0pCZFdzWVdZaFd3ZG1BS1E2cTNYRHNNeU1qUEVrTnFTRnBLTmMxWHY0RHdZNVUiLCJtYWMiOiJjNmJjMDM5NWNkY2YxYzJlYjRhMDgwMWM1YWNjNjc3MDI5MzkxNzEzZDk5ZjgyM2M1NDhmZmE4ODcyYTZkYWZlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:58:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InB4cEtmMWFRTDd2V0MvZDJxNU5WSnc9PSIsInZhbHVlIjoicStTK243bVBiMVpCOVpxRmdXZDRGR3UzcmRHck9BVWk5eXRadTI4ZWM1cTdTelRDTmhkY2ZpQ2pMYndaMVFvSmZUQlUyWFNyMkE5M0ZSeTVENVVPMGd0Uy9tZnF4bGJkbWVkQ0Jya2FjZjk3dms1U3FCWWhYVFZJN2h6YnM5aU16QSs3bTVnTFdrNTFRMnNvbkxRalU2SXdqRklGWFNjbUdoYUw5NDJzdWdnSE8veitjZnJzcUZZWXNDd2VnUld6TFdPL2RaQ3VxbmZyZS93TVVpMzdFMkRkL0thZk14YTZBVVYrRHJpbENZVlRQSnIva0dqRGFGQlRteEZ3K3lsTG9kd3VLVzJXWDJXRmNob2xLalYwY1NlZkMzOU1nTlE5VUh5MVNKeXRWNzFvRjNReHAzQ25adDhXckU3Z05OMVBraHBmbHUzbDdTd0NERGdiZVZBaEtMZWYxNlFCWHdCM0RxZEUrWFBkb0NBY2Q3aTV3UmdSd2VGV3dZSTlJak9EU0IrMkpzMjAzaGJMQmYwVlRBeC9qRDdHc1VNTlg2Y1ZEQnNSaHVpemZzb1c2aUtXQ0RSNGp6UXI0bzJCVEM5VE5QN1lRRjZQV0NoenlFRmtWWGl4QURydkFMbTFYTGFhQ0ROcWFIdUwrdEh5dXJBMFBJVndMNTFSZWdhc2ZoOTAiLCJtYWMiOiJkMmFmOGE2YjUzNzNmMjg5YzM0MDMxNzhlZDEzNzZjZjJhOTBjMmE3ZDhmY2NlYTJlYmQ5OGM0ZGM0NTRhNTRiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:58:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNwcHdWNzZXMTV1aFY1dUw2OEJvZVE9PSIsInZhbHVlIjoiTC9GZjMvQVNVbHBrZzZhakttWmRCSEN0Ymp0ZHVEeW9MUUY2dTBSb05HM0dBbnZmb0h2MGY0NVRhM0JzbURlbktuQThWdzhCelMyWVRsM3duWW9zSnVsZ2ZKTk5FN3JTS004bjRNVGRlZTZIZlNhK2V3dTg2WWl5a0tSc01VOEpTRDI4Z3lDRjBUdUxPTGRvN0FzdGlIMWFxc1YySnBpUVpJbDl0RmEwaXN1TytBWFNDTjk0Yk5GNE8vdGhvR0UxWUlERmdHMXAyK0pOWHBIQkovRTV1WGk2ak9CeE9KMnVuSDF6R3hjbTFzSmpRRTBwcS83aTA4ZzluSXFHRUF6YmNOY1oyZ3o1bXA2NXNiOUVsLzNobkpnL1RMTjhtZFFlcUtmTlZEUVRSNHYyemFyUjRra3lpRHp4MVhUN05qclltaGUyS1FzUHBoN1kwZ1VzOU9PbHA5eVJOQk1sNmdMTlhlZEtiNVVvbSsxc1ZiMkdhamJOY25WdmQ5MlZtZmY0R1hhbXpaZjA3UjAyeElnbXF5dngzak5CYjNvOHhMYXpRcmVpamo2eEl3bWFIY28zTHFzR1lhYzdFVjhHeFU0Wk5MM2dFNThINm81VGtWQzUyc0pCZFdzWVdZaFd3ZG1BS1E2cTNYRHNNeU1qUEVrTnFTRnBLTmMxWHY0RHdZNVUiLCJtYWMiOiJjNmJjMDM5NWNkY2YxYzJlYjRhMDgwMWM1YWNjNjc3MDI5MzkxNzEzZDk5ZjgyM2M1NDhmZmE4ODcyYTZkYWZlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:58:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InB4cEtmMWFRTDd2V0MvZDJxNU5WSnc9PSIsInZhbHVlIjoicStTK243bVBiMVpCOVpxRmdXZDRGR3UzcmRHck9BVWk5eXRadTI4ZWM1cTdTelRDTmhkY2ZpQ2pMYndaMVFvSmZUQlUyWFNyMkE5M0ZSeTVENVVPMGd0Uy9tZnF4bGJkbWVkQ0Jya2FjZjk3dms1U3FCWWhYVFZJN2h6YnM5aU16QSs3bTVnTFdrNTFRMnNvbkxRalU2SXdqRklGWFNjbUdoYUw5NDJzdWdnSE8veitjZnJzcUZZWXNDd2VnUld6TFdPL2RaQ3VxbmZyZS93TVVpMzdFMkRkL0thZk14YTZBVVYrRHJpbENZVlRQSnIva0dqRGFGQlRteEZ3K3lsTG9kd3VLVzJXWDJXRmNob2xLalYwY1NlZkMzOU1nTlE5VUh5MVNKeXRWNzFvRjNReHAzQ25adDhXckU3Z05OMVBraHBmbHUzbDdTd0NERGdiZVZBaEtMZWYxNlFCWHdCM0RxZEUrWFBkb0NBY2Q3aTV3UmdSd2VGV3dZSTlJak9EU0IrMkpzMjAzaGJMQmYwVlRBeC9qRDdHc1VNTlg2Y1ZEQnNSaHVpemZzb1c2aUtXQ0RSNGp6UXI0bzJCVEM5VE5QN1lRRjZQV0NoenlFRmtWWGl4QURydkFMbTFYTGFhQ0ROcWFIdUwrdEh5dXJBMFBJVndMNTFSZWdhc2ZoOTAiLCJtYWMiOiJkMmFmOGE2YjUzNzNmMjg5YzM0MDMxNzhlZDEzNzZjZjJhOTBjMmE3ZDhmY2NlYTJlYmQ5OGM0ZGM0NTRhNTRiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:58:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1725250279\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1850171502 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>23</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1850171502\", {\"maxDepth\":0})</script>\n"}}