{"__meta": {"id": "X59ee7d38b80942fd69218dc0e3683887", "datetime": "2025-06-08 00:31:36", "utime": **********.359234, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749342694.79896, "end": **********.359271, "duration": 1.5603110790252686, "duration_str": "1.56s", "measures": [{"label": "Booting", "start": 1749342694.79896, "relative_start": 0, "end": **********.14989, "relative_end": **********.14989, "duration": 1.3509299755096436, "duration_str": "1.35s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.14992, "relative_start": 1.3509600162506104, "end": **********.359275, "relative_end": 4.0531158447265625e-06, "duration": 0.20935511589050293, "duration_str": "209ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45054608, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02716, "accumulated_duration_str": "27.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.283571, "duration": 0.02476, "duration_str": "24.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.163}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.324961, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.163, "width_percent": 3.829}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.339729, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.993, "width_percent": 5.007}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/invoice/processing/invoice-processor\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2081022612 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2081022612\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-462598011 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-462598011\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1489037358 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1489037358\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/invoice/processing/invoice-processor</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749342560544%7C33%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImxwTVp1WVlsZ2ZRWDRqbCsyK2cyVnc9PSIsInZhbHVlIjoieTR4OEhrU0dyUGFtV0I5dlkzQWRaV2tUVk5TbEFRdFFvU1NraXA1eFlsejk0RmpCdlhqakhnclZXRVBmTW5KdDd3N3NObDkvenlETm1mOVp0ZktWeVR5K21YaTh1OEVEV0sxT2w0L0lqRzVQb2Q4ZU5pZzk5c0dDUjc4L2pyRXkrckw0TmFYOTF3MDhVZGJKWWJLclBNUGtFU1ErdS9sVEpXNlpNWEgxMldRZGVHa1BKWXhJWFFUM2VseTVSOC9nZnJNNlR4WnFNOE45eWtFbEdzWExzMkwwSW5FSWU1S2NzUUo4TFpQbmlsY3RlODhLcnRNMkJ5UndpYjJ0dW81TUhiVC9weEFqd1ljNXRHVXJqbUNLWXFkV1FQNk5naDRTaVMwZjYraWpBdjlqSjdLSzhFcHJPQzk3ejE4anMyMXBTMEkrb2luQ3hMcXc3aG54YmhxYy9JSmxUQXhWWkJaK3BaQUdyTXJtRk9wdlN1bzZmdGR0Tyt5QUNrZHdJMXRGZzR1QkQrRUFzL0tXMVVtR1U1TXRJRXdPZjJRV3hUK1NXcitIb296b1puL3lMU0N3eDE5WGpTK2FORmNVMFRCcDNQSUh5TlQvaFB5YUJPTFh2TW5NTU9KVlU2NCt2VmZtVmVlQjAwZnZJVzBXZUpSd1FEZHVja0tkb3VIT3k0RTIiLCJtYWMiOiI3MTkzNjZhYWY1NWExMzY4NTU1MGYzZGZmOTZkNzg5MWRjOGUwYWNkMWI0YjY1NDBiOGY2NmJiNTI3NzVjMjgwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImcvWnFpSE1SZFJ4N21qV2tNVFBKTnc9PSIsInZhbHVlIjoibFdUSWdnUnI5UG9BVXJnZTlBMG1kNVpORk5pTEQwUVZWdE93WE0rMFdtbXZWZFNkd001Yjd3SmJzOXQ0elRmb2NpTU1UdTlFL2ZHU3VCRk9ScDlPR1d0bEJyTGZuVldKUFFHbExtZlV6bkQvcWVET25mOHkwd3kxZldHOGpLNXVEVEUzQ0ZvQWRHOHEydHgwOTJIcndJV3gya2RkbmhSMUdVT09oZWpXaURCUktFQ0JEVUp4R1FWSm1ObWxpNkJOLzlEdkVPS1lWWkRET25nbHNaZWFFQlFLVm1TbE05alZTWm0rVjlnTmF1cU1YcHJlaE1HdWNBeUZ3VkE1UTF2VmJlZExQdy9TT2VLNURkQlV6VWgrR2JQeXFzMmgycjhLQlpJR2VhOGtXdmE2SDJhZ0JCNENxb29JWnpPNFliVGtiR2d6c1RQSEd2cy8yWXE4RUw3bmxkTy8rSnpaOGxGQ2ZGNTVTZ0JEQmgzekFyT2dWeHZIRTlIYkhQL3YyRE96Tk92WmZDRyt3bEdYWUd4ZzJoTFp2dUJVeEsrRVlleVNmU1BwLzh6N0d1c0c0c0NCS2sweThhM0ZqZTZMY0pRSXcvdnVYNjFPQmk3SC9RcmJDWHhIcHpIcUR6VmNXQ2x2MVdJd2xDcnlTYmREbEVHbVlNa3YzQS9NV1pJSXZ4WVkiLCJtYWMiOiI5NDc4NTFmYjEwMjUyN2FiNzNkNDZkZDg0ZWQ5MDFhNzQ1ZGY2YWFhODc5ZGZhMTk5MDhjYzI1NTRlZTZkOTc4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1429605446 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1429605446\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-609272523 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:31:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjhEbDhZa29oNVkxYlk2OGxtWjl4WUE9PSIsInZhbHVlIjoiY0hvWS9pRTBVVUY1bmxHU1crTjcrWmgwazFtZ3g1bjU5ZGZhSDJVWW1iWWJPR3VwMGZQcllab3ZCSnh6QkNobXErdGFsay9lK2dyUkJjdjV0c1hXdklIeXhDVlBUZVRzYi9NUjc4V1lPL01yd1ZIdEhlSkxabVlCa1ZOVUd3S3RRczBZZHU5bFRHQXlDV21BZDVkb0RmbXA4MWVTODVuUXNkVjRGRkFtS1lWeEdCQjErZDR1NUVyWWFodHExOGNkUnVxKzlXUTZrMEtRK01aZXkzcmUrZWxhb3lQMEpoeEd3Q0pLZ0tja2gxNnBFdTFCKzBlUVVhemJnSnJnb1NTVE9pZXFlZ2w0SjhOTng0U1JVWEF6b0lGeVJjZGJweWxJNW1pWmxoaFFNMlBVQ3lFT29pUmNTaTFjRVhrRVI5Mk5JUDI4YWlTU2sweDYva1ZhUXVQNjE1cmpmZlBETlZPMlduM1c1aHJsdDRHQjZnR2hVOS9VeGY0RitYaDZjOEhsazNKQmNnV21MSUwxdDc2ck9ZL2w4MjVuZ3E4eEdOSWl1UXc2RkhjZlhYd0ZKbmxqYklZQllPUkJ4MVNNMWFxWWZadWRpbU01aGtITGFydVZaTkFXaVBpSUtJRDgycEJDbHBqUE02bmV6YU9PazVyMnBCTUdOaDMvZlJrNkozSlkiLCJtYWMiOiI3NmVjOGY4ZmEzNzMyMjY4YjUwNTE3N2M4ZWUwNzAxYWFjODQzMTJiMmI0NmNhMjg1NTMwZjM4ZDlkYTNlYmJhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:31:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlhQakQxOVFvUGNPY0RpSCt6Q2lyMEE9PSIsInZhbHVlIjoiRHo4dmh6NGVXZ1R0ZUhJN1V5eTNRZTkvNWJYajBYNmd2S08xUUk2WW5qdUlRUXhsUzBNSnFFU3IwS1pDdHdXdmhIek5ta3FWb3FBMzEzMEpRcWVqaEU2VzF1Y3AzdE4ySmtFUlNmZmpaOHcycThLWXBvbG9ueFdRV08rekViaUl1R01uSDQ0MGRZZWdtbTRDMkxmOEwxaytKb2pjUDc5emd0bXF1L0FBd3dPWGhaZmpIazJFYlZEVDVSQmNOeWc1RnYxdmYwdUdyZTBJVlh6RkhpWFBQWnA0Tk5jRVhjVVN3SFBWVGVKdDIwZG5KWnNvdzdjVjZpZWFqQ2VHZU1tdkNFMEJpWE1GUTNmTlo0eEw1UGVmdDRMQXMzd3RHa3lYVWk5aENLbVlyNmlyUXVLaExRT2liOHZ4Q0dCckVUSFlDNjYzZktsSkdJOUZhTlBoRkplT2M5aThaZVVHZ3k5bThERTJObCtZR3luWHhuZlpyWk1Ddmk0cXVrM09yVEtIQWhHalJGVGhqYzdmVFdBYTR1RGpURmo5RzdpUW5FS1BPcUxYSVZTNS9xWExucEx2aDV5RWs3OUpPVllrVzVmL2ZVVE5BeTFjTityZVJoalJpWE02ZzRsdlBPNXpFbVE2RTZ0dElNOXB1bU1RMldiY2xzaGhuR1p4d0crRTk4TDgiLCJtYWMiOiI3OTAzN2U1MTU0YzdkM2FmYWExZGZiYWExZjg3YzY1NTFlMWNmZjRhOWZhYjczMmMzZGU5OGUxNmU1NDQ1OTNjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:31:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjhEbDhZa29oNVkxYlk2OGxtWjl4WUE9PSIsInZhbHVlIjoiY0hvWS9pRTBVVUY1bmxHU1crTjcrWmgwazFtZ3g1bjU5ZGZhSDJVWW1iWWJPR3VwMGZQcllab3ZCSnh6QkNobXErdGFsay9lK2dyUkJjdjV0c1hXdklIeXhDVlBUZVRzYi9NUjc4V1lPL01yd1ZIdEhlSkxabVlCa1ZOVUd3S3RRczBZZHU5bFRHQXlDV21BZDVkb0RmbXA4MWVTODVuUXNkVjRGRkFtS1lWeEdCQjErZDR1NUVyWWFodHExOGNkUnVxKzlXUTZrMEtRK01aZXkzcmUrZWxhb3lQMEpoeEd3Q0pLZ0tja2gxNnBFdTFCKzBlUVVhemJnSnJnb1NTVE9pZXFlZ2w0SjhOTng0U1JVWEF6b0lGeVJjZGJweWxJNW1pWmxoaFFNMlBVQ3lFT29pUmNTaTFjRVhrRVI5Mk5JUDI4YWlTU2sweDYva1ZhUXVQNjE1cmpmZlBETlZPMlduM1c1aHJsdDRHQjZnR2hVOS9VeGY0RitYaDZjOEhsazNKQmNnV21MSUwxdDc2ck9ZL2w4MjVuZ3E4eEdOSWl1UXc2RkhjZlhYd0ZKbmxqYklZQllPUkJ4MVNNMWFxWWZadWRpbU01aGtITGFydVZaTkFXaVBpSUtJRDgycEJDbHBqUE02bmV6YU9PazVyMnBCTUdOaDMvZlJrNkozSlkiLCJtYWMiOiI3NmVjOGY4ZmEzNzMyMjY4YjUwNTE3N2M4ZWUwNzAxYWFjODQzMTJiMmI0NmNhMjg1NTMwZjM4ZDlkYTNlYmJhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:31:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlhQakQxOVFvUGNPY0RpSCt6Q2lyMEE9PSIsInZhbHVlIjoiRHo4dmh6NGVXZ1R0ZUhJN1V5eTNRZTkvNWJYajBYNmd2S08xUUk2WW5qdUlRUXhsUzBNSnFFU3IwS1pDdHdXdmhIek5ta3FWb3FBMzEzMEpRcWVqaEU2VzF1Y3AzdE4ySmtFUlNmZmpaOHcycThLWXBvbG9ueFdRV08rekViaUl1R01uSDQ0MGRZZWdtbTRDMkxmOEwxaytKb2pjUDc5emd0bXF1L0FBd3dPWGhaZmpIazJFYlZEVDVSQmNOeWc1RnYxdmYwdUdyZTBJVlh6RkhpWFBQWnA0Tk5jRVhjVVN3SFBWVGVKdDIwZG5KWnNvdzdjVjZpZWFqQ2VHZU1tdkNFMEJpWE1GUTNmTlo0eEw1UGVmdDRMQXMzd3RHa3lYVWk5aENLbVlyNmlyUXVLaExRT2liOHZ4Q0dCckVUSFlDNjYzZktsSkdJOUZhTlBoRkplT2M5aThaZVVHZ3k5bThERTJObCtZR3luWHhuZlpyWk1Ddmk0cXVrM09yVEtIQWhHalJGVGhqYzdmVFdBYTR1RGpURmo5RzdpUW5FS1BPcUxYSVZTNS9xWExucEx2aDV5RWs3OUpPVllrVzVmL2ZVVE5BeTFjTityZVJoalJpWE02ZzRsdlBPNXpFbVE2RTZ0dElNOXB1bU1RMldiY2xzaGhuR1p4d0crRTk4TDgiLCJtYWMiOiI3OTAzN2U1MTU0YzdkM2FmYWExZGZiYWExZjg3YzY1NTFlMWNmZjRhOWZhYjczMmMzZGU5OGUxNmU1NDQ1OTNjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:31:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-609272523\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1432855731 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/invoice/processing/invoice-processor</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1432855731\", {\"maxDepth\":0})</script>\n"}}