{"__meta": {"id": "X527a15fc659cda8968e3f01dac6243a7", "datetime": "2025-06-08 00:28:44", "utime": **********.624185, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749342523.711087, "end": **********.624211, "duration": 0.9131240844726562, "duration_str": "913ms", "measures": [{"label": "Booting", "start": 1749342523.711087, "relative_start": 0, "end": **********.455502, "relative_end": **********.455502, "duration": 0.7444150447845459, "duration_str": "744ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.455517, "relative_start": 0.7444300651550293, "end": **********.624214, "relative_end": 2.86102294921875e-06, "duration": 0.16869688034057617, "duration_str": "169ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48114848, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.0115, "accumulated_duration_str": "11.5ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.528135, "duration": 0.004200000000000001, "duration_str": "4.2ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 36.522}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.547329, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 36.522, "width_percent": 12.522}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.5809689, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 49.043, "width_percent": 15.565}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.587347, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 64.609, "width_percent": 12}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.598849, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 76.609, "width_percent": 16.87}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.606837, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 93.478, "width_percent": 6.522}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1803834258 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1803834258\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.595701, "xdebug_link": null}]}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-2116876479 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2116876479\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1276592750 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1276592750\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-659016062 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-659016062\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-744175042 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1834 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749341283326%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InAwbjZEY0hoaHR0Q1R3MGxab1lKRnc9PSIsInZhbHVlIjoiM2ZheWxBYkg3VWZvUjhkRUIxME9BY1VZakIyWWxKQUJzL0w5eDRRMWd5ejBCUmRtUk80RHNxakdsRlQvNFRwZkNuSjF5TFA1Y1NhbVdCMGNIbFhCTDNTdDlBRE9GZndkWFp4S1FYd3pUcFJncHAyZGxiUGxuUmtwaXVpUm0wYmg5QjVaVytZZW5MY2IzMVRUbjlLeS9oT0dwL1plNlpMd0xucTVyYis2VUtsdzk0TzBTU0o2OG9Pbm5PKzVYUmcwazFtK3hKdWxVdlZVZFNtM1llY0UzVWNKbE1Ld1R3K3JEZmhPbEZWKzVnK04wTGJFbjcxOXJpMnhia3ppZTFiUjFCT0xjRlh5L0NIQ0NPcHV5ZjlJSjQ4WXJ6UUxqUzgrOGkrcTNDMGRCdXJBS3oremdxUEJ3QktaRXRZbDJlaHdBMmpBRGpOTDVnbEZ0SVY4VmZMbVJ5WkJLVko4YUtoUXZlVXFhTHVBWkJid3ZPb1JIRUI5SFJiVS9VSmt2eUZsMlR2WTRWaEhJV00xLzliY0F6QzFSa092SngwTHVPU0liRURxek9vTURZMjRhZnNGY0MreG13MmhkK2xrVms2Q0g2V015cEVtejFmTEswd08wSHZJZWMzcENqQlFXL3dlY3hpN2lqNnlwMm9FVDVrRUdrYVYwYTdoOXJwN3pITUgiLCJtYWMiOiIwMDMzNTdlZjI0MmY3YjBhNTc0YjczZTJlYWQ2NWQ0MzIxNzE3MmQxNDA3ZmRiZTM5MjMyYjZlN2M1NTA0MDExIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkgzUnlSdFB3ZWNzZ2wwVjFEQk9FeXc9PSIsInZhbHVlIjoiaVNsOFRJeEpaZUE2SXJCMk9XQXYwVzNSVHU0MitHVVN3WnQzTEIyTTZTVU1tS2laaDdSYTVSdUl0U1REcXEwTjMwL1lKcEhzdmJDdVFNQ0lxWWhVaHJRT1JKRDV2NXFUWGgvL3o0aW1DZVRvL2hYTHArS3R2TDJ1VkJUR3I4SFFxcmJPVmliaUZvWjRLWHNuOGdNLzhPSStqWWgySWdxczNSaDRpMUVjcEpnZHNuanVZN29FVEkyZ1pVVWcxWXlGRXhkWEYyV0VhWHhMQm9mMmY2YlF1by9SZjB0bVF4aTRrRkE5TVVJZmJSSDBWcmZoRmpNVFlDSktPRmE2Nk9CdU1IR0JXS2FhQjViQTZPR25PTjJwa0w2WFI0WmZqWFZLWmNJbWxmUkJPb0tPZUdkcmp6Wk9OZmpmcWlaU0l3TFFOS2ZLYjBkQXE4Q0RuUEY0S0RMVTNxQm5MRnNoRlJWb3RxZ3F1QXhvL0lEWVRtM0tRQXNJaHYzb1JyeWxCOUF3eHk0SUFJVlZGeEROdjF5bUozdlpWaWptblNWdkorVEN0aUliMGkvV2hiNTlTTkN2b3k5aTl2NTZFL3hBYktqY2RUYlhqdnh5SUFab0kvWUExWS81eFFCSWNhKzBhemxJNzBoNlFuemhqL3QxU0dBOHJ6Ujc3TlBoVjhQM2FWK3oiLCJtYWMiOiIyNDRmYzQ4ZjVmYzdiNjBkMzllYzVjYmExNmQwYjZkOTdkYzE4MWEyNDg4MzRjNGFmNjFjYjUwZWRhNDRkMTY4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-744175042\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-425216442 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-425216442\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:28:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Iko0TUxMUkN2eXA1ckY3VXdqVHpYZ0E9PSIsInZhbHVlIjoibzkyT0xzR0lVVWRNbGwrZFAxdVBNM2hxbDBrMUtPb1VzNE93WkxET0M5TmFRMys1ejNJVVNjajNZMGtzYmlmTS9RYWQ0bmFWRnphYWYzdU9ZbHdvdHZhQVRDUlQveHkrSU9tZkdZV3p4TG5PRXplbkRBTU83UTZWZXVDTDlPTEZ4VWlFZVo0dkRaYW1yckZqM0hoSFRjUWZGRFhsTXVyTjNmYXNabStveE1Sb2tpUmVraytCYUhQV2VRdGY0OXpGMUxWZEY3UWVsaXorbXk1Tmh0OEZwalRPaGJyMERLSU1LMkR4R1JOay95NmgwcU5Nd1k1Uk0wREZPVVRUVXpQNmRlbUJDVUJKTjVTUHpkOFZ2ajFERFo4KzNZNVJrdW50ay8ySWU2TEFZUkxpODMwZW83dFVTOFgraVZpdEtmMWZjY29wRitsVGIzbmhVWlhTV2dsaGpjTkh1U2YreXduTVQ3ZFl3U1o2RUNsbjlEdWdBaUVzaWhPS2hQekdibnBkMkV3OVJoZzh3alVlNnVXZStMM2Z0SytGV2VTdVkvRmE1TlNRRmhFM3RsWFlBS3NRaVhZdTZyWWlQSlcrYjc0N1FUSTQrb2lEbGZwQ3Eza1BnR05Wa2EyVUFRSXZNUjdrZExlU05pdWlJSmVXOTZzcElRVG9OT3VzYnl2V0NvZHEiLCJtYWMiOiI3NWMzMTU5M2ZhMWQ4ZjY1ZjEyMWIxMGQzZDY2MzdjNzgzMTU3ZDAwZTlkYjQzYWVkZjQ1ODA0NGVlODA5MDlhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:28:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlgzOHVIZ040V0tveFUrRHJsS3pXZUE9PSIsInZhbHVlIjoiZklqaS8rcmlpU0Vpc3Y2THVZQlExQzREVG5pMjBqNWpYaWV3eVliNkdzaFZDK0s3bUhDbkJ4M2lkaHlxNmxTbjRwV0lPcUN1RldPTGVGNEFEaWJPN3NBdmFkVGJBWmkrTXFyN05uQUZrMklHK1YwSnEyTWhiOEw3clU1RjBwZEQ4Ujl0cC9KSjloNHIvd2dkSVdUZEp6dmxEVmp6SlZ4TjBUeWY1R01EWUNxcWRDT2JqTy9paGR4TlNOUG9SeWdZOHVna3hTTjk3bVNYVlRZUzhUaXE3RVJQaW41Ym1qamRRVUsyRjBHU0F2S1k0Z1ErcXVBM2cremNTSmEwZkZuZytnYy9raFpQNUptTEVXRzFzRy9nVHgzeEpIMXJ4KzFkUHdJNzJZSHNqNWRLRXY3MGdmMy90NXhpUU5icjBVeEFRNTNTMEFPTHlFUVhENzlXMHZoZFNFTEp0QW05VTJsSWlvQnljSzlqUXJWNHUxTUxpWUhndnFTZUdzZitqclowR1BER3FzSlczemVGQXQwUTVaVEtBWUo3YStOTWFCVFdoNlMvb2lDbnBsd2JTbXZrckhHTWRUU2pzb0g1UVFEK2swb21hZGJDZnFtSTlSZFo4VGc3TWVYYjhVTmV3YUUyUmNrN1JJT0ZvbHNyb3dhNHVRNnhSR0xNQmhJL2xOVkMiLCJtYWMiOiJlOWIxNmRjMDBlMTM1NDc0NTBjNGFiN2M4YTAyODI4ZDAzZmY4MTQ5MWRmMjM2MDBlNzViMzEyOTMxNTcxNjQ3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:28:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Iko0TUxMUkN2eXA1ckY3VXdqVHpYZ0E9PSIsInZhbHVlIjoibzkyT0xzR0lVVWRNbGwrZFAxdVBNM2hxbDBrMUtPb1VzNE93WkxET0M5TmFRMys1ejNJVVNjajNZMGtzYmlmTS9RYWQ0bmFWRnphYWYzdU9ZbHdvdHZhQVRDUlQveHkrSU9tZkdZV3p4TG5PRXplbkRBTU83UTZWZXVDTDlPTEZ4VWlFZVo0dkRaYW1yckZqM0hoSFRjUWZGRFhsTXVyTjNmYXNabStveE1Sb2tpUmVraytCYUhQV2VRdGY0OXpGMUxWZEY3UWVsaXorbXk1Tmh0OEZwalRPaGJyMERLSU1LMkR4R1JOay95NmgwcU5Nd1k1Uk0wREZPVVRUVXpQNmRlbUJDVUJKTjVTUHpkOFZ2ajFERFo4KzNZNVJrdW50ay8ySWU2TEFZUkxpODMwZW83dFVTOFgraVZpdEtmMWZjY29wRitsVGIzbmhVWlhTV2dsaGpjTkh1U2YreXduTVQ3ZFl3U1o2RUNsbjlEdWdBaUVzaWhPS2hQekdibnBkMkV3OVJoZzh3alVlNnVXZStMM2Z0SytGV2VTdVkvRmE1TlNRRmhFM3RsWFlBS3NRaVhZdTZyWWlQSlcrYjc0N1FUSTQrb2lEbGZwQ3Eza1BnR05Wa2EyVUFRSXZNUjdrZExlU05pdWlJSmVXOTZzcElRVG9OT3VzYnl2V0NvZHEiLCJtYWMiOiI3NWMzMTU5M2ZhMWQ4ZjY1ZjEyMWIxMGQzZDY2MzdjNzgzMTU3ZDAwZTlkYjQzYWVkZjQ1ODA0NGVlODA5MDlhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:28:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlgzOHVIZ040V0tveFUrRHJsS3pXZUE9PSIsInZhbHVlIjoiZklqaS8rcmlpU0Vpc3Y2THVZQlExQzREVG5pMjBqNWpYaWV3eVliNkdzaFZDK0s3bUhDbkJ4M2lkaHlxNmxTbjRwV0lPcUN1RldPTGVGNEFEaWJPN3NBdmFkVGJBWmkrTXFyN05uQUZrMklHK1YwSnEyTWhiOEw3clU1RjBwZEQ4Ujl0cC9KSjloNHIvd2dkSVdUZEp6dmxEVmp6SlZ4TjBUeWY1R01EWUNxcWRDT2JqTy9paGR4TlNOUG9SeWdZOHVna3hTTjk3bVNYVlRZUzhUaXE3RVJQaW41Ym1qamRRVUsyRjBHU0F2S1k0Z1ErcXVBM2cremNTSmEwZkZuZytnYy9raFpQNUptTEVXRzFzRy9nVHgzeEpIMXJ4KzFkUHdJNzJZSHNqNWRLRXY3MGdmMy90NXhpUU5icjBVeEFRNTNTMEFPTHlFUVhENzlXMHZoZFNFTEp0QW05VTJsSWlvQnljSzlqUXJWNHUxTUxpWUhndnFTZUdzZitqclowR1BER3FzSlczemVGQXQwUTVaVEtBWUo3YStOTWFCVFdoNlMvb2lDbnBsd2JTbXZrckhHTWRUU2pzb0g1UVFEK2swb21hZGJDZnFtSTlSZFo4VGc3TWVYYjhVTmV3YUUyUmNrN1JJT0ZvbHNyb3dhNHVRNnhSR0xNQmhJL2xOVkMiLCJtYWMiOiJlOWIxNmRjMDBlMTM1NDc0NTBjNGFiN2M4YTAyODI4ZDAzZmY4MTQ5MWRmMjM2MDBlNzViMzEyOTMxNTcxNjQ3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:28:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}