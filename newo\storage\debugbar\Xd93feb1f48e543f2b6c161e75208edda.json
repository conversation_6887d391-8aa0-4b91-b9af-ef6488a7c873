{"__meta": {"id": "Xd93feb1f48e543f2b6c161e75208edda", "datetime": "2025-06-08 01:14:10", "utime": **********.616798, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749345249.819158, "end": **********.616829, "duration": 0.797670841217041, "duration_str": "798ms", "measures": [{"label": "Booting", "start": 1749345249.819158, "relative_start": 0, "end": **********.499964, "relative_end": **********.499964, "duration": 0.6808059215545654, "duration_str": "681ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.499986, "relative_start": 0.6808278560638428, "end": **********.616833, "relative_end": 4.0531158447265625e-06, "duration": 0.11684703826904297, "duration_str": "117ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45270200, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1671\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1671-1681</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02317, "accumulated_duration_str": "23.17ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.561864, "duration": 0.02238, "duration_str": "22.38ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 96.59}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.601414, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 96.59, "width_percent": 3.41}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-855247753 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-855247753\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1614959267 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1614959267\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1740555970 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1740555970\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1580654501 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1939 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i31pha%7C1749345246637%7C11%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InFzdklqVTBab3RlRkNTMUxEWjBKcnc9PSIsInZhbHVlIjoicFZrdnlpUG16NVFEeEZGbEMyemRaRFUxNkdnbG0rbGMvTG1tS3kvMlc0STVtYllNYjJEdnR3QVZ1S1RrOUNmZHFrdVhtYVZYTlovVEI4b0RaMHVjZjh4clM5UEFhSXdKdW5YSEE4N2Flcm1VSm1BVnVKZi9uN1B1YmpDODVySGpHeFQzVDFiam8rVXRjZ0poNXhFZGg2YVBxazJKL3ZHWE5VSFBETzBCQlc0a2J5dnd1VmZsSXZqblp6QjdDZDFINFpYcHZjOW5Sdm5qTnBwNzBLMUVDRlNvaWZRNG8zR2QvQWxYekNMVjZYY0pCUVRjM2M0VHk4UjhSZ0tzcGNpUWdHeTMyZFRlVHhHUS9wQjRRRnRURHdFeDgyVnlWa2Ewc01GcVk5VEY3VEdvZml1aGdxYWdvKzhjam1LWlZiTGE2MFNteUIrWjJGRG9pa2dkZ0VEdkNVUlRBQjRhTytPYVM1VUU3VVl2Qm0xTDR0NEt4Lzh4c0hKTmw1bmxjV2d2S29zTUJYTkNyY2NKWXYyK2FBMVZKWUU2QUg1bmh0MU85bmF3M3BKWjcwbzdZdTBhTk5KR0FZMUdoSEpSUnpSRWlaYmpnNVpWWlo4TEttNGJhc3h1cDRPakFWempES05BSW9KeW9DV1k3QU1Ma2hmdldVTEcvUFVhbHJnenVZVVkiLCJtYWMiOiJhOTJjZDMxMjIzOTRiMGI2NGYxMDgyYTMxNGY3OTZhMGJhNzI4YTU3MGFjYjY0NTFmMTJkMmIyNzZmZDUwZWY4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImlFQnBhazhvQkR4RUlIcGxJOEU0VEE9PSIsInZhbHVlIjoieVFWdDBLVWJFd0FFSDNXV1JiR2N0NERyWWNZNmpld2VUaGZEQXMyY2VidWlNNVc1VERPMUc5ZUpLaVk5STJUZVY5dVlVVGZyTTBtR0E2THdKUnFwNjdENkM0S3VYTmdjYXY0OEVDUDk0aU1hVkUwYTlPRkp6L1F4VEpuWW05YzNUV3dIUVZsL29UalFSOHlSTnZrbGtXeHY5ZXlpbU5KOGpmWHYreGkxbkFEZWtIK3RwNHpIRktBZEc4QW9JTTdvV2dUcjc5STNhVFZyYTNxVTF4MmdQVGdCcUJ0UWtOTWcxS0dIM1lWNi9acDErUjlPc0gzMlowVmJJN2c3N1ZNNlNZLzBNL2J6dGdPbWhoNS9XVkdEYnR6anZDVHV6QUJQUkkxWG56aFltaWdQUXZxWC9YTUszbmNkS0JPVlpWeFhLZ2RJZEtZSjdWMy9CYmkvVWFaWGNKWWdnN3UrcFBxOFUybUtNQlZ2cFpRaG56SjN6aVVZMEdNQUsxd3Nnb0s0UjRtanJxeTM0bTJaS1UyWHAzaXBzaXBueHowM1dUSlJTUFUySnZ3alhHdHp3Um04WW53UnNZaWxJaVRlZndrSHRYMHR1NzAwcTZiZlBkcyt3QUhRcXVZZVh5M1RnMlk4TFljUGt5VjZhcHcrbVQrSStaQ1Y1bm9qeTVGVUl1ZngiLCJtYWMiOiI3NDdmYTc1NjdiYTE0MTJjMDNjNjZiYTkwNDk3Mzk1MTRlZmVjZjliNTMzODgxY2VkZDIxZGIxYTFhNzVkZTRkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1580654501\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1239975362 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1239975362\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1631928538 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 01:14:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlJkOFY1STgxeU02Y3dIaTkrNkNTdVE9PSIsInZhbHVlIjoiNFRsb0c2QnNDM2NTeko5MzN6V3V2bCtUVjg1ZkR5bGh5eDNpNGV2SWRzNDA1bEw2bVFvZWRuYldZMG45Vzk2ZFFSZEhrTUQ1bXRPK0JxcFhCeXIrcmg4UlNidDUramZpZTd3a2hwT09LS1JnT0lHWkF1T09hdzFCZnBpUnBmS3RaaDB1NFZkZjBsR1k0TlZVdmw2WXFwcVBFMGIvSVphNE5tR2JGVEVuTGFZcytHMEdza0pjWDM4UWx0SXFRalVKNFA3eXJSMlV1V0N0bUVSOTR3WE85dmhrSjdvR2xVS0lxSjZHOUcrUDZ5a3Q0Tkp1Z3RXdTRBTzhvalJGbTdpaTdVenpnZ3M1Nm40UHlJMXVJVGxYVmtXbFlSVjJPbDB5SmVNY0NUeU15TGQ0L2dxd3hWV1RCdEpvSWJ0MkZZQm9yajFHVnZ2cjhyUmF5MkN0SzVtbEJEZllwUk5YajBnY1FFbm93ekE0c0ZGYlovZTM0N0lWTDRMNXBtT1UxTkpPS3lJdUpyb290aklxSWNlWFZWVUROaUNwZlJYenNnMWp3K0MrV2owOHFXbUxzSHErdmRhdHR3RENlQ0VlZU5tblNhMW5JMnhOSHhJd21BL2RrN3VoT2dGa2t0SVF5Y09tV0pzQ01vRkFhLzdGK25ZaHFkWGlTY0syWWdjbFdMT08iLCJtYWMiOiI5Y2I1MjAzOGYzYjI5OTNmMGNhMjBhMGQ4Nzk2OTVmNmY1ZjcyZDBlYjhjNDc2YzE3ZWJlMmE0YmU3N2E5ZGZkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:14:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjZGakJxeTJPTEhhR2JJY1Y1bGlSTmc9PSIsInZhbHVlIjoiZmlNaHBxQXY1ME4yQVNkcWhiN2ZaZytQK09vUHo3d0svSnp1ZUc4NkgxS2VnNDFyRHMvaUIzbFo4Qk5kYzBkWkJBdkJ3TllWdkNmY1lyZnpvZ28zS29zVEVlRUJBRHE4UmZkS2FqbG5xUWRBcEJKSDlsMGQ1TGI1dlV6ZDc5cGY1WklPcXF0bDk1cHZVUDhmYXEwTC9nY3dTMEVlVUU0SWtmaS9CSEtZOW5Ya0FlZFg2U0p6V2F5MlNFV1laT2FEc3VkWi8yS1pLU0drRDVaNkFwMDMxYVp1N3k1QUNvM2psc2d0L0toVmtLVU9UVmxGMThFNzhJZENnem1WMFI4QUpyalJnb3pxSmNDb0M3Z2RjanZDbDdudnR4WVBjNC9WdmZyTmxpVXQzMEwvKzNmSGVFUW81VUVGRlAraHFEb2cxZlB4S253WkxPSlZwU0t5dWd0eHhWM1p6Y1U4UkNVMC9LcFhZbTZFbFRFeHE3Zll5UGIwbkxvUS81MlNpa3B6M1JISXdpbnIxWDFudTFmWWd6MVpoRkI3UWdGWVJZZnRsMnhBTFBSbXhKSkdhL1hVZ3lqcTdDWWE1dlNkeU5xVXBweUNGSG51R3JyT2FJQ3VjYzN1OXJQYklvQS8xbHowU3RKcWRPeGtjczdMUjNzdm04MHRsZEJKUHptczJ4RTYiLCJtYWMiOiI1YmQ2MTZkNDlkYjlmNTcwMDI1NTZiZjk3ZWQzMjMxZmFkYzUyNTBjZmYzNWZjM2ViOThmZDU0Yzk1NzQ5ZmMwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:14:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlJkOFY1STgxeU02Y3dIaTkrNkNTdVE9PSIsInZhbHVlIjoiNFRsb0c2QnNDM2NTeko5MzN6V3V2bCtUVjg1ZkR5bGh5eDNpNGV2SWRzNDA1bEw2bVFvZWRuYldZMG45Vzk2ZFFSZEhrTUQ1bXRPK0JxcFhCeXIrcmg4UlNidDUramZpZTd3a2hwT09LS1JnT0lHWkF1T09hdzFCZnBpUnBmS3RaaDB1NFZkZjBsR1k0TlZVdmw2WXFwcVBFMGIvSVphNE5tR2JGVEVuTGFZcytHMEdza0pjWDM4UWx0SXFRalVKNFA3eXJSMlV1V0N0bUVSOTR3WE85dmhrSjdvR2xVS0lxSjZHOUcrUDZ5a3Q0Tkp1Z3RXdTRBTzhvalJGbTdpaTdVenpnZ3M1Nm40UHlJMXVJVGxYVmtXbFlSVjJPbDB5SmVNY0NUeU15TGQ0L2dxd3hWV1RCdEpvSWJ0MkZZQm9yajFHVnZ2cjhyUmF5MkN0SzVtbEJEZllwUk5YajBnY1FFbm93ekE0c0ZGYlovZTM0N0lWTDRMNXBtT1UxTkpPS3lJdUpyb290aklxSWNlWFZWVUROaUNwZlJYenNnMWp3K0MrV2owOHFXbUxzSHErdmRhdHR3RENlQ0VlZU5tblNhMW5JMnhOSHhJd21BL2RrN3VoT2dGa2t0SVF5Y09tV0pzQ01vRkFhLzdGK25ZaHFkWGlTY0syWWdjbFdMT08iLCJtYWMiOiI5Y2I1MjAzOGYzYjI5OTNmMGNhMjBhMGQ4Nzk2OTVmNmY1ZjcyZDBlYjhjNDc2YzE3ZWJlMmE0YmU3N2E5ZGZkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:14:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjZGakJxeTJPTEhhR2JJY1Y1bGlSTmc9PSIsInZhbHVlIjoiZmlNaHBxQXY1ME4yQVNkcWhiN2ZaZytQK09vUHo3d0svSnp1ZUc4NkgxS2VnNDFyRHMvaUIzbFo4Qk5kYzBkWkJBdkJ3TllWdkNmY1lyZnpvZ28zS29zVEVlRUJBRHE4UmZkS2FqbG5xUWRBcEJKSDlsMGQ1TGI1dlV6ZDc5cGY1WklPcXF0bDk1cHZVUDhmYXEwTC9nY3dTMEVlVUU0SWtmaS9CSEtZOW5Ya0FlZFg2U0p6V2F5MlNFV1laT2FEc3VkWi8yS1pLU0drRDVaNkFwMDMxYVp1N3k1QUNvM2psc2d0L0toVmtLVU9UVmxGMThFNzhJZENnem1WMFI4QUpyalJnb3pxSmNDb0M3Z2RjanZDbDdudnR4WVBjNC9WdmZyTmxpVXQzMEwvKzNmSGVFUW81VUVGRlAraHFEb2cxZlB4S253WkxPSlZwU0t5dWd0eHhWM1p6Y1U4UkNVMC9LcFhZbTZFbFRFeHE3Zll5UGIwbkxvUS81MlNpa3B6M1JISXdpbnIxWDFudTFmWWd6MVpoRkI3UWdGWVJZZnRsMnhBTFBSbXhKSkdhL1hVZ3lqcTdDWWE1dlNkeU5xVXBweUNGSG51R3JyT2FJQ3VjYzN1OXJQYklvQS8xbHowU3RKcWRPeGtjczdMUjNzdm04MHRsZEJKUHptczJ4RTYiLCJtYWMiOiI1YmQ2MTZkNDlkYjlmNTcwMDI1NTZiZjk3ZWQzMjMxZmFkYzUyNTBjZmYzNWZjM2ViOThmZDU0Yzk1NzQ5ZmMwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:14:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1631928538\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-128049256 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-128049256\", {\"maxDepth\":0})</script>\n"}}