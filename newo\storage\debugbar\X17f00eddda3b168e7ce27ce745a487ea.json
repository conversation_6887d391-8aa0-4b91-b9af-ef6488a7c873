{"__meta": {"id": "X17f00eddda3b168e7ce27ce745a487ea", "datetime": "2025-06-08 00:30:34", "utime": **********.388495, "method": "POST", "uri": "/empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749342633.299347, "end": **********.38853, "duration": 1.0891830921173096, "duration_str": "1.09s", "measures": [{"label": "Booting", "start": 1749342633.299347, "relative_start": 0, "end": **********.238536, "relative_end": **********.238536, "duration": 0.9391889572143555, "duration_str": "939ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.238551, "relative_start": 0.9392039775848389, "end": **********.388533, "relative_end": 3.0994415283203125e-06, "duration": 0.14998221397399902, "duration_str": "150ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47349592, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@emptyCart", "namespace": null, "prefix": "", "where": [], "as": "empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1655\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1655-1669</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.025010000000000004, "accumulated_duration_str": "25.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.297827, "duration": 0.02196, "duration_str": "21.96ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 87.805}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.337167, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 87.805, "width_percent": 3.239}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.3671331, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 91.044, "width_percent": 5.358}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.371622, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.401, "width_percent": 3.599}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-847976800 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-847976800\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.378279, "xdebug_link": null}]}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Cart is empty!", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-367118634 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-367118634\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-540084624 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-540084624\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-384686423 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-384686423\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-9300446 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1834 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749342593005%7C4%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImJidjRSRlFmSE1zV1JhWkhlb1IwYkE9PSIsInZhbHVlIjoibnA1NVlRZXlwSkM3QnU4UitUb20yd0NLT1RZOWZwbXVWOU1aSDhLY0dpNEU0U3VzaWdxdWxETkdGTFI3S05HMTk0VjlEbmFYU055RTFTR2ZYZVZZTXc1d05PbFY5S3BlbVRpS08xQkpkL3hoZzdNV2V3NFVKOHVSQzUzNGRHWUdxb2pEMHdiRHphTWEvbW9mTDBhRVJQRjlDd1paanVRRW1aVGc5WHg0Q2FyV1pHZ0JtMUZyMXRjekgraFJKSWRxeHRweitTbUloQWlpalhGc1lDTzh3alN5TlB4TU9laW9Yd0d2MzBNMmE2YTBibDVXZjdGNTgyaGMxVlBNVXZGZ1A2ZDZ6WHh4aUtja1I3YkxKZnZUR1k2SVpGUlJlMkk5NjR4WkU1bFhHV3ZKeThyRWZLa1QzZlJzQ3ZoUFo0bE5zaXVkRVhoYjA0UGpMUFFZL3Zib2pNYzAwbTBPSHVyejdNcUdKajZmNkYxWHd1V2tzOWUxcmZnNHJpbWltaURnaDl5V0MwemF2Sk5YejdnNGNjUEtOUDJhLzUvZTc2WElJWXRSaHFuckdaVHVzZ3RuSmhUWFpRZnh4S0pvbnhpNi9mOVhPK0paTk1sTnBET09KTmo2UC9wc0haY2NGOWgrbFQyS2NIR3RpZDBTc0NtWnBFbXJ2SG5yUGFBNVJFSDgiLCJtYWMiOiIyYTgyOTJkYzUyMzQzMTEwYzQzN2JhZjVjOWQ2OGNhNjQxNDAyOWMwNDUyNzY2ZjdjMzc2NDFjYWJmNzI2ZjBjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IitEaE5RZ0NONUZYMlVVQ3lDMkIxNWc9PSIsInZhbHVlIjoiY0FvT2l0Rmo1eG5DTElsZ3NJMUd4ZmJaVFRCamZBWkhsby9tajZ0Q0twYlgzVC9KR0dSKzNLbWFCR0hGSVRobkpqTmlzWVZjZHBVSDMrT29DRmJWQ2Q1SHpxRUQ3cFFSNkxyZGFBbTIxSjFXRDJiMmZtMzVjdTVsbUFTYXhNc1BqaGt6Vmxab2FDWTdkM1ZYSDlnMllWRU9ObGFkdWRlVlBVemx5eW92MUZNTHUwZmFuWHpFeEJJVE5xcVdwbk40SW4zcVdVUlVPL1EzK1NvbktKMmxVZkduanRsbnJRa3BzMmY2clhyM1o3Y1c5aHBONVFTM2c2NzMrdkhtWktvYVdnbGV5VUYweGlnQ2poNG1uSmlQeDJ0K2R4TmtCTExnY3R5bUVHSlp3bm5HVDcyazU4cGZQZ1FUNVlNNFhGVDZFZmJ0ZGNqUlZQK3p4S0hXOCtrVnJsLzVWeTlUY01MMjExbTFWcmUwTmRDQzhLWnJ6S0tXek1QNk1DempHUnhNbERNQi9zQzFOQ0pWMzl5eHM4d0M4Ynl6eDJvMVlxRkRWQ0FDVzMycmM2S1Vnc2YyYjJYS1ZzWmk0T0JsZXdNcGpySUR3QXhodHh2ZExoejh0WWRVVFlsTW5yZ3hTZmFDV2h2dk82bjl2RmVodWovaUprNWU2djdDTUo2S2R2U04iLCJtYWMiOiJhYmNhMmY0OTVhNGRmZDg4ZTA5ZGVkODcxM2U2NGI2MDdlYTAwMTg0NmFjMjk4NTc1M2Y5NjkwYjI3ZDQyMzk4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-9300446\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-420573573 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-420573573\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-183153690 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:30:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjMrRWxXNW1oZmRzTEtiZE5JRXRBVUE9PSIsInZhbHVlIjoiYXRDNnQvVFJ3c2tjRkFzL1d1WnFUbkVhSStYdzdIYU1JT0lhbzErekFQellkZlpjT2NpdHVEektvWEl4M0RRUm9nZk1sVSsxM29Ga2xDWjNRdXNuTVgzbFI2RlpONXl0bEs0c25iaVBjWFhsT2lhNitjR3JaQmdwM1lrNkl1bU1YQ3kxdDFUckZCKzE0SDZFK1NxQU8rTjgrR3N1UmVBWXVBaVRjMXRkOGJPaFp3UG1OZG5adW0wT2w2VVd0UDZZRCtObG5Mckl4bWgxMUF4YzdMaWhyTkhlRFVUaTZKdC9TTEFYTlkxNGkvU2kwVU5YTUlKU1htdC9JWkFicFpUekcvTDNSV05zcFQrVEtlRk5qNEs4R1UzSlpSMlhmMVhzMFRSZWVGMUNsN3krLzkzNlgxRGtIazhJc1ZuZFFPWUJacDZCY1lON0tmTXNFeU1PclE4N0ltVVBDM29vU0l2RkZ5Skk5enRuNXRQWUxwUWFTSkFIZTBoSlZmNHIweGt4dnNWVWFCVWFPb1F1Q2Zsb2diODNtL1hVTklvQ3VLa0ZlQ3hKMFpQOTYydUhkVWVHMG5UZnQ5MG9tV0F1dGlrd0RmZTVZaUk0VHBWeVZUK201UHpPNkx3MU9GVERxcGQyQlMvY1BHci9JZHdrL2hEdlZYWGNhc0Q1ampxTDVWT3UiLCJtYWMiOiI3YjQ3OGQ3YWQzODdkZGEyODJkY2UxYjMyYTZkZTEzMTIxMzRlZWUxMThiMmMzNTYxMGM4ZGU0MmM5MjlkYjQ5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:30:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImdIenIzenVqTHY5a2dRUDNmTFc0eXc9PSIsInZhbHVlIjoiN3JRSTZTR3NTZkhnMDlJSDNWVHRaWWFSaDVCZjBIblp2NUVTVkNONTdqZW5RTEZuV2wvZC83ekhGUjFsUFI4eUI3ZC9TYVlHb2g0SnVkZFU2TkowZzlWcDI0dTVXL1psUVRoWGxZK3Bobm9RYW9ZMDlJb3Q4bldXR2pDbVpRWnBBb3hNcyt6aC80bGwyMW42SkVMV2l4YTVHRnNmTGlaRkFwLzBiSWYxdlNkendWU0F2aU5KMnNySlFXSFlQcTRtb2U3VE1IajhVR3Q5RHJGOXlSTzRXWDRUSll2K3FCMjFXZzg0VWM3cEc0Z1RPL2IvQzNLNktTNmluQmlTZjkyUVpmdnYrSXk3UWNiZ3kxa1kzR1luK0UvN2dReUJPdmpwUnRHUFdTVnc2V0tlbno1OGp3WXhnN0UxVXZTUWhTdWlLZFJVM2E5d3hmVG9xVklpM3pPdmNzWDlBUDI4U2JXb1lET1QxK2t0VkFiRmpqQmZUYXZPOFVtNnZTMWlTSXlscHJISzI5NUVVbTJUZlh4Z1pRUzBEc1RlU3NMakwzK1hsM2JhRWtCZnd1MzloSDhRMTZUdGFHRHdENDVacW1CRXdCMk5XbG4wYVZtdW5aek15NUdweDU1K1kweXVwam1tWHcrdGF1aTE3dDROY2xsK0NGN1krNEUvNU5qU0ZibzUiLCJtYWMiOiJkMTljYmZhNTU1ZDMzMjU1MWNjYTU5Yzc2NjFlZTZkNjJmZmY3ODZkNjlhYTlkNTk0YTVmZGJhMGUyYzNhM2Y1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:30:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjMrRWxXNW1oZmRzTEtiZE5JRXRBVUE9PSIsInZhbHVlIjoiYXRDNnQvVFJ3c2tjRkFzL1d1WnFUbkVhSStYdzdIYU1JT0lhbzErekFQellkZlpjT2NpdHVEektvWEl4M0RRUm9nZk1sVSsxM29Ga2xDWjNRdXNuTVgzbFI2RlpONXl0bEs0c25iaVBjWFhsT2lhNitjR3JaQmdwM1lrNkl1bU1YQ3kxdDFUckZCKzE0SDZFK1NxQU8rTjgrR3N1UmVBWXVBaVRjMXRkOGJPaFp3UG1OZG5adW0wT2w2VVd0UDZZRCtObG5Mckl4bWgxMUF4YzdMaWhyTkhlRFVUaTZKdC9TTEFYTlkxNGkvU2kwVU5YTUlKU1htdC9JWkFicFpUekcvTDNSV05zcFQrVEtlRk5qNEs4R1UzSlpSMlhmMVhzMFRSZWVGMUNsN3krLzkzNlgxRGtIazhJc1ZuZFFPWUJacDZCY1lON0tmTXNFeU1PclE4N0ltVVBDM29vU0l2RkZ5Skk5enRuNXRQWUxwUWFTSkFIZTBoSlZmNHIweGt4dnNWVWFCVWFPb1F1Q2Zsb2diODNtL1hVTklvQ3VLa0ZlQ3hKMFpQOTYydUhkVWVHMG5UZnQ5MG9tV0F1dGlrd0RmZTVZaUk0VHBWeVZUK201UHpPNkx3MU9GVERxcGQyQlMvY1BHci9JZHdrL2hEdlZYWGNhc0Q1ampxTDVWT3UiLCJtYWMiOiI3YjQ3OGQ3YWQzODdkZGEyODJkY2UxYjMyYTZkZTEzMTIxMzRlZWUxMThiMmMzNTYxMGM4ZGU0MmM5MjlkYjQ5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:30:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImdIenIzenVqTHY5a2dRUDNmTFc0eXc9PSIsInZhbHVlIjoiN3JRSTZTR3NTZkhnMDlJSDNWVHRaWWFSaDVCZjBIblp2NUVTVkNONTdqZW5RTEZuV2wvZC83ekhGUjFsUFI4eUI3ZC9TYVlHb2g0SnVkZFU2TkowZzlWcDI0dTVXL1psUVRoWGxZK3Bobm9RYW9ZMDlJb3Q4bldXR2pDbVpRWnBBb3hNcyt6aC80bGwyMW42SkVMV2l4YTVHRnNmTGlaRkFwLzBiSWYxdlNkendWU0F2aU5KMnNySlFXSFlQcTRtb2U3VE1IajhVR3Q5RHJGOXlSTzRXWDRUSll2K3FCMjFXZzg0VWM3cEc0Z1RPL2IvQzNLNktTNmluQmlTZjkyUVpmdnYrSXk3UWNiZ3kxa1kzR1luK0UvN2dReUJPdmpwUnRHUFdTVnc2V0tlbno1OGp3WXhnN0UxVXZTUWhTdWlLZFJVM2E5d3hmVG9xVklpM3pPdmNzWDlBUDI4U2JXb1lET1QxK2t0VkFiRmpqQmZUYXZPOFVtNnZTMWlTSXlscHJISzI5NUVVbTJUZlh4Z1pRUzBEc1RlU3NMakwzK1hsM2JhRWtCZnd1MzloSDhRMTZUdGFHRHdENDVacW1CRXdCMk5XbG4wYVZtdW5aek15NUdweDU1K1kweXVwam1tWHcrdGF1aTE3dDROY2xsK0NGN1krNEUvNU5qU0ZibzUiLCJtYWMiOiJkMTljYmZhNTU1ZDMzMjU1MWNjYTU5Yzc2NjFlZTZkNjJmZmY3ODZkNjlhYTlkNTk0YTVmZGJhMGUyYzNhM2Y1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:30:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-183153690\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-268989279 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Cart is empty!</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-268989279\", {\"maxDepth\":0})</script>\n"}}