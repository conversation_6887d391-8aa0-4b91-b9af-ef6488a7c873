{"__meta": {"id": "X3f2eab8ef62a93c7ebcd085bd4cb9eb2", "datetime": "2025-06-08 00:40:39", "utime": **********.83007, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.146213, "end": **********.830095, "duration": 0.6838819980621338, "duration_str": "684ms", "measures": [{"label": "Booting", "start": **********.146213, "relative_start": 0, "end": **********.746681, "relative_end": **********.746681, "duration": 0.6004679203033447, "duration_str": "600ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.746695, "relative_start": 0.6004819869995117, "end": **********.830098, "relative_end": 2.86102294921875e-06, "duration": 0.08340287208557129, "duration_str": "83.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45039936, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00434, "accumulated_duration_str": "4.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.788006, "duration": 0.00282, "duration_str": "2.82ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 64.977}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.805889, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 64.977, "width_percent": 21.198}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.817153, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 86.175, "width_percent": 13.825}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial/productservice\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1185945155 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1185945155\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1070494495 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1070494495\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-25665649 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-25665649\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2072359926 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749343217349%7C39%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlFWK2pZMFNKZ1dRY2NNZE1uOW5mQlE9PSIsInZhbHVlIjoiemlERkxNaTJpekk4cUNZdFF6ZzQ4RTkyUVBqK2FPdm8zak1oMDlTWjd4L05oZk9XbTNzN1ZvcXpRRmNxYTZYSytGd3lJcGd6SUM5SlJkdGpOdEd0N0htSFZFTFZGNFZJNDlhQS9xZDc1Y1FJV3JOd05ydUFGSXl5dnhOMU9TM1ZiQ2N6MVQ3WWZ6ZVkwdEdxY0cxVGpabWJKSTQ2WHd5MTlJcVVUNmpRbFhDRjc5ZGVsUlZoL0RJSTJ2dVdDU0QySEFkRWZuM3d0amVlaEwyWmRzTDFOL0xTZkxVUng2ZWZmNkNCUUZNajcrQktYQW01K3BKOHhyVm9icE1WbmpKS3Y3WjJ6YzFDZTFndnlYOE00eGxxVWNrVG9GVzdFMVRXekx2TXZ5WHNKcnJZYk1TWnA3dkpYL3RUd1hoTzFEYUg2R25JM29PbE00NmZJYWxPR0ZXdzJON2FDWXU4QXlaczFWRW1EcVhXaTZmWjlZYk5sMUIvQlhZR1lGbU9qNlRrL2twVXBGMzRjSVh4eFJ6MGJpQ1c4bWh2dVd6bG94eEs2enlNWGZGTG1TZHpvRGVJNEEwc1FBQWJNNHlHNlc3VGQxbjEyUkxHTmRvVzZvd1JyV1pmb3dQOHdkaE9XSS9PL0pqeFhLWklOY2NXK2xQdjQ5eTlxanFDeEZ1dFlzL2siLCJtYWMiOiJmMjQyOGM4YTVhNzllMDNmMGUyZDM1MTMyZmNlMWEwYjUyZWRlZjU4Y2RiMGMzODJmMGQ1NzIyMDI5MTg3ZTJkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlJrSDJhaU5CalR4RVFPRU9WSHpkVkE9PSIsInZhbHVlIjoiQmFlOVNmd1dOeGNRZUZHNlhZaHpRRmM4V3NRakJ5QlNMbHM5ZHdlTVNsd2ZmZW9vMit2RDF6NVNWeU5xN3VVTG4yZDg4YnFoUTFjZEJGRWNHNkxTalJMaUFLSXpSMDZtbEorbTgyZWdHaXZLKzFaL3pXNmR4bW15Z1RCdE9GWUtqV01hNVpHUkx6WFlaT3pyMi9tWC9JcmJ3dG9BeUM3eDdlWHhJMXhLUXlOQ1d3OTIzWWFRL1BpSzNZVlVMeFIzdEhyeml2Nlo3UmYrTC9MUWlhRmc1VkNBRHEvYTBlNGtDRW1YeWdvYjVYOUVDVFAyYm01UUhCOHBsbVlDN1FJQkhwRFZ6V3NZNzcySVQ1cUpWckJqZWVmb3lyYWNQOEZmUnc4UDRSdllVck5rZERSK1hEOWEyTjBYWGN0TkNHam91aHdZMXZZS2R1SmVoMldrdS8vY2swNnFxcVM5SVNuQ1NSSk5WSXRsRGlxTUdIcHd0ajVnbXl4dmRsOU1jeXpkYjN5aUxKK0JKYW02amtINDErNzFhOVlEYTIvdnZhYmNwUVU2NEVmUkNKYXRzOXVlbkR1djdvMWovTVhpbGV2OVVEM3dSdVl6TWNERzlDeVdXOXU0OVNqdVhiSjJFSVBGRlVWQ0JjTXBURjBBdVVQL2tscE50eEduKzB1OUErTG0iLCJtYWMiOiI0ODJjMGQ0MzY2M2E1MjdmM2I3NzMwMWUxYTAyZjk4MjgwNGJiZWMzMWMwYWMyYjhiOTIzYTQyMDFiNDg4ZWNjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2072359926\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-298155690 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-298155690\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1134890404 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:40:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkIxOW1yQjFqb1hhaEowbjE2OHFWeVE9PSIsInZhbHVlIjoiTUk1bzRTZWtVTENaZ3FoaGtFTlhLdThQcUEvclVNVFRJaXpvY1kyMFFhN3MxVXJ2WTZFL1QyRnUrK1Y5M2hoOWhDSHY0c3c4TWIwai9Td2g4QVB3dDhPaUF1cUx5bjZ2dGw4SWozT3ptNURSWTJGaEpielA4S1lyV2N1ZEU0blpma1dHQUVhMElIc1dZM1BrK3pnTEJ4OGQ1Y1VBQ2xtRFFqQktpSTF4TFAyVWQxemZ5WHA5dDZLMDFPb2M4c2liWEpwYzA0K2RuQUJQMVE5c1FwQlJNc1VFc1ozUVViWE9ZUlphYXhhY25VaDJ5RUxsUUdlbW1NOWVjVzUzRWhrNG1MU2dDSTdpdU1IMTlUWDkyR2EzL3MzUU5OeUxzRnBBLzhtRE9JNmFIckp0THFueFlIUk1BZDA3UXVMYXNheEY3WHFpaEZwLzNwekVRSXhWRTZ6bXU0N3pxcHNlWnRSVHBVUjBRSlpMUVdaN3NzekgwcTI3ZnUwZXBVOHVmUU9NZjhBVHpDVjNOb0l4Y3RwcHp1emQ1MStXSmpZdzZRWHBJRWIra0pkd1ppMlgrS0FKMXh1WEhqQ2Nvd1dLSGZpRkVIRno1RTRMZXNsWnpFQy93RHc4SmlmWnBjVk5VcUpCOHE0M2srT3V2SVlrMzB6QWdJYmVNT28zRjVnN0xkUk8iLCJtYWMiOiJkZjZkODI1NjI4YjQ4YmU3YmQ1ZTFlMDBlMDI2YzFhZTdhODBjNjIzODM1ZWY1MWI0OTFhODBkYmVlNzc4OGQ2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:40:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjZjL0hGWjhid0lScmZkZVZ4TWlGU3c9PSIsInZhbHVlIjoiZVVwbU92Y20zYjlZNmN6OFpXa3pDNXVwZjJBWGNGWkdXd2pnemFRMDZSaDRhZldFYkIyUUp4bXhUUWdTaWxNMjIrR0lLN2VwdVJsRmsrWjM0bTdpMERNNzI3aElHbXJBQWZxcDNpWDc5S0d6NklWZnFhVVlQekY0MVJtRHRpYXpaR25uTmdQQ0hVYTlpcndnVlFxYmNRb2VJamYrRHdkb1B6YmZXMzNUdXZyanBqaW4xQkpsUEtaRk9WK1NCRFR1SmtZb1ZJbGtMTDR3ODBadTloZ0tPTGVGR2FNL202U2FkTTVVcnU4V2NuaXVKZjY3MC9jN1hVR0VmTU5ZV0RTVWNWTUkrOE5IZFlCNGt2Y1pVN1pHYkI2c004TkMwNzZnNUlsaVR1T2lLZ1FyMjVaV3haMjdsLzBWWUo2bmV2ZnVGV2UvNS95SXJNT0ZSZmhObURTRVdwUmx2NnVySm8wTzVyeWVTY25pYVlaSzAvOWNFRndsV29jVVNoK0l0cnB6WVl5WC9QajB2RnhXS1N0YW9vajN3aFF6TW1ZVEVkOWZtZEFBYkxWdTkyVFE0WTBpTCt2MnlDYlRRRnNRdWppTkp5WVpvTzhaU3JjdWZBajFjV1o3LzV1NlB2MjN4OHMxYXhDSEYyQktVLzFVM3FNdkhaWE1jbzRkMFRIUkFrbkgiLCJtYWMiOiIzMTc1YjI3N2JhZGMxOTBjOGNkMjNkNDQ5NWVmMzJkYzAzYjU0YzFiYTVmMzdjMDIyZGRmNDJjNGFhNzM5MGE2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:40:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkIxOW1yQjFqb1hhaEowbjE2OHFWeVE9PSIsInZhbHVlIjoiTUk1bzRTZWtVTENaZ3FoaGtFTlhLdThQcUEvclVNVFRJaXpvY1kyMFFhN3MxVXJ2WTZFL1QyRnUrK1Y5M2hoOWhDSHY0c3c4TWIwai9Td2g4QVB3dDhPaUF1cUx5bjZ2dGw4SWozT3ptNURSWTJGaEpielA4S1lyV2N1ZEU0blpma1dHQUVhMElIc1dZM1BrK3pnTEJ4OGQ1Y1VBQ2xtRFFqQktpSTF4TFAyVWQxemZ5WHA5dDZLMDFPb2M4c2liWEpwYzA0K2RuQUJQMVE5c1FwQlJNc1VFc1ozUVViWE9ZUlphYXhhY25VaDJ5RUxsUUdlbW1NOWVjVzUzRWhrNG1MU2dDSTdpdU1IMTlUWDkyR2EzL3MzUU5OeUxzRnBBLzhtRE9JNmFIckp0THFueFlIUk1BZDA3UXVMYXNheEY3WHFpaEZwLzNwekVRSXhWRTZ6bXU0N3pxcHNlWnRSVHBVUjBRSlpMUVdaN3NzekgwcTI3ZnUwZXBVOHVmUU9NZjhBVHpDVjNOb0l4Y3RwcHp1emQ1MStXSmpZdzZRWHBJRWIra0pkd1ppMlgrS0FKMXh1WEhqQ2Nvd1dLSGZpRkVIRno1RTRMZXNsWnpFQy93RHc4SmlmWnBjVk5VcUpCOHE0M2srT3V2SVlrMzB6QWdJYmVNT28zRjVnN0xkUk8iLCJtYWMiOiJkZjZkODI1NjI4YjQ4YmU3YmQ1ZTFlMDBlMDI2YzFhZTdhODBjNjIzODM1ZWY1MWI0OTFhODBkYmVlNzc4OGQ2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:40:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjZjL0hGWjhid0lScmZkZVZ4TWlGU3c9PSIsInZhbHVlIjoiZVVwbU92Y20zYjlZNmN6OFpXa3pDNXVwZjJBWGNGWkdXd2pnemFRMDZSaDRhZldFYkIyUUp4bXhUUWdTaWxNMjIrR0lLN2VwdVJsRmsrWjM0bTdpMERNNzI3aElHbXJBQWZxcDNpWDc5S0d6NklWZnFhVVlQekY0MVJtRHRpYXpaR25uTmdQQ0hVYTlpcndnVlFxYmNRb2VJamYrRHdkb1B6YmZXMzNUdXZyanBqaW4xQkpsUEtaRk9WK1NCRFR1SmtZb1ZJbGtMTDR3ODBadTloZ0tPTGVGR2FNL202U2FkTTVVcnU4V2NuaXVKZjY3MC9jN1hVR0VmTU5ZV0RTVWNWTUkrOE5IZFlCNGt2Y1pVN1pHYkI2c004TkMwNzZnNUlsaVR1T2lLZ1FyMjVaV3haMjdsLzBWWUo2bmV2ZnVGV2UvNS95SXJNT0ZSZmhObURTRVdwUmx2NnVySm8wTzVyeWVTY25pYVlaSzAvOWNFRndsV29jVVNoK0l0cnB6WVl5WC9QajB2RnhXS1N0YW9vajN3aFF6TW1ZVEVkOWZtZEFBYkxWdTkyVFE0WTBpTCt2MnlDYlRRRnNRdWppTkp5WVpvTzhaU3JjdWZBajFjV1o3LzV1NlB2MjN4OHMxYXhDSEYyQktVLzFVM3FNdkhaWE1jbzRkMFRIUkFrbkgiLCJtYWMiOiIzMTc1YjI3N2JhZGMxOTBjOGNkMjNkNDQ5NWVmMzJkYzAzYjU0YzFiYTVmMzdjMDIyZGRmNDJjNGFhNzM5MGE2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:40:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1134890404\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-621304982 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">http://localhost/financial/productservice</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-621304982\", {\"maxDepth\":0})</script>\n"}}