{"__meta": {"id": "X1b5a8a1e994bb75036f2c718613c07cb", "datetime": "2025-06-08 00:28:19", "utime": **********.643135, "method": "GET", "uri": "/customer/check/delivery?customer_id=7", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749342498.815496, "end": **********.643163, "duration": 0.8276669979095459, "duration_str": "828ms", "measures": [{"label": "Booting", "start": 1749342498.815496, "relative_start": 0, "end": **********.544638, "relative_end": **********.544638, "duration": 0.7291419506072998, "duration_str": "729ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.544655, "relative_start": 0.7291591167449951, "end": **********.643167, "relative_end": 4.0531158447265625e-06, "duration": 0.09851193428039551, "duration_str": "98.51ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43910216, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.017329999999999998, "accumulated_duration_str": "17.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.604587, "duration": 0.016309999999999998, "duration_str": "16.31ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 94.114}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.628359, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 94.114, "width_percent": 5.886}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-477484293 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-477484293\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1606316077 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1606316077\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1158917 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1158917\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2040098475 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1834 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749341283326%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im8xYVpleXprSjhYd0dFRkZkMUttNmc9PSIsInZhbHVlIjoiNEtSVDk1N2hnM25pS3JFdXZZYWVOUHovditMTStQbUM0SWRzTHpzdy9KTVRZTDNDTE5lOWlFWlorMHZtK2ZOVzFOc0FzdUU0TkxrVzdiREZoNVNQSytBRUJFRzdML0F0cmJFTExXQ1lSZG1McFZRb2Vwa2xzdUFzS2hrdFBqWHVzWFZhdTlNRFpwUFdqVUxmcG0yemtTWUtreVB6QVhQaDRRTTkvQlByTTZlREtsK1cxNjZjSTRIcjk5bnQxNzRvQ2lmU2hNUSs5VWtPaUhWSDFBOE9yQk9YcDJlN004RlZTaDI3YTlVNTB1N1MvWkRaazE4YktUUmZ2Z3VoZHYrV1dsenhNQkdXOW9nK3A3TUN4NlhSVTJPemc3eGtsSlA1aDAzOFRsd0dqVFFBcTIvczZQSTlyV3IzUjhMVVp4ZXhhaEQ4R0lDdnh4V2pXWldZSXBxVURocVVRbmFpcXJ2QjZnVmZUSDR4Y1RqeThsV3dDcHIvRzk5bFB5TmZIcFl3L083VDNhaXlhajRmU2M0cmtzY2lFcTlReHZ3blpxSzc1Y1F1SkNHRmo4czh6d1NCVU9Ba2RQV1BqYmpLVnJOZjdVclBCaDlBZHo0N1RGNDVGMkJCelZQRVB5dlcvbmg5eU5SQnRrQzVrVmZ0anRpUlRlUUcyM1JBbnhsRUNzUXciLCJtYWMiOiJhZDEyZmQxNzlkOGQ2ZmIwMTA3NjQ2Mjk5Y2VkN2Q3Y2NjZDY2NDVjYWRkMjA3Yzg0NDAyNzUxOWFlYjZkNWZkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InFBdzdmR3EvWnRrVE5HUGx6aVRBYXc9PSIsInZhbHVlIjoiUEpMZ1Q0cWMyKzdiOEFTTldtVnNmUmV3VlQ2bDVIYnMvNTR0a0xsUk9seFZBdDRTSis5a3BkT1VIVDNRZGRZL0I2REhuTkRveGNuVVFid2FpUG1pcnJnQXpIUjdUR3BsbGw2MGMxbXJKTHN0akllR3Yvc0xRcVhpckx0YXZpVE1Sa2cyU0FMZTJlUlNPMjNBYStnRmprM25pajd3UFVycEtlZmJpQmwySWtwdXNNMDlCL1VsZEdUdUVtcksrM3hPREhGZHVPZ1BTbjlFMVkrcVlzMTNXWG9qeG5wbzI0aTRmT09ZS1hvQ0NuT1FUTEtBaWhKdHNxMTBMcGJzRk5BME5PQ3Z2WVFUL0pvem41ZDZrUnlwdGlaRGtoS2tvb0RsM2NFcFJoU2xhOFFmY3JSN0FucU1nR0tvTVRJYndKam9qSGpHR2VXZnNpVGJnVWI5M29RcGlFVUtjejAwNFZXTFNyR056M2gzR1p0UkZwRW5VZk9EaC81b1lHQWwrWHRVL0VoZXdpakdGUEExbXVGYlVzelkxRWNDSkwyOWlNYmdRWGpmeFVJZUJ2NnN5RGNTeVY2b2owUGxmWE9FQUJQWlIxYitERjJSby82UWRWdE1LejdsK01vOHVnOFQ2cVIyTks4aDlSZ1JwRjROQUxaU2xSMDZPMURmcHBEOGpLMDciLCJtYWMiOiI1ZGZhZjE0OGQ5NjRhY2Y0YzgwOWRkYjE5ZTJlY2RjNDlmZTg2MDlmNGQ0ODc5ZTZiYmI1MmZlZTFkZDYwOWU2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2040098475\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1049612591 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1049612591\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-821852906 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:28:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkczMzVacmZnNlgxYnpRWGNhNXJJRVE9PSIsInZhbHVlIjoiUGxyKzBQcFoydnY3dTdGNTJmZVdJajdaaURaOGt2QmV3cUkyemFOMWZnMGlLQmcwNndEMjN0L28vZG04KzQvYndGNEROZ1FSN2x5Q0tpNUdzdnNLVlJKNVhCZzRIcWJJSVZvSklqQlppcURURzdqd1FOaDk2a2dqWGFnUnZhYVhXeHRUZ1U4ZUF1eTk3RkwxSzA0Rm9WeXozRkRGMWpXYW9JOHE5aitpSHRiY0xHMlNLT1ZRYytYOWlVaDNqWnArakNtVFRaYTljaHZGSGNTbk01a1NaNmR3d3Z0aDhQZWQ4RHNvdWFjWWxGcGxiQzZJVTdzbWRQY2xkT09ndGl6Y0dpZlg2L1VRU0E2OFlJZzllWDFiMEx2SFZ2bEpKRU16OU9jZW53RFAvbEJLeHp0WDZDR2xwemNoOGNDNE4xcXZrTk9aMS80elVXRWZVQjVDejVCcTRtSk9vZUtNTFBiTGt0Yk16Tk84UURPV0w4d0JxQWYvQmViOGloTTBWbHBjQ2YxV0JoZE5xK25rYktiQUJOUCtvK0U0RkJjSHpjN0hScTFkelp0QU5BYzREeDF4ZVprRVQzc0ZLQXJoc2lBQmNmbFczeS9kbysyK29FZGh4Tjg2U0x5dGZNMGJwbTNSaHdrVks1ZVV0V3ZPZm05clp4RUhnWlRieFVubmZoWlYiLCJtYWMiOiI4YzFhM2Q3ZGI3OTQxNDdjMDI4OWIyZGIwMzYyOThlNWMzNmEyZjZmZWJhMGMwYzNjMDljY2MyNzA3NjAyNzNmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:28:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkhLeHg3RVgyNVhtVTNkWVBwMlM1Nnc9PSIsInZhbHVlIjoiZHVLazZEc1ZBWml5TDlJeHlpKzd2ZnZINDJ4anBZMWNxaUpCYm5CZ1cwNG55OUlsaGJkdnJIdkpWT1E5TE1UVUdmUG1XM3gwNGJuQnpPcFRFblo5dG1WaVYxU21mWnJFNHJJVFhtTVR0SFRvQTBvWmNCamtsc3VVWnZYTVNhbGVJUEtXMnM2RjdMeGdVbmFzTEtjamd1NzJFMzJsbUkrT25xSjIyRjA1dExnRjlFWUozZFBqcjhsa3pTWUpRS1BsREdVRENBZWg5NkFlSlVneFRFbVRzZkFkQjZXbXJTaUtoMzBQY2FyaWJINDYzOW5rVUdDNllVV2lEVGRReXc2NUJiY2JQRDMxK1Q2ZHFYYnBSaXVDTURTdW44OFBEcmp2N0ZVc1Vxb0FkK1pQbXZOdjVxOGRJTXdMTXNaRnpmK2M3eWFhT2dTZmN3NVVvVlhwc0VHaktQcWlpMkQyMlVIYVRCbzJLWjViYW1WNU9FanpLM09FeXdUcm1aKzJlaEx5SjRienBiR2duL0FwRUhoTVptMGNrNTJxZzJ6TFc2eDVJMk5kUnBoTDBzK2llTFZ1NU1lMml4cjFWb3h4VDF4bFFmY1dTUGYwTmhRVWtRTWljeEMyMjFkdmh5V0dtTWdnK0ZMMDRIU3pJZnVBUUVZRU80WDJZcUlpYjZiNVdtUTAiLCJtYWMiOiI1NjM0YWUzZjk5NTYzNmMyNGJiN2Y2MGY4OTFkNGVjZjZiNmZiNGRiMzgyMzM1MWFkNWYzMzNhOGU3ZjM3ZDJkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:28:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkczMzVacmZnNlgxYnpRWGNhNXJJRVE9PSIsInZhbHVlIjoiUGxyKzBQcFoydnY3dTdGNTJmZVdJajdaaURaOGt2QmV3cUkyemFOMWZnMGlLQmcwNndEMjN0L28vZG04KzQvYndGNEROZ1FSN2x5Q0tpNUdzdnNLVlJKNVhCZzRIcWJJSVZvSklqQlppcURURzdqd1FOaDk2a2dqWGFnUnZhYVhXeHRUZ1U4ZUF1eTk3RkwxSzA0Rm9WeXozRkRGMWpXYW9JOHE5aitpSHRiY0xHMlNLT1ZRYytYOWlVaDNqWnArakNtVFRaYTljaHZGSGNTbk01a1NaNmR3d3Z0aDhQZWQ4RHNvdWFjWWxGcGxiQzZJVTdzbWRQY2xkT09ndGl6Y0dpZlg2L1VRU0E2OFlJZzllWDFiMEx2SFZ2bEpKRU16OU9jZW53RFAvbEJLeHp0WDZDR2xwemNoOGNDNE4xcXZrTk9aMS80elVXRWZVQjVDejVCcTRtSk9vZUtNTFBiTGt0Yk16Tk84UURPV0w4d0JxQWYvQmViOGloTTBWbHBjQ2YxV0JoZE5xK25rYktiQUJOUCtvK0U0RkJjSHpjN0hScTFkelp0QU5BYzREeDF4ZVprRVQzc0ZLQXJoc2lBQmNmbFczeS9kbysyK29FZGh4Tjg2U0x5dGZNMGJwbTNSaHdrVks1ZVV0V3ZPZm05clp4RUhnWlRieFVubmZoWlYiLCJtYWMiOiI4YzFhM2Q3ZGI3OTQxNDdjMDI4OWIyZGIwMzYyOThlNWMzNmEyZjZmZWJhMGMwYzNjMDljY2MyNzA3NjAyNzNmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:28:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkhLeHg3RVgyNVhtVTNkWVBwMlM1Nnc9PSIsInZhbHVlIjoiZHVLazZEc1ZBWml5TDlJeHlpKzd2ZnZINDJ4anBZMWNxaUpCYm5CZ1cwNG55OUlsaGJkdnJIdkpWT1E5TE1UVUdmUG1XM3gwNGJuQnpPcFRFblo5dG1WaVYxU21mWnJFNHJJVFhtTVR0SFRvQTBvWmNCamtsc3VVWnZYTVNhbGVJUEtXMnM2RjdMeGdVbmFzTEtjamd1NzJFMzJsbUkrT25xSjIyRjA1dExnRjlFWUozZFBqcjhsa3pTWUpRS1BsREdVRENBZWg5NkFlSlVneFRFbVRzZkFkQjZXbXJTaUtoMzBQY2FyaWJINDYzOW5rVUdDNllVV2lEVGRReXc2NUJiY2JQRDMxK1Q2ZHFYYnBSaXVDTURTdW44OFBEcmp2N0ZVc1Vxb0FkK1pQbXZOdjVxOGRJTXdMTXNaRnpmK2M3eWFhT2dTZmN3NVVvVlhwc0VHaktQcWlpMkQyMlVIYVRCbzJLWjViYW1WNU9FanpLM09FeXdUcm1aKzJlaEx5SjRienBiR2duL0FwRUhoTVptMGNrNTJxZzJ6TFc2eDVJMk5kUnBoTDBzK2llTFZ1NU1lMml4cjFWb3h4VDF4bFFmY1dTUGYwTmhRVWtRTWljeEMyMjFkdmh5V0dtTWdnK0ZMMDRIU3pJZnVBUUVZRU80WDJZcUlpYjZiNVdtUTAiLCJtYWMiOiI1NjM0YWUzZjk5NTYzNmMyNGJiN2Y2MGY4OTFkNGVjZjZiNmZiNGRiMzgyMzM1MWFkNWYzMzNhOGU3ZjM3ZDJkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:28:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-821852906\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1062212203 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1062212203\", {\"maxDepth\":0})</script>\n"}}