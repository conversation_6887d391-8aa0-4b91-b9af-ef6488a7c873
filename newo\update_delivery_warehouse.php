<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use App\Models\Warehouse;
use Illuminate\Foundation\Application;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "تحديث warehouse_id لمستخدم Delivery...\n";

$deliveryUser = User::where('email', '<EMAIL>')->first();
if ($deliveryUser) {
    // البحث عن أول مستودع متاح
    $warehouse = Warehouse::first();
    if ($warehouse) {
        $deliveryUser->warehouse_id = $warehouse->id;
        $deliveryUser->save();
        echo "✅ تم تحديث warehouse_id لمستخدم Delivery إلى: {$warehouse->id} ({$warehouse->name})\n";
    } else {
        echo "❌ لا توجد مستودعات متاحة\n";
    }
} else {
    echo "❌ مستخدم Delivery غير موجود\n";
}

// تحديث مستخدم Cashier أيضاً
$cashierUser = User::where('email', '<EMAIL>')->first();
if ($cashierUser) {
    $warehouse = Warehouse::first();
    if ($warehouse) {
        $cashierUser->warehouse_id = $warehouse->id;
        $cashierUser->save();
        echo "✅ تم تحديث warehouse_id لمستخدم Cashier إلى: {$warehouse->id} ({$warehouse->name})\n";
    }
}

echo "انتهى التحديث\n";
