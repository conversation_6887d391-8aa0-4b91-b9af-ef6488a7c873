{"__meta": {"id": "X1aa3d6a9cda4815b850f6bfe35bc6df7", "datetime": "2025-06-08 01:15:03", "utime": **********.052131, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749345302.387393, "end": **********.052155, "duration": 0.664762020111084, "duration_str": "665ms", "measures": [{"label": "Booting", "start": 1749345302.387393, "relative_start": 0, "end": 1749345302.969817, "relative_end": 1749345302.969817, "duration": 0.5824239253997803, "duration_str": "582ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749345302.969832, "relative_start": 0.5824389457702637, "end": **********.052157, "relative_end": 1.9073486328125e-06, "duration": 0.08232498168945312, "duration_str": "82.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45055568, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.004350000000000001, "accumulated_duration_str": "4.35ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0133162, "duration": 0.0027400000000000002, "duration_str": "2.74ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 62.989}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.029896, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 62.989, "width_percent": 11.264}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.0394418, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 74.253, "width_percent": 25.747}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/customer\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1707011698 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1707011698\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1341876075 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1341876075\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1317740216 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1317740216\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1881869047 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749345295884%7C54%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlIvN1VndlNXSDJONTRLT0tSdW54dmc9PSIsInZhbHVlIjoiSE44TjB3UXh2R0dQSVg4MUJWVTZ0eXBCbUxvNmZFL2dJdlRXM0Zabkgrb0JyTUN4WHp1d3BjckZZSlNib3IrVHhXRWsxb05xY1ZKUFl4MUE4MVRCVUJ0YnBpNVhqTWJNWU54VGQwaU8yZHg2SjB6STZVQ1dPN1haNnRBbzByVnFqNjNjc3E5MW4rRmlxZXVOQjlpL2xWUkNNb0l4U0NUN0lKSTg2YzM1ekhrMHZBMEV6emZzMmQvc3RPSFFqVndycytPaTI4TXFhcU9nMGcrQ3RCYncvczcvUm9RUkdsd3lrZGJEbmpsdnY3SVJpMzVsUk1GcjdMMUZUQ2tRRTAweWkxSkZHTitSelRLMlluZ3ZWUkxoaC9ZK0VKdmZoV1BGL09zenh3UmpsblAvV1VNQU40dHpOSytSNm0yZWRqVWs4RWdjUEZaZ2Y5K3BpMk5LaWcvUDlOOG5qMUFFaHhHTElJWitXb2pzcFBpampLOGZmSEtxa25oTC83UzVvSzdVOW5YQ0lBSHc2Zkg1c2Vqc3NXRGM0WHNYOWs1NUMxYk5yWkhUV3FrZ3VRMVk4S0hGNmJBU2VWUTZEUkhKUk1SeDJsSFpaL0UvQnNGc3AxQ2w4UHdIOGpXa1VIOGR3eXM0Qlh5RWhYWStYUWk4aHgveEhJYWlodVlaWEMvSWE4blUiLCJtYWMiOiIzYTI4NThmNDFmNmNiYmQ1YWJmZmI3N2JjZWQ4NjM3YWYwNjc4YmY4MWQ2MWEwNGEzMDNhZmE4ODRhNDkyMjQ1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ild3cDJ1MGxLVzV0VjBCRTR4VlpJeUE9PSIsInZhbHVlIjoiY2JiaEd3UkNCSHRIaUtaelpEMUMzV0tDekZNMVlIbXhQeXJsTGR6cUJINmJWYkNKZlhIV2gzdDlwc0JNTjI5MUFzMjhBUGdLWlhsN2dQNEp3K2FYT0ttRXhRckhvYnFxME5ZL0EzY0wyQmpNSlVHcm1NOGJkdmY1NUZNQkhwM0FrdjduUi90TDY3cVZVVlNla3c4bXBicm51NncvbHpDb2dVaDErNS9wcU5sK1pMczVtcUtjT2ZCZjM0NVFsOXhMaUJIemFnaS9jU2VWNUdzajJXbXpTM1cva2YzWDVoS2YvYjhzenZvRkx4dkhJTm0reURycTZDek5CU3ZtamM5dkMxdGlmTnNlWUs5YW5SbFZVSERwZS9wTVdWY3NLUTZEUVZTMzVSTnRWa3l3eGxnM0JFV21UcFRvVUN4NGl5Y0I0QWRhSVlnb0l2aWprNE12SmlqS3ZoZXdxNUdJRU84eXpHUERtbHg5MitOOXRYNzgrRzc2SkRBZGJnVWJ5SGJhTG1lTis3MEtZNWRDZ05UQk1QL1llN3B0MUVaTGgrMm5nbXZYT2R5ZmtOai93TzdJME9KWFAva044UWRudmFSaGpHWjVNNTNGZlVhU2ZBKzVLTnB2UjFGMzdLb1pMNm9CR3h4LzJYRmN6ZTdpWndkbng2aDl3YXNqelZrNmtHblgiLCJtYWMiOiJiZGM4NjU4YmJjMDRhODYxZTlmOWFhZWM1ZmQ5OWI2YjhlYzVkMzc1ZmIzYzM4MDFmMmFmMTFjZmEyNzY3Njk1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1881869047\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-330452829 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-330452829\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1044992945 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 01:15:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZGT1hCR0Q5WThGSXRZejZ1L2JuTEE9PSIsInZhbHVlIjoiRVAwdUFDR0Z4TVhueGNOejVmUlhJYjRoNytyL3owVXVhSGwzMk92NjlaTEtjZnRIZkdkTnBBZDIzQU9CSGtUZktyYSs3Mk5PYm1LdE9ZajNBTnltYy9kZjZlWDQrZzZzRlgreXVtYzFnNnBPZENETHBCTVEwOHRFbHpCVDRhejJ0RjNVci9adzdxNHkzdWowNW5maU04UlF4SmFFRVJ4cXNmb1NJVG1SYVY3ZFVkekphcFA4WGFSczhWLysvYnVIUEJyTzJiNmRtQysxaFpTRDlQWHhIcW55WkhkdzlvV3BoV3JETFlnSG13czM3TC9JVFN0bUhPVi90V1I1S1RtbDRlWkpTT1lxUFk1cW5kTHFRTGRGcVptYi9YVGdQUVIrN0ppRCtBc2hUMlNrbDRKYTMzcHNkelduSEpiYVNCUUFJc21Kam54K01pSTlUVDUzUk13dXl0d05WZmFFR0RrUURwOFQrc3hZUEQ5YTgwTDRDMEhmUjArenVZNXc0UlhaNmxMa3BaVzVDQkk3RjRGYStTbG5TSytmQ3FsRVJLeldVQlUxWW9wS0liZnZSTDJSeTF4RG5DK2M5blBxeFFNMUZOa0M4aW5vUEV6UkI2dzlLb21IZVlmTXlrcUp5RW1VeGZOa2dPNUNuUWtiM3dxbTRVZUVHWE11cFh2QUNSOUwiLCJtYWMiOiJmMmFjZjhjNTI0OGI0M2E1NmFjNWJlYTdjNWQ2ZGU1MjZiOWJhN2U2MWQ2YWE1MjNmMmMxMmQ1N2Y0ODNiYzYyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:15:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IklhL2tKVUs0RFB4RnFZbnVTTzJrZkE9PSIsInZhbHVlIjoiTno3MmF4K29pNlVIa2poMHJFUWhWWnZpK1o0UFA3cERSZW1qUGg0blI4VGZDVEFFSjJUNzdoRnVibG5EVGNFUFVmdEFCaHlOVEZEb1UzbmhRQXhwRUg0ZGMzamtZYjVUdk8vSk5CZ1lyTFI3WlhPa0lMYXViOHVBNW9tRzVVblFhcmYzUjdQNGVPdmEyQ3R1bUVqSVViZU1XdmI1dnVTS2txUXVJb2lGMjFJN1hjNlc1c0gzTFM0TGs2RVlkbVNDeUxiYml0aUNENGhwOUJjOTgrSnp0dFJUcjk5NmJnczQyWHJTSlcvN3RadmpEMlJDdzVvcjBpdkZZazU5Z29RVkIwbmo3QTcxUVJaekNTckFVSVVxMDFZK2RGdUh4SGJpZzN2Q0tDTnFkT3RuQzl2c3l4cGtpMGprV1hUUTU4Z1piUitKV2txSUxkaTFjU3V2ZWZ0TzZlWDJLRGlCRVZ6WStEQ2szOHl6bVBQdUJWTVVGUndwa1dSMzV4NWhIOGNTcG5JVkVMdXcrS3pNQWlLQzVoU2s0alBLN0pTNEVQUi81T0NXaENVci9LYXlvRU9BcmRLSWNSRDVaOENVR3l3bTBIVzRVMWxjemtOZWxLODAwenpQWGlUb0JwNDNTZ0tZZlphZitrUlQzSGw0Vjlib2dmRVVIRW80dkZWejlWZzciLCJtYWMiOiJmNzRiZTMyMjI1OGQ0YTk4N2M4OTY1N2RiNWE1MjkzNDgzYzM1MTIyMjY2YTllOGI1ZjAwMDY4OGM4MDFkMDBmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:15:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZGT1hCR0Q5WThGSXRZejZ1L2JuTEE9PSIsInZhbHVlIjoiRVAwdUFDR0Z4TVhueGNOejVmUlhJYjRoNytyL3owVXVhSGwzMk92NjlaTEtjZnRIZkdkTnBBZDIzQU9CSGtUZktyYSs3Mk5PYm1LdE9ZajNBTnltYy9kZjZlWDQrZzZzRlgreXVtYzFnNnBPZENETHBCTVEwOHRFbHpCVDRhejJ0RjNVci9adzdxNHkzdWowNW5maU04UlF4SmFFRVJ4cXNmb1NJVG1SYVY3ZFVkekphcFA4WGFSczhWLysvYnVIUEJyTzJiNmRtQysxaFpTRDlQWHhIcW55WkhkdzlvV3BoV3JETFlnSG13czM3TC9JVFN0bUhPVi90V1I1S1RtbDRlWkpTT1lxUFk1cW5kTHFRTGRGcVptYi9YVGdQUVIrN0ppRCtBc2hUMlNrbDRKYTMzcHNkelduSEpiYVNCUUFJc21Kam54K01pSTlUVDUzUk13dXl0d05WZmFFR0RrUURwOFQrc3hZUEQ5YTgwTDRDMEhmUjArenVZNXc0UlhaNmxMa3BaVzVDQkk3RjRGYStTbG5TSytmQ3FsRVJLeldVQlUxWW9wS0liZnZSTDJSeTF4RG5DK2M5blBxeFFNMUZOa0M4aW5vUEV6UkI2dzlLb21IZVlmTXlrcUp5RW1VeGZOa2dPNUNuUWtiM3dxbTRVZUVHWE11cFh2QUNSOUwiLCJtYWMiOiJmMmFjZjhjNTI0OGI0M2E1NmFjNWJlYTdjNWQ2ZGU1MjZiOWJhN2U2MWQ2YWE1MjNmMmMxMmQ1N2Y0ODNiYzYyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:15:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IklhL2tKVUs0RFB4RnFZbnVTTzJrZkE9PSIsInZhbHVlIjoiTno3MmF4K29pNlVIa2poMHJFUWhWWnZpK1o0UFA3cERSZW1qUGg0blI4VGZDVEFFSjJUNzdoRnVibG5EVGNFUFVmdEFCaHlOVEZEb1UzbmhRQXhwRUg0ZGMzamtZYjVUdk8vSk5CZ1lyTFI3WlhPa0lMYXViOHVBNW9tRzVVblFhcmYzUjdQNGVPdmEyQ3R1bUVqSVViZU1XdmI1dnVTS2txUXVJb2lGMjFJN1hjNlc1c0gzTFM0TGs2RVlkbVNDeUxiYml0aUNENGhwOUJjOTgrSnp0dFJUcjk5NmJnczQyWHJTSlcvN3RadmpEMlJDdzVvcjBpdkZZazU5Z29RVkIwbmo3QTcxUVJaekNTckFVSVVxMDFZK2RGdUh4SGJpZzN2Q0tDTnFkT3RuQzl2c3l4cGtpMGprV1hUUTU4Z1piUitKV2txSUxkaTFjU3V2ZWZ0TzZlWDJLRGlCRVZ6WStEQ2szOHl6bVBQdUJWTVVGUndwa1dSMzV4NWhIOGNTcG5JVkVMdXcrS3pNQWlLQzVoU2s0alBLN0pTNEVQUi81T0NXaENVci9LYXlvRU9BcmRLSWNSRDVaOENVR3l3bTBIVzRVMWxjemtOZWxLODAwenpQWGlUb0JwNDNTZ0tZZlphZitrUlQzSGw0Vjlib2dmRVVIRW80dkZWejlWZzciLCJtYWMiOiJmNzRiZTMyMjI1OGQ0YTk4N2M4OTY1N2RiNWE1MjkzNDgzYzM1MTIyMjY2YTllOGI1ZjAwMDY4OGM4MDFkMDBmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:15:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1044992945\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-965421732 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-965421732\", {\"maxDepth\":0})</script>\n"}}