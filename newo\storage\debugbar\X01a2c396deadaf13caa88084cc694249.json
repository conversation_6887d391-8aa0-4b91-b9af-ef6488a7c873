{"__meta": {"id": "X01a2c396deadaf13caa88084cc694249", "datetime": "2025-06-08 01:14:55", "utime": **********.894841, "method": "GET", "uri": "/customer/7/edit", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.155694, "end": **********.894873, "duration": 0.7391788959503174, "duration_str": "739ms", "measures": [{"label": "Booting", "start": **********.155694, "relative_start": 0, "end": **********.745262, "relative_end": **********.745262, "duration": 0.5895678997039795, "duration_str": "590ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.745276, "relative_start": 0.5895819664001465, "end": **********.894876, "relative_end": 3.0994415283203125e-06, "duration": 0.14960002899169922, "duration_str": "150ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52013104, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "1x customer.edit", "param_count": null, "params": [], "start": **********.879131, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/customer/edit.blade.phpcustomer.edit", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fcustomer%2Fedit.blade.php&line=1", "ajax": false, "filename": "edit.blade.php", "line": "?"}, "render_count": 1, "name_original": "customer.edit"}, {"name": "1x components.required", "param_count": null, "params": [], "start": **********.885825, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.required"}, {"name": "1x components.mobile", "param_count": null, "params": [], "start": **********.888436, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/components/mobile.blade.phpcomponents.mobile", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fcomponents%2Fmobile.blade.php&line=1", "ajax": false, "filename": "mobile.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.mobile"}]}, "route": {"uri": "GET customer/{customer}/edit", "middleware": "web, verified, auth, XSS, revalidate", "as": "customer.edit", "controller": "App\\Http\\Controllers\\CustomerController@edit", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=175\" onclick=\"\">app/Http/Controllers/CustomerController.php:175-191</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.024620000000000003, "accumulated_duration_str": "24.62ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7854438, "duration": 0.018690000000000002, "duration_str": "18.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 75.914}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.817784, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 75.914, "width_percent": 2.6}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.8408139, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 78.513, "width_percent": 6.702}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.8452868, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 85.215, "width_percent": 2.437}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 179}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.852757, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:179", "source": "app/Http/Controllers/CustomerController.php:179", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=179", "ajax": false, "filename": "CustomerController.php", "line": "179"}, "connection": "ty", "start_percent": 87.652, "width_percent": 3.534}, {"sql": "select `custom_field_values`.`value`, `custom_fields`.`id` from `custom_field_values` inner join `custom_fields` on `custom_field_values`.`field_id` = `custom_fields`.`id` where `custom_fields`.`module` = 'customer' and `record_id` = 7", "type": "query", "params": [], "bindings": ["customer", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/CustomField.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\CustomField.php", "line": 63}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 180}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.856555, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "CustomField.php:63", "source": "app/Models/CustomField.php:63", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomField.php&line=63", "ajax": false, "filename": "CustomField.php", "line": "63"}, "connection": "ty", "start_percent": 91.186, "width_percent": 4.062}, {"sql": "select * from `custom_fields` where `created_by` = 15 and `module` = 'customer'", "type": "query", "params": [], "bindings": ["15", "customer"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 182}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.860963, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:182", "source": "app/Http/Controllers/CustomerController.php:182", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=182", "ajax": false, "filename": "CustomerController.php", "line": "182"}, "connection": "ty", "start_percent": 95.248, "width_percent": 2.153}, {"sql": "select `name`, `id` from `warehouses` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 183}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.864828, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:183", "source": "app/Http/Controllers/CustomerController.php:183", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=183", "ajax": false, "filename": "CustomerController.php", "line": "183"}, "connection": "ty", "start_percent": 97.4, "width_percent": 2.6}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => edit customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1808482436 data-indent-pad=\"  \"><span class=sf-dump-note>edit customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">edit customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1808482436\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.851229, "xdebug_link": null}]}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/customer\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/customer/7/edit", "status_code": "<pre class=sf-dump id=sf-dump-1009033017 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1009033017\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-821932858 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-821932858\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1599973033 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1599973033\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-854429424 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749345292083%7C53%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InJ6bE9Md0N3MmErWkNFYW1IaW5aK1E9PSIsInZhbHVlIjoibHNUVTRsY2tVdzc1TUpOT2JvMlRTTHo0S1p2VjFidlV0L3MxOUNtekJwU2tOdmNpQzZsRWFDZFlVd2hkbVJERWxUaDdEY3RHcjZUeDRiSEUyNHZkSS96Ykk5bWxlOVhUd3hpWEJGWTRIdXE3TEZMZTE1WGZ3cVEvc25IbG5pSjRiR1ltS0RxQktFWVR1RWZFd3dtK3cvVUNxaFlTSVNHdVlnRklmZlUvYjc0eXQrbGlJSnNyN3psUElKSjQ4dXFpQk8vTVFLcHY5Q1h4VHU1YmNzZk1KSUZNL0NYZ0RjTXJHbFgvREJ3bUZiOE5BVGx0eENhc1VVTzhUUWhiWmN5WlpFU0VJZVV2b0VlOVZQN1VpRi8rS3RucXlKdUdzNUt4TFhiNjRsRmFtemkzczJEVUJNVGkrYzlQMGEzM2o2WVNpMWRyVGlpUlo4dlQ4T09TbmY2QTJlVkNaTnZEK0NNdTgzSDRYZkM0UWVZMVU0MXB0RCs2NENhQXhqcUZvZ2hIK0dWTUdHTHV3N0dKUDBieUFyZnJEclFRZVYxTXBQQ3U4Q1dYbk5FWmNzMkh5eWsreEhDVjVxTHZkUTBTWTUyMEIvMWZNTlZBYzltTzNSV29jdGZ4UGtYcXBpTVhKZnl5N002NG1ReUY2U1hZVXoxNjc2N0JSblFJTHQ5YnpEcVIiLCJtYWMiOiJhODM0MWQxOTg0YjQ0OWFlYTNhOTdiZTEzYmZmODRlMzU2NTk5MDkyM2ZlZWRlOGVhYzIyYzk0YThlM2ZjZDkzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjcyWUxRY2xJai9SSGNteG1NeTF6bkE9PSIsInZhbHVlIjoiSjAvTFVxeUJ3c0JuSWpzOC9sRTRvQ2RqRWJTYnloL1AzcHg1N1lpVXNFNlJRM01zTTRQQ3F3U1pDRkFTTUtHeEVqTFBXSFRiQW12b2tDdzgzM2FQU0tlZjhvUURlZ0JRZEgyb0RhZE4rekg2Z2RIUy9leU03Ty9ncnZBWVJQOXM2MHhpRjB6Z04vV2kzN0NpSWk1eXlpVzI4NnU5TlRYRlAxYTgyS0pzYm5Mb1FKVnpOcDF2SDhlR1pmbmRLRWFPVXFSZ1BkMTFocWVCTGV5TlZxVFdTSGQ4RzJMY3owOE55SlFpdmpXdlNqbGZKU3lFc20vZU9UbWJBdU1pUjhRUS9xL3N0OFZhUG15NXZQQUtRNDdmazR2dFNtdUQxeEliaC8wTjN4akZIMUVBaFJ6ak5SYndXYXk3cmo1czdtemgxMG5BVzVweXY1ZXpsTHE5ODFBLzgrd1lOckZxYmZiVWkwb0JFeVh3bjdnWTR4SFBVZTJFTTNIZDZQN1JQR1IrYXBHWmNHME9RVno1UzJ0V0kzcmpRTkU5N2xOUkpmYUFQSXgvbWhLNnBpQlBHbUFhRGVUNFNyYkprbWMxbXpLVEVTT0kxcnlINXBLWjFBWDBzQ2ZjMDNycjI3bGQyV01lcERuanQ4OUpoMGhRRmVucmRraTJVaWpxVUNPZXNzOVQiLCJtYWMiOiI3OWUyYzc1YjJmOTAzNzNkNDllOWYwMzNkYWMyYmY3MjY5YTQyNWFmOTU1ZThkMDZhZDM1NzYwMDdiNjcyZTlhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-854429424\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1151122288 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1151122288\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1609254300 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 01:14:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkVjZit0cjZ3aW1uNzRpbEFndFcxVWc9PSIsInZhbHVlIjoiMGprcmMyRUJjU1R0aS9SVm5na1Fud2J1M0kzZ0xSUDhobFpwK2xtWmptRFF4cEpyOHlsV0RmTVVjM2Vka2VCN1FvUXJ5UU02am92Tm9HL215VFR3aVlBYmkyaFUwdlEvSFJGMWtoSDZNU2EwaEhXSGU4RjFEZXRSWEQxSHpUYkJ1TGx1UmJxUzg2b0VsVDFOVjJTUG9HUkFRVXRsS1hJMy96NVNUNWo1VGVxQUE4QjdqSUVraUE2QUFlU2JJM2tLRDc1TjA2MUxqOWhFekRyemUyeWZvNEJtWkpQcEtHUCtpUlVCMXVsbjVtbS9QeHdOUVB5TXpzU3BhRlpoaG9MaU1IeVpTTjlqTTFuTjNFS1FQYnpQR2htc3gxV0ZZeWwzS2lyRk5nYkNFWXdQZWN0NEZmdlNXU2srNzQrVVhqZEMrQWhrY2piMXR0RGwxYWRmdVBxajdjTndDbzZmemZ0Zzh4cnNjSElDb3BFbDV3NC9ycUQ2TENvdHBvYk4xN3N3QXJKUXVlTFBFUUVHVHFndm45MUFVeU1uU1BGVmQ4VFV5Ylh4Y0wzb25zWU1aVXJ5M1NWYUNOR09NOFdwVWNvSVE2emRxbzRralYxcWlnRVI5YnFFUkZ3STNPOG1rT1d5NGs0b0dLTCtDYVBSMmV1Uyt5WVFMNmVUd0t3VHJsbHkiLCJtYWMiOiI3NjUxYjA0YzM0MmY2MjY3ODRlMGRlZTcyNDllODRlNDUzM2M2MGJhYTA0YzQyNmQ0Yjc4YWRhZmVjZmQ2NGUxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:14:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlUzdi95ejlWbEJ4dDVZM01IV0lVbkE9PSIsInZhbHVlIjoiRjVzdGQxMzM0OTVyMjhIY09KaTlSMllIcldUTHhQeklsUFdneUl4SHVmc2VjUmI3REI2em9vLzZOMjU2aEdQZHhXUVpRalA3Nlc2Q2ZKdGNEdlpmOWFxQW0yL2cxTGo1d0RnWll0cGFhOUNRMjNSaHlJL0pvOVZ6SjdIL3ZjWGttc2tEVDAvY0ZDZ1prWnhGNDYxRTNFYkJCanJHU1BPcitYZUpQTy9wQmhoRVZoWWdFejBRdWtVdE9Fc2JwSUczZTRxbUMydE9JUWFNSHdjejhyWlRLNnA3QjFnSXVtUFdMOGxZbC9KcHU5VDNuc1g1MTI1anFvWVc0TTRncWQvQ1FlVWJUd0paZmcxRzAwQ1NuNUppOWxldnhkOVdjTlV2VldRVE5lVUw5SnR6ZVJtYkc4MWlUa09pYy94THJSWlZzdDhZY0ZkR3ZrT08xckNmdHUyS2pKbjhxWmJTNWYyTUgzcXJBN0loM1JadndDRFY3TGl6d0ZsT3ZIS3NOcFgxREdLS1Y1MTFHMVIzSHNmYkhPUHM5dU1oZzk0Vmh0cHh6T1h2QTJOZTF4Y283VXJMOGhkbTYxK1NkWlZiV0FFMTliWFF3RGRoMjBuWWxJelBWZ3pvRkR5K096VlZSL01lZlllYS82WkVjcm94SklyZjlOays4MXhGb2owSnNZTWIiLCJtYWMiOiJkMzc4ZjYzN2VmODRiZWViMTk2YmI2MjlhNzM1YjNiNDFkOTQzN2NkM2FkODA3ZjFkYTZjYjkwYWE0MjQxZDRkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:14:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkVjZit0cjZ3aW1uNzRpbEFndFcxVWc9PSIsInZhbHVlIjoiMGprcmMyRUJjU1R0aS9SVm5na1Fud2J1M0kzZ0xSUDhobFpwK2xtWmptRFF4cEpyOHlsV0RmTVVjM2Vka2VCN1FvUXJ5UU02am92Tm9HL215VFR3aVlBYmkyaFUwdlEvSFJGMWtoSDZNU2EwaEhXSGU4RjFEZXRSWEQxSHpUYkJ1TGx1UmJxUzg2b0VsVDFOVjJTUG9HUkFRVXRsS1hJMy96NVNUNWo1VGVxQUE4QjdqSUVraUE2QUFlU2JJM2tLRDc1TjA2MUxqOWhFekRyemUyeWZvNEJtWkpQcEtHUCtpUlVCMXVsbjVtbS9QeHdOUVB5TXpzU3BhRlpoaG9MaU1IeVpTTjlqTTFuTjNFS1FQYnpQR2htc3gxV0ZZeWwzS2lyRk5nYkNFWXdQZWN0NEZmdlNXU2srNzQrVVhqZEMrQWhrY2piMXR0RGwxYWRmdVBxajdjTndDbzZmemZ0Zzh4cnNjSElDb3BFbDV3NC9ycUQ2TENvdHBvYk4xN3N3QXJKUXVlTFBFUUVHVHFndm45MUFVeU1uU1BGVmQ4VFV5Ylh4Y0wzb25zWU1aVXJ5M1NWYUNOR09NOFdwVWNvSVE2emRxbzRralYxcWlnRVI5YnFFUkZ3STNPOG1rT1d5NGs0b0dLTCtDYVBSMmV1Uyt5WVFMNmVUd0t3VHJsbHkiLCJtYWMiOiI3NjUxYjA0YzM0MmY2MjY3ODRlMGRlZTcyNDllODRlNDUzM2M2MGJhYTA0YzQyNmQ0Yjc4YWRhZmVjZmQ2NGUxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:14:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlUzdi95ejlWbEJ4dDVZM01IV0lVbkE9PSIsInZhbHVlIjoiRjVzdGQxMzM0OTVyMjhIY09KaTlSMllIcldUTHhQeklsUFdneUl4SHVmc2VjUmI3REI2em9vLzZOMjU2aEdQZHhXUVpRalA3Nlc2Q2ZKdGNEdlpmOWFxQW0yL2cxTGo1d0RnWll0cGFhOUNRMjNSaHlJL0pvOVZ6SjdIL3ZjWGttc2tEVDAvY0ZDZ1prWnhGNDYxRTNFYkJCanJHU1BPcitYZUpQTy9wQmhoRVZoWWdFejBRdWtVdE9Fc2JwSUczZTRxbUMydE9JUWFNSHdjejhyWlRLNnA3QjFnSXVtUFdMOGxZbC9KcHU5VDNuc1g1MTI1anFvWVc0TTRncWQvQ1FlVWJUd0paZmcxRzAwQ1NuNUppOWxldnhkOVdjTlV2VldRVE5lVUw5SnR6ZVJtYkc4MWlUa09pYy94THJSWlZzdDhZY0ZkR3ZrT08xckNmdHUyS2pKbjhxWmJTNWYyTUgzcXJBN0loM1JadndDRFY3TGl6d0ZsT3ZIS3NOcFgxREdLS1Y1MTFHMVIzSHNmYkhPUHM5dU1oZzk0Vmh0cHh6T1h2QTJOZTF4Y283VXJMOGhkbTYxK1NkWlZiV0FFMTliWFF3RGRoMjBuWWxJelBWZ3pvRkR5K096VlZSL01lZlllYS82WkVjcm94SklyZjlOays4MXhGb2owSnNZTWIiLCJtYWMiOiJkMzc4ZjYzN2VmODRiZWViMTk2YmI2MjlhNzM1YjNiNDFkOTQzN2NkM2FkODA3ZjFkYTZjYjkwYWE0MjQxZDRkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:14:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1609254300\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1709014431 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1709014431\", {\"maxDepth\":0})</script>\n"}}