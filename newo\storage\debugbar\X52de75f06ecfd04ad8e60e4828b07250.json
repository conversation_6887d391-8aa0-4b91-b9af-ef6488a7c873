{"__meta": {"id": "X52de75f06ecfd04ad8e60e4828b07250", "datetime": "2025-06-08 00:28:28", "utime": **********.408413, "method": "GET", "uri": "/printview/pos?vc_name=7&user_id=17&warehouse_name=8&quotation_id=0", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 163, "messages": [{"message": "[00:28:28] LOG.warning: Implicit conversion from float 160.79999999999998 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 186", "message_html": null, "is_string": false, "label": "warning", "time": **********.331693, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 1.4 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.333098, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 3.5999999999999996 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.33391, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 3.8 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.334188, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 7.199999999999999 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.334394, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 7.399999999999999 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.334594, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 13.2 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.334759, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 13.399999999999999 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.334977, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 16.799999999999997 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.33519, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 16.999999999999996 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.335362, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 20.399999999999995 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.335531, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 21.799999999999994 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.335688, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 26.399999999999995 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.33586, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 28.999999999999996 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.336031, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 31.199999999999996 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.336216, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 33.8 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.336456, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 37.4 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.336645, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 39.6 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.336871, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 39.800000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.337069, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 44.400000000000006 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.337246, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 47.00000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.337453, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 49.20000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.337698, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 50.60000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.337959, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 52.80000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.338199, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 54.20000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.338568, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 56.40000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.338998, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 59.000000000000014 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.339276, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 61.20000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.33956, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 61.40000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.339799, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 66.00000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.340076, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 66.20000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.340308, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 68.40000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.340543, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 71.00000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.340776, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 73.20000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.341006, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 77.00000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.341242, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 79.20000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.341472, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 80.60000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.341698, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 82.80000000000003 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.341925, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 84.20000000000003 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.342144, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 87.60000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.342386, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 89.00000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.342649, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 92.40000000000005 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.342872, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 93.80000000000005 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.343104, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 96.00000000000006 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.343336, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 97.40000000000006 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.343559, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 100.80000000000007 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.343798, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 102.20000000000007 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.344021, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 105.60000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.344269, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 105.80000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.344503, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 108.00000000000009 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.344727, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 111.80000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.344955, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 114.00000000000009 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.345189, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 116.60000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.345437, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 118.80000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.345677, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 121.40000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.345911, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 123.60000000000008 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.346149, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 125.00000000000009 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.346376, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 127.20000000000009 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.346676, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 129.8000000000001 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.346927, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 132.00000000000009 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.347203, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 132.20000000000007 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.347429, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 134.40000000000006 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.347681, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 134.60000000000005 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.347961, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 139.20000000000005 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.348229, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 143.00000000000006 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.348502, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 145.20000000000005 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.348773, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 146.60000000000005 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.348999, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 151.20000000000005 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.349374, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 153.80000000000004 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.349657, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 156.00000000000003 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.349903, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 156.20000000000002 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.350132, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 158.4 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.350349, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 159.8 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.350573, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 160.8 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.350862, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 159.8 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.351114, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 160.8 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.351308, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 159.8 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\DNS1D.php on line 214", "message_html": null, "is_string": false, "label": "warning", "time": **********.351487, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 0.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.36676, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 1.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.367274, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 2.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.367625, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 3.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.367945, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 4.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.368237, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 5.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.368528, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 6.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.368835, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 7.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.369166, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 8.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.369543, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 9.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.369979, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 10.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.370381, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 11.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.370785, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 12.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.371115, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 13.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.371599, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 14.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.371828, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 15.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.372015, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 16.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.372252, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 17.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.372509, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 18.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.372768, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 19.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.37304, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 20.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.373296, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 21.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.373551, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 22.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.373799, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 23.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.37406, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 24.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.374314, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 25.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.374567, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 26.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.374813, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 27.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.375054, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 28.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.375229, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 29.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.375403, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 30.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.375674, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 31.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.375863, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 32.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.376056, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 33.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.376294, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 34.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.376485, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 35.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.376692, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 36.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.37692, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 37.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.377171, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 38.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.377396, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 39.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.377633, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 40.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.377863, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 41.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.378147, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 42.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.378375, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 43.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.378601, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 44.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.378832, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 45.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.379088, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 46.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.379323, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 47.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.379567, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 48.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.379804, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 49.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.380037, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 50.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.380302, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 51.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.380541, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 52.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.380793, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 53.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.381044, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 54.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.381314, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 55.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.381612, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 56.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.381868, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 57.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.382149, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 58.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.382391, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 59.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.382682, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 60.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.382971, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 61.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.383246, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 62.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.383483, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 63.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.383714, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 64.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.383958, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 65.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.384191, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 66.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.384422, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 67.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 874", "message_html": null, "is_string": false, "label": "warning", "time": **********.384658, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 0.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.384895, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 1.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.385171, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 2.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.385429, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 3.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.385681, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 4.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.385923, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 5.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.38616, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 6.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.386404, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 7.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.386639, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 8.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.386883, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 9.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.387217, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 10.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.387468, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 11.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.387731, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 12.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.387986, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 13.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.38824, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 14.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.388474, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 15.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.388722, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 16.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.38897, "xdebug_link": null, "collector": "log"}, {"message": "[00:28:28] LOG.warning: Implicit conversion from float 17.5 to int loses precision in C:\\laragon\\www\\to\\newo\\vendor\\milon\\barcode\\src\\Milon\\Barcode\\QRcode.php on line 878", "message_html": null, "is_string": false, "label": "warning", "time": **********.389205, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1749342507.233854, "end": **********.408757, "duration": 1.1749029159545898, "duration_str": "1.17s", "measures": [{"label": "Booting", "start": 1749342507.233854, "relative_start": 0, "end": **********.029258, "relative_end": **********.029258, "duration": 0.7954039573669434, "duration_str": "795ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.029276, "relative_start": 0.795421838760376, "end": **********.40876, "relative_end": 3.0994415283203125e-06, "duration": 0.3794841766357422, "duration_str": "379ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 54701728, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.printview", "param_count": null, "params": [], "start": **********.296383, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/pos/printview.blade.phppos.printview", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fpos%2Fprintview.blade.php&line=1", "ajax": false, "filename": "printview.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.printview"}]}, "route": {"uri": "GET printview/pos", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\PosController@printView", "namespace": null, "prefix": "", "where": [], "as": "pos.printview", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1306\" onclick=\"\">app/Http/Controllers/PosController.php:1306-1408</a>"}, "queries": {"nb_statements": 10, "nb_failed_statements": 0, "accumulated_duration": 0.03960000000000001, "accumulated_duration_str": "39.6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.105926, "duration": 0.027960000000000002, "duration_str": "27.96ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 70.606}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.155156, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 70.606, "width_percent": 1.97}, {"sql": "select * from `customers` where `name` = '7' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["7", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 1314}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.163024, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1314", "source": "app/Http/Controllers/PosController.php:1314", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1314", "ajax": false, "filename": "PosController.php", "line": "1314"}, "connection": "ty", "start_percent": 72.576, "width_percent": 4.874}, {"sql": "select * from `warehouses` where `id` = '8' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["8", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 1315}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.169968, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1315", "source": "app/Http/Controllers/PosController.php:1315", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1315", "ajax": false, "filename": "PosController.php", "line": "1315"}, "connection": "ty", "start_percent": 77.449, "width_percent": 2.803}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.2025452, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 80.253, "width_percent": 3.434}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.207751, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 83.687, "width_percent": 3.864}, {"sql": "select * from `pos` where `created_by` = 15 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 517}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 1318}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.223271, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "PosController.php:517", "source": "app/Http/Controllers/PosController.php:517", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=517", "ajax": false, "filename": "PosController.php", "line": "517"}, "connection": "ty", "start_percent": 87.551, "width_percent": 4.141}, {"sql": "select * from `product_services` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 1393}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.266613, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "PosController.php:1393", "source": "app/Http/Controllers/PosController.php:1393", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1393", "ajax": false, "filename": "PosController.php", "line": "1393"}, "connection": "ty", "start_percent": 91.692, "width_percent": 4.419}, {"sql": "select * from `pos` where `created_by` = 15 order by `id` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 1402}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.27385, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "PosController.php:1402", "source": "app/Http/Controllers/PosController.php:1402", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1402", "ajax": false, "filename": "PosController.php", "line": "1402"}, "connection": "ty", "start_percent": 96.111, "width_percent": 1.843}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "pos.printview", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/pos/printview.blade.php", "line": 5}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.3107772, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 97.955, "width_percent": 2.045}]}, "models": {"data": {"App\\Models\\Pos": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 7, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1356691580 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1356691580\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.22055, "xdebug_link": null}]}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:1 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 15\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/printview/pos", "status_code": "<pre class=sf-dump id=sf-dump-2057436678 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2057436678\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>vc_name</span>\" => \"<span class=sf-dump-str>7</span>\"\n  \"<span class=sf-dump-key>user_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">17</span>\"\n  \"<span class=sf-dump-key>warehouse_name</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>quotation_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-860957161 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-860957161\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-955374457 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1834 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749341283326%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjUrTmVEbXpMK0RGQllDL3JKeXNKeVE9PSIsInZhbHVlIjoiWnpPY2REL3owK0VLeTg4UUkzd3Q3MWRwdjg4R2NPd1daYnRDSUVjMDU5M1RjR1VUZHlHaVdLNG5WWjV2YzQvYTc1QTFpVW1sUWd0UmVaRjlqOEZMVzkyRmJVSlU5UGJJS1EwWFA0cHBOVWhMdjVBZTRmeUtQV25CN1V4T2hyNmZaamRXWmhTSzZ2VVBsRkp0Yzk0Mlc1dmhyQU1DS0loQXFvTnN6eE96K09RQmhmTlI1MktjVjNpUXdPTWkrc0l0VFl3SHJYZ0FYYjlSTStTbm1ZV0tGaUF4VmRGVlZzbnlIbjlUTktYZEV2U0xRTCtTQ3lUYW9DK1ZyQnR2Y1E2dUdIZStJOFBuWDBNT0granNEa2tNcE1wWGJtcjhUZmFtbmpKMS9lK2pOZnNNZFBKL0FKQlF3SkpqRDRmdU1QdDMvd0dJYWpwYTdrcmdDSDhoT3p5YVpVaDBtMnE0cFB1b3lrLzh1L0tjb1Z1UU5NMjBaYlovd2I4VG5RUWQzdkVuSFg1dGlvTDBpN3RoNHNmK1NJRmNsN0N5T2l0OEk3clVnUGNqRzVpaTFNYkVINkFHR3lLbGg3enB4RGt0ZFpsdlFicWxoZWk4djdUL2VFYktXSHlxLzFzeEt4S2ZlR0xvdnhtdEo0L3VGdjl0Q2lLa0kySEp0Z3p5WW9RdWN6WlkiLCJtYWMiOiI1ZDlmOWUzZWVlZjNjNmZiOWZkMGRlNjk0OWUwMjgwNTNhZGM3Y2UwMjc1ZDM4OWE0MTg3OTc0YTc0OTc4YWRlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImRTbFRyV3hJOHQ1N1piSkoyTElzaEE9PSIsInZhbHVlIjoiYzVhdkRNZUEzTkdZWlJ2QzJrWHhUSmp1WHM1dHdUSDZNZ210NkxheUkwZTZWYXQxaWtEMTFwSEpKYm8xOGh2V2NBN2VueDY0NnVIbXZTMXdLVVVVRVIzekpZWExrQ0dnRzJIdTJObHJVTUdrODhBYjFsbFdHcmgxTjVuY2dDWnVURWFCQ1p1c2FjU1c3V2Noazd3KzN5TnV3anBzdW94QjNTWkQ0Yi9KVTRYOWdwOURVL1lzaFJkQ1hvSDRDWVBCSnRHV2drY0pFS21NRm9DSURqRnJJOFhEdlUvOWdoRUFqRktNUjBrYWhjNWtHSkY0TUpIakJVQnQwZFlWMTNia094ZnovdXB5ZU9JNCtzTE1XWHZSRWEvSTZnaXYycmN5d3Rwbm1pZnIxUW05SGVEbmRtZng3Nk9leG9wSlQrNU9SV25MenV5ZDlTUDNjNU1kZjljSUZ1SUNNOUJaOXhMcDVkY2E4Z0FTTXdvNHIzeFVkbjh3VWwvc0RNS29hdkdGVElxd2txU2R6TkFUc3J5cnBhR2F1YmRoMzBKOUhYYWR4SW4rUnh6YjJJR2hkS1EySE9Wa2lIb2M4Ty9ackcybTRaTzIzTHRHLzZqVkdKcTBLbnl0V09IK3NGMlcwUXBEN3FtYnpHN0Z1NWVaU3d5eHNUazhlazJpeXF4TGpVQ08iLCJtYWMiOiI2NmVhZGQ3MzM4N2E4ZWNiOGEwNjYwMjhjOWQ2MzhmMmUxYjEzOWM1MzM4ODM1MjhmNWViZTQ2NjMwMzU0NGNiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-955374457\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1103809505 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1103809505\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1408288328 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:28:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlFnMUFWblp5ZHN1WUFzY2M3N0ZJUEE9PSIsInZhbHVlIjoiUjRUMzJJd3BkUmxLbytOZ2Q2SHVtUjkwSGNKUFhTWG00ZENqc0dXUGsyVmlSYjhBKzEvbVNOSTlPcmZOL2NEQTM5NEpZVGhDVmluK0JjRHFKbjFXcy9uY2ZQcm11MDU5NkpaYVlLUXN2Nm90eXMrQ2F4aDQ3Y3d0U2k3U0I0dUJnOEJSUW9HOUVLeFMxQTZPbFQzMWUxWHQ5UmZKV1A2b0JlQ0J3M1l2Q0NQelZDZmxhVm1RU1hvMWNLVzlPZGRGS3ovSTlRUWJPMzQzTjVQWEpIZi9xUGptT0Y4Y0pWcWkyV1NwQWFyYURrNWlZWE1ISlJBcHNoZ1FaQjE5YldYengyNU9jcHJiNERKUnJMMXZJdCt0SEtoWUJhS3FtZmVaOFVCRVhGTUltRWVSbUIxNjJkUC9MWE1BdkIxMFB3a2kyb2FsVG1UR09SWlRXVTRjVFRGL3FVNC9YaDZUVGk3d2VWQU9tbXQ3aUtwSFA0L3pGN0ZKWlE2VGNQWmh0akV1b1Z3SVM1eWk1OEhMS2ZyN1FLR2F1cnRaOGl2eXNQNkUwNkQ5T2FlcG1KZUUxQzlwK1dlcnNqMWhmWEM2OHJnSFVmckVaOVdDdGZ2YUkxdTQwdmFYeVlRdnp2WkhOTWp6U0FlQTJnRFpUazF3Ulk3Wm85UDI5VEdqbkRjVDF4MXciLCJtYWMiOiI2ZDNlOTA2NDk1ZjkzMjUyZjA1ZjVkYTM0NDBlMGQwNGFmNzVkYTVlYmQ1MWM0MGNlMjkyMjNkOWRlYzJiZTM5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:28:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkRORTJIbkF0cjdTRWM0dzk5aVRmUEE9PSIsInZhbHVlIjoiaDNzTlo1dTlCVGFpVzgxcVI0VmhlcHdzbWJDYjFza3ByTmFKTFhldHZLcklxQkhxUkFwbVFja0t5WlZYb2h2cWpLT1RSV3ZjTnpxdmJKK210cUtiV0w0YjB5UGJjSnpWUzBweFFmMzVvckNiaXYvd0RPSnQ1QXQ2MnZPcU1YOXI4RVpqS3pIcG8yL1d6Umk0ZmY1NG1ybnlTbFBvVzJBVElVNTZrVWtPNDZvV2toK09XUVU1bDhOTDhtNHptbktwWVNwK1NjcXlmVzlKd1AyMmt1dkh4WlhjMVlXcW1iWXBhbGptZmJVTFdNTERqVnh4MVhUNjVtK09QcS9NVE45YU0xTXNiemF6YzROWVREbDJiSXhrMmZsbVhjM0RDQjBQY3Z2RjBvdUlicktmR3lRS055ZGtLSFhhSE5pQjRqZHdFcFJicFdqSXFWZzgwZEl5MGpRV0RpNzQxdHdUM2VaeGpFaEJBU1l3d25RbE5ZM1dRdFJ4b1l1OFlSeTRyd1ZkVThFOXpreWUybzNxRFoydU9BVGtpK0FjQURhY24xSWNCeThWdHRaekxDc2ZNRm1lTTh3QldxS2crK2NXWjVaUlhmMHdybXV2d3pQdWJFalhOakNXVk9iVzZRYXVBU2pYUkFROG1EUDBCT3FBNGxsUXZVNnd4c21hSmdUamYrcXEiLCJtYWMiOiI5ZjdmY2ZkMjRlZDQxZDE1NTQzODY5NTc2YjEzOThkOTQxMjQ2NGQ5OWFhNWIzZTViYTUyNTU3NjkyYWEzYTVjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:28:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlFnMUFWblp5ZHN1WUFzY2M3N0ZJUEE9PSIsInZhbHVlIjoiUjRUMzJJd3BkUmxLbytOZ2Q2SHVtUjkwSGNKUFhTWG00ZENqc0dXUGsyVmlSYjhBKzEvbVNOSTlPcmZOL2NEQTM5NEpZVGhDVmluK0JjRHFKbjFXcy9uY2ZQcm11MDU5NkpaYVlLUXN2Nm90eXMrQ2F4aDQ3Y3d0U2k3U0I0dUJnOEJSUW9HOUVLeFMxQTZPbFQzMWUxWHQ5UmZKV1A2b0JlQ0J3M1l2Q0NQelZDZmxhVm1RU1hvMWNLVzlPZGRGS3ovSTlRUWJPMzQzTjVQWEpIZi9xUGptT0Y4Y0pWcWkyV1NwQWFyYURrNWlZWE1ISlJBcHNoZ1FaQjE5YldYengyNU9jcHJiNERKUnJMMXZJdCt0SEtoWUJhS3FtZmVaOFVCRVhGTUltRWVSbUIxNjJkUC9MWE1BdkIxMFB3a2kyb2FsVG1UR09SWlRXVTRjVFRGL3FVNC9YaDZUVGk3d2VWQU9tbXQ3aUtwSFA0L3pGN0ZKWlE2VGNQWmh0akV1b1Z3SVM1eWk1OEhMS2ZyN1FLR2F1cnRaOGl2eXNQNkUwNkQ5T2FlcG1KZUUxQzlwK1dlcnNqMWhmWEM2OHJnSFVmckVaOVdDdGZ2YUkxdTQwdmFYeVlRdnp2WkhOTWp6U0FlQTJnRFpUazF3Ulk3Wm85UDI5VEdqbkRjVDF4MXciLCJtYWMiOiI2ZDNlOTA2NDk1ZjkzMjUyZjA1ZjVkYTM0NDBlMGQwNGFmNzVkYTVlYmQ1MWM0MGNlMjkyMjNkOWRlYzJiZTM5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:28:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkRORTJIbkF0cjdTRWM0dzk5aVRmUEE9PSIsInZhbHVlIjoiaDNzTlo1dTlCVGFpVzgxcVI0VmhlcHdzbWJDYjFza3ByTmFKTFhldHZLcklxQkhxUkFwbVFja0t5WlZYb2h2cWpLT1RSV3ZjTnpxdmJKK210cUtiV0w0YjB5UGJjSnpWUzBweFFmMzVvckNiaXYvd0RPSnQ1QXQ2MnZPcU1YOXI4RVpqS3pIcG8yL1d6Umk0ZmY1NG1ybnlTbFBvVzJBVElVNTZrVWtPNDZvV2toK09XUVU1bDhOTDhtNHptbktwWVNwK1NjcXlmVzlKd1AyMmt1dkh4WlhjMVlXcW1iWXBhbGptZmJVTFdNTERqVnh4MVhUNjVtK09QcS9NVE45YU0xTXNiemF6YzROWVREbDJiSXhrMmZsbVhjM0RDQjBQY3Z2RjBvdUlicktmR3lRS055ZGtLSFhhSE5pQjRqZHdFcFJicFdqSXFWZzgwZEl5MGpRV0RpNzQxdHdUM2VaeGpFaEJBU1l3d25RbE5ZM1dRdFJ4b1l1OFlSeTRyd1ZkVThFOXpreWUybzNxRFoydU9BVGtpK0FjQURhY24xSWNCeThWdHRaekxDc2ZNRm1lTTh3QldxS2crK2NXWjVaUlhmMHdybXV2d3pQdWJFalhOakNXVk9iVzZRYXVBU2pYUkFROG1EUDBCT3FBNGxsUXZVNnd4c21hSmdUamYrcXEiLCJtYWMiOiI5ZjdmY2ZkMjRlZDQxZDE1NTQzODY5NTc2YjEzOThkOTQxMjQ2NGQ5OWFhNWIzZTViYTUyNTU3NjkyYWEzYTVjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:28:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1408288328\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1632034961 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>15</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1632034961\", {\"maxDepth\":0})</script>\n"}}