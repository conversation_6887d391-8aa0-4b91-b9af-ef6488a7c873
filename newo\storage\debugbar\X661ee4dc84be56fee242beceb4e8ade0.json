{"__meta": {"id": "X661ee4dc84be56fee242beceb4e8ade0", "datetime": "2025-06-08 00:30:07", "utime": **********.022039, "method": "GET", "uri": "/search-products?search=&cat_id=0&war_id=8&session_key=pos&type=sku", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749342605.772045, "end": **********.022089, "duration": 1.2500441074371338, "duration_str": "1.25s", "measures": [{"label": "Booting", "start": 1749342605.772045, "relative_start": 0, "end": **********.742989, "relative_end": **********.742989, "duration": 0.9709441661834717, "duration_str": "971ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.743012, "relative_start": 0.9709670543670654, "end": **********.022094, "relative_end": 5.0067901611328125e-06, "duration": 0.2790820598602295, "duration_str": "279ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53117384, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "search.products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1224\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1224-1320</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.01622, "accumulated_duration_str": "16.22ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.844588, "duration": 0.00555, "duration_str": "5.55ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 34.217}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.875043, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 34.217, "width_percent": 7.46}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.924298, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 41.677, "width_percent": 10.604}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.932951, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 52.281, "width_percent": 7.522}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8'", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1236}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9469628, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1236", "source": "app/Http/Controllers/ProductServiceController.php:1236", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1236", "ajax": false, "filename": "ProductServiceController.php", "line": "1236"}, "connection": "ty", "start_percent": 59.803, "width_percent": 11.344}, {"sql": "select `product_services`.*, `c`.`name` as `categoryname` from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15 and `product_services`.`id` in (3, 5) order by `product_services`.`id` desc", "type": "query", "params": [], "bindings": ["product", "15", "3", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1252}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9581182, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1252", "source": "app/Http/Controllers/ProductServiceController.php:1252", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1252", "ajax": false, "filename": "ProductServiceController.php", "line": "1252"}, "connection": "ty", "start_percent": 71.147, "width_percent": 9.248}, {"sql": "select * from `product_service_units` where `product_service_units`.`id` in (5)", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1252}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.971001, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1252", "source": "app/Http/Controllers/ProductServiceController.php:1252", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1252", "ajax": false, "filename": "ProductServiceController.php", "line": "1252"}, "connection": "ty", "start_percent": 80.395, "width_percent": 8.693}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 5 limit 1", "type": "query", "params": [], "bindings": ["8", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductService.php", "line": 267}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.977943, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "ProductService.php:267", "source": "app/Models/ProductService.php:267", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=267", "ajax": false, "filename": "ProductService.php", "line": "267"}, "connection": "ty", "start_percent": 89.088, "width_percent": 4.747}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = 3 limit 1", "type": "query", "params": [], "bindings": ["8", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductService.php", "line": 267}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.0039551, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:267", "source": "app/Models/ProductService.php:267", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=267", "ajax": false, "filename": "ProductService.php", "line": "267"}, "connection": "ty", "start_percent": 93.835, "width_percent": 6.165}]}, "models": {"data": {"App\\Models\\WarehouseProduct": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceUnit": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceUnit.php&line=1", "ajax": false, "filename": "ProductServiceUnit.php", "line": "?"}}}, "count": 9, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1441583116 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1441583116\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.944908, "xdebug_link": null}]}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/search-products", "status_code": "<pre class=sf-dump id=sf-dump-1104270531 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1104270531\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-492372636 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cat_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>war_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-492372636\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-218479334 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-218479334\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1155517916 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1834 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749342593005%7C4%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InZBcTVZaCtQaTEvempJdDgxWHJZZmc9PSIsInZhbHVlIjoiandWSzFsRUVMZGEyWU93MUU5L1NBclc0aEx2eGt4MS82R0pDNWxBNXR3cE9NeDMvNGFZc21PUzI5SWFMRjFSb1VkdEVHeTZUSldVWHVBVHFoUzB0OVBnUVRxb3B4cW9jOVcwOU4va3UyVEZUc29nSmxNRmRYeFM0RGhWVitzVnpEQ1ZjSWk1Qms0NWJPVHlnQlV4d3A5Q09YNTl0UlhQWEloU3lqZVp3UzhDMkJSbmtmaDNIUllzeTFzcHl3L2pWVGFvOU5hYzNiTXZtYzZQMXBzUy9TQlA2YlExOEEzTjdqekhCUnRKaXpaUUJMMzRyaEhoZ0p1QWp1aWcvMkY0dTA1STV1SEhvd2p6Y3RMVk83L2JMcnZCUlhJc3QvZ1JVeDZKMnQ2Sm8xMWNIck1WZlNNc1V1eklvT0V0djFCcm9sVjdaOERqZGlHY3VSMCtZcWVkWUZiZk5CVW5FYVNWUEZVSVhSUTloTWViTDlaaWNkZkhBakpiNDRpUnBWeGx5RjJpVUtoc0hqU2JkTDQ4RGxLRXAyY2xZR25oZVZxR2FwUDdrTlB1TjZyWG9JcEdPWTY1cXg2NmVJT01EaVZkSytmZE1ZTlhLQVZaM0o5cFkrZW1NMEFNcUpwOHJGaERBdXhNcDBXSklWQUZtN0h5SU0xVy9ZTmVaandPUnVBVVEiLCJtYWMiOiJkMTQ2NTExNjEyY2M4NzU0YWUxNzliN2MwNTVlMDZkMzZkMDVmNTY5YzA3NjQ3YjBmMTc1NmJlNmVlOGVlNDgwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlR6azFockM5UVpQdzJ6LzdpS1RwVGc9PSIsInZhbHVlIjoiZEZwa0ptNlpDL2Rpd2NuMzFhSWtmQ3ZQV1lCR28wZlVYMXZsTjFZOWRSbi9Iek5TcmM2dDhQS0tyaXc5VkJONWR0K1h3MWw5QUVSVExlZ1RnOUlnL2FETE44WmMrTXRGanlmd3M4SkgwbmR6NkR3THdMYTI2alE2M2tTeUhrS2hCa28wczlHcm1xdTFYQVlPODhVWnZHTmFwdjdTaE5vZDJZL1hxcDZGc09ubWtZOEsweWNmQjg1dzRHd0VHbi85SmJwL2hEcytOeHNxaUJZU1VFSnpEKzRvSDd1MFB1cktvNXE2R29vZXRld3dveExObjVLTUNRN1FucXdtYVl1TE1yQUZoalhqRWxFOVBsSkJaNXczdnFsSkN5RkxZbFB5MHFuZjNuakNKRVFnM3U2MEQ4U1NRS090LzlyekVSbWt1NEdsa2lnTkYxWEFweFlJUGZUQTlNVFpRcktlSWpzemhpZzNQcHFITngwa3lmdmpNZWVKYWFETzd5UVp2aEhYRFFMVHVmZlZ3TkRoSHlkZkdQbUYzVFFxZ014alZuQ1d1ZUkyVElLSWJQYzhLV0sxWmdpSWhBY1RNU0UzRkd3WDdpSFpGb2RLRVYvQ1kvVGg3VnozMy9yWU1ERDZsSlNKa2FEQk5LK0NLSnllTFJOaXgyOUpvNHpHWThQaVJLUWgiLCJtYWMiOiIzNjQxM2MwYTc5NDUwNDFlOGMxMTdkM2U4Y2FlZjgwMmI4MGEzYTdmOGRhY2QyYTFmYTVlOGVhNmU3NGYxMjQwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1155517916\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-615468951 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-615468951\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-299501904 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:30:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlFJTldCTkkwMUhUSzk3UGxRNlZHV1E9PSIsInZhbHVlIjoiN2paeTdBdnlmd3ZjTDZvRjRIUXFNM0hFaDZCSENQYlRGeUN2VWxaQUZjaERlTVdNYkpoQ2F4Wk9OR29abnV0NEVaNFVGRXFyTDRTaXVPeTRWVFBzdUJ2Z1M5VDZrb2txOTNWSCtrOUswWHF6OXd6OWt3WldxWkN4bndUTE5DdDJIZ0IzeE8vb2U4WFBpbGpWRTEydFRKS1Q2dVdvLy9GY25sSXpFVFdiUkwramtpYXZ0dkN0NGZuTVFEZGZ5MWNSU0RRbXlyOUZNM1BvdlpUUGxxVHl0VUZCWXNEZEtoTUVRS1h6b3c3OUphenJ0WXNKdWpyQjA5MmVQVlR2K0x3cEdmZ1pDbm8rZW8yK1BnNzdWaFJwSWhaeFEwMDJOL05CZzZtYTFqVnNoWWRsVFNHVlpUcmpmd1lKb3YzMG5WMU9aQy9PbmJrczY5ZDJUQ2F0NFB3aTdyMTFvM1BTSGJVU3M5b3hReUpKQnVlVVFLSVlvL093bU1yaENNbjVOT0kvcGhlZkcrOHNxU0Evakh5RjNsanhlTDU5NzRIYkx0czBrdDhkaGdoK3VuaTh6emkyQUxEQm11REpxdk40c2tJaXlQaGlEdSswNVZ2TmJRMUs2aSthYzA2S0Z6VnhmTU5WN20wdm52Vm1pR3RWTUptcmcxTHpFNUpuL09MaDc4VzEiLCJtYWMiOiI5NWUzMTI1ZmUwNDkxMDdhYjkyMTAxZWE2NjM2ZWQwODExMWM0MWM2MTk1OGE4NjQxZDdhYzA0M2E2YzIyYTcxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:30:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjdWUm5YZlhjNUZuZ3d5RWZPRGJZTWc9PSIsInZhbHVlIjoiTEhGbHlMeis3L0ZuVzBWRjFUVnpWVURTYTNyeEZOTC9POGtxNmVZVitlbTlDODJ3aUNrK1p2SE8rT1JudmhXVUJRdTZWeUpLVGJyRE1GNDZydVRuTXE5Tm5mMHgwWnY3UkdyRnp5MzlSUDFFRGlEanNpa3RpdHpDckhRRzdWdkJtR1hiMmxWTW5ONDNsVTAzYjM1MEUyRzNabHN4NWl4NGpDODJKWTRVbWs5Q01tZ3BIdEtIMTl3b2g2Nm9RMUNVbFhyQVpydlhwU0VzdWJOelJXZVNBK1FyRFp3cGpUNXpvV0dLSnpmc2ZYa0ZWTWRpTGsvcEdQQkNYT2s3RVNaQVRJY0VFMm1RUG5Wd2RLLzN1ZFFOOWR4R25KWkNLb1AraVF1L3lzYzlWUVhXMnRsUWk4cVI3RnZXUEtLQWwwTXhCOUk0Y0dpZEl3N1VGdWpYM2YyMzhBVWYyU24rbWErZFhWYWdoUTBTZUM1VGNQOFBnMXl2Z0pEUk5WU1pDa0x6cWFSQUFkVi9qdmE2M2tJZWxPQVF2L0toU2pmMENCSDdxbElUeDNLelJtNGYwcVljRDhYYUllZTBpdWxMSldHbGxXdWRrUmY1ZXNmSlY2K1ovczVYOFVjWW5vM0tEZ3laV2F3c1dpTXNWZ0wvd1RTNWNPTDJKcWdLMHFrNlhyS2oiLCJtYWMiOiI0MWFiYmY4MWQ5NzM3N2U4OGE3NjU4YzQwNmQ4MzllYzE1OTQ0YWFkYWY4NGExYzA1ZmNlMTAwOGQzYzcyZWZlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:30:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlFJTldCTkkwMUhUSzk3UGxRNlZHV1E9PSIsInZhbHVlIjoiN2paeTdBdnlmd3ZjTDZvRjRIUXFNM0hFaDZCSENQYlRGeUN2VWxaQUZjaERlTVdNYkpoQ2F4Wk9OR29abnV0NEVaNFVGRXFyTDRTaXVPeTRWVFBzdUJ2Z1M5VDZrb2txOTNWSCtrOUswWHF6OXd6OWt3WldxWkN4bndUTE5DdDJIZ0IzeE8vb2U4WFBpbGpWRTEydFRKS1Q2dVdvLy9GY25sSXpFVFdiUkwramtpYXZ0dkN0NGZuTVFEZGZ5MWNSU0RRbXlyOUZNM1BvdlpUUGxxVHl0VUZCWXNEZEtoTUVRS1h6b3c3OUphenJ0WXNKdWpyQjA5MmVQVlR2K0x3cEdmZ1pDbm8rZW8yK1BnNzdWaFJwSWhaeFEwMDJOL05CZzZtYTFqVnNoWWRsVFNHVlpUcmpmd1lKb3YzMG5WMU9aQy9PbmJrczY5ZDJUQ2F0NFB3aTdyMTFvM1BTSGJVU3M5b3hReUpKQnVlVVFLSVlvL093bU1yaENNbjVOT0kvcGhlZkcrOHNxU0Evakh5RjNsanhlTDU5NzRIYkx0czBrdDhkaGdoK3VuaTh6emkyQUxEQm11REpxdk40c2tJaXlQaGlEdSswNVZ2TmJRMUs2aSthYzA2S0Z6VnhmTU5WN20wdm52Vm1pR3RWTUptcmcxTHpFNUpuL09MaDc4VzEiLCJtYWMiOiI5NWUzMTI1ZmUwNDkxMDdhYjkyMTAxZWE2NjM2ZWQwODExMWM0MWM2MTk1OGE4NjQxZDdhYzA0M2E2YzIyYTcxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:30:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjdWUm5YZlhjNUZuZ3d5RWZPRGJZTWc9PSIsInZhbHVlIjoiTEhGbHlMeis3L0ZuVzBWRjFUVnpWVURTYTNyeEZOTC9POGtxNmVZVitlbTlDODJ3aUNrK1p2SE8rT1JudmhXVUJRdTZWeUpLVGJyRE1GNDZydVRuTXE5Tm5mMHgwWnY3UkdyRnp5MzlSUDFFRGlEanNpa3RpdHpDckhRRzdWdkJtR1hiMmxWTW5ONDNsVTAzYjM1MEUyRzNabHN4NWl4NGpDODJKWTRVbWs5Q01tZ3BIdEtIMTl3b2g2Nm9RMUNVbFhyQVpydlhwU0VzdWJOelJXZVNBK1FyRFp3cGpUNXpvV0dLSnpmc2ZYa0ZWTWRpTGsvcEdQQkNYT2s3RVNaQVRJY0VFMm1RUG5Wd2RLLzN1ZFFOOWR4R25KWkNLb1AraVF1L3lzYzlWUVhXMnRsUWk4cVI3RnZXUEtLQWwwTXhCOUk0Y0dpZEl3N1VGdWpYM2YyMzhBVWYyU24rbWErZFhWYWdoUTBTZUM1VGNQOFBnMXl2Z0pEUk5WU1pDa0x6cWFSQUFkVi9qdmE2M2tJZWxPQVF2L0toU2pmMENCSDdxbElUeDNLelJtNGYwcVljRDhYYUllZTBpdWxMSldHbGxXdWRrUmY1ZXNmSlY2K1ovczVYOFVjWW5vM0tEZ3laV2F3c1dpTXNWZ0wvd1RTNWNPTDJKcWdLMHFrNlhyS2oiLCJtYWMiOiI0MWFiYmY4MWQ5NzM3N2U4OGE3NjU4YzQwNmQ4MzllYzE1OTQ0YWFkYWY4NGExYzA1ZmNlMTAwOGQzYzcyZWZlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:30:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-299501904\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-816477927 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-816477927\", {\"maxDepth\":0})</script>\n"}}