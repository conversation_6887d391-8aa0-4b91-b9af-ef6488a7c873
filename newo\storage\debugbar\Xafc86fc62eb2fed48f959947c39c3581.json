{"__meta": {"id": "Xafc86fc62eb2fed48f959947c39c3581", "datetime": "2025-06-08 01:18:56", "utime": **********.9299, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.144835, "end": **********.929926, "duration": 0.7850909233093262, "duration_str": "785ms", "measures": [{"label": "Booting", "start": **********.144835, "relative_start": 0, "end": **********.818099, "relative_end": **********.818099, "duration": 0.6732640266418457, "duration_str": "673ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.818131, "relative_start": 0.6732959747314453, "end": **********.929929, "relative_end": 3.0994415283203125e-06, "duration": 0.11179804801940918, "duration_str": "112ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45055800, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.018529999999999998, "accumulated_duration_str": "18.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.870167, "duration": 0.0171, "duration_str": "17.1ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.283}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.904555, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 92.283, "width_percent": 3.886}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.915757, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.168, "width_percent": 3.832}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-549338466 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-549338466\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-707834748 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-707834748\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-302760730 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-302760730\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749345519645%7C57%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlZUWG5JRmd3Q1ljYmo3T2twdHJzc1E9PSIsInZhbHVlIjoiMWc2MnJZdVJMUFJEZ3hUNm5IbldHNld3Q2pSQW9pTk4yOXRLcUtXZ1JNcVluYStjOUxIV1EzdWRuRkF2V045bmR2cEpOdzFwZWcxUVIwaFRpaXMwc0lzSS9KTHVJYkUvTVpobVM4VjVYSlF3M0xJVm1MSEVFQXRwcWN0WEYwN3ZpWHBnOE5xc2lybXBzY1d0Q1dGeVRyQ1JBa3RwZHFTUEQxZVVpUUYzcDdTMHdnbDY5WndPZTdLS25SaGVKcWxjNDZ6MDRtS1o2dkQ3NE82KzhJdUlud1VNV1VBUGtWRUtZRnora0M2Wk5qOUcxdGJWdTY5MlFWTDJMUko3Z25HbkN3TkF0bnFiYThDSUpaVTQvTk5wL2FraSttYjhhSlczeFlFNTZ3QldDQlV1K3U4Z1hrdkNxZjI4eC9FdUtvL0p3V0FnajdPVFR0Znh6MVhYTlFqKysvd3k3dCtuZ0RaRDNkSWJRRTI4c1ZpMUxUZXAzaExrTCtDajJjQTBaMVhqa2JiN1BpVVkySXZzeXlzWkFuY2RLa1U4K0ZVaGZBMjJ1eG5KYklTYnRRaGIvQmlwRE4xZ2ZQcmVEaFBQdEN6bnFuWW40bnhManpPVDY3VTIrNUNOUTBUYmVKalY0ZlFWazdyWHNJdnZrMTdmSHUxUE1rVEc5OXJZZzVDUnhXdUciLCJtYWMiOiI1YzdhNTljYmM1MjExYzA5OTM3ZTJmYzljNGViODAyMzU1NDBjODk2Yzg1YTMwMDk0MDFjNjlmOTg0Y2ZmZTBmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ilh0WDNRbzV5WlgySXkvZWJibVZMZVE9PSIsInZhbHVlIjoiaUtSTG5ib1Mrc0w4NkxTUXdDTTB4NDBhUmRHSlovem9SMm5TeU5aYzJxdGdGaGxwcElrempXSGxBYThCNGxYMkx1TjhmNElOT2hKZDlmYmVpdUg5NUJNa1hjcmp5YXBiN3Z6SVJZL3l1bXowVUJPMWpzMWlNU1RwaVpJdUNTbnB6cjY4TVlYbGZ2Tno1bWtleTUramdYMkJlOXNYV1VsNU56R1lHYVpqdFo1V0JyOHZweHNtNFdTYlVReDFkalFLTkdoMW0vRkhHRS83bWoyTUZQUWxKTTVjMzRRWkF1Qzk1VGJ3T2s1dXRIUmRwdnZaL3B4bXc3WTdWS0dnOWEwNnNTbHpETXhveHhrZFpSV0F6c0M4b3M1VkdwZTAxeHZUWk4yME9pOHpyQUd1eHdzSzZtWHM4VHoxbnJ6a2tCeTRsMkNpbUJ4YUZFeWRDVWRaeVdIYVVtTHJ1MUtKcC9JV1R2MXlYc2pqZ0VGTTZQMUptSVZQL0pnTzVaN091dlV6SXZkS09BZ3kzY0JEQ3p2UEdMc3IyUCt6L0dUYkNWczBCa1hCMUluM1FOU3cybkhrSGNURDhsMis5V1BsdGRjZll3QmNkSUtIWVlRUmt1V1hkeC9ZSzlzR0pCLzFFUEhZcm5qaU5JM2s0bjdUOTRYWXVQeEZvZW9BVHNOcWttemMiLCJtYWMiOiI0ZmYxYmYxZjM3Y2RhNGI4OTExZmZiYTc2NWE2MDY5ZDk5MmQ4ZDc2NTI0NzNjNzgzM2JiY2FlOTg3NWM0YzE5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1631596825 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1631596825\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1968421403 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 01:18:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjRBQWgyeUR2bFExYkhrZEhnSW1aNWc9PSIsInZhbHVlIjoiOWlpVk1PS0t2WHZkSThQMmdvWjc3dVFFa0JLWGNFU1B4QWJiUzVYelo3ekphU0xOWkl5cTFvZXJZUC9zMGdkamtRdDQvaHRXTGcrREFWK0pLMldwV3lMTmRSSFllVjc2RXVGYmR0MkhXcU5SRkhWYnFiRDdnZGJaNmhCK2hVdmF6OFFmMmlYMVB2RitUbFlvV1UwUkhEZkZZMDVxY0x5THFTTklONmZZekdvaGNzNEtoUnFyOTFveGhkMWtyM1RabjdYeXR3b0lXZFRZdmlNaWZnYWtWSXFoZEVObFFwVWhWVjZBNkZ2aGlZbGt5ZWRTN3NqLzQrWEwzMHlncFptb09tUDVQRHVseWQ3ZjU1ZnVvVFZ3SGVCbmkyMmE4bWdlYzFaQTNYZlF1UVh5WDhsYysrbmdJOEZEQm1JOTFPYVBjVHNLdTU0YzhEaXk3SDFkSVI3K09NNW1ia2JxaE93V2FwWFNuUm1GWDl4Tk9aUVpabzJaMEpoWjZDRzdMaTlpa2hGK2JIR0dwNnQ4ZlVTQWZiVFZjZ3VDc0VOcmRCYWRSMkFFanpLZ1R4RWxPOEhOQXpyRlJ6Zm1XUWxia0xZSGExcGF5bWFhaHJjbitvd3hQMlZpYjRzVFhCMW5wNDVLWnoxQmFHendGME5vaEVmcVJVV2tHQVU2eHVSQ0NJU2UiLCJtYWMiOiIzNjRhODgxMmJhODQ4NTMyMTIwM2EyZWIyZTVmMTQ4YWVhMDY5MDE2ZTc3NjVkMzY2MDQyZjU5M2E4N2YyNmYwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:18:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik1SenVzS242VzUzYjBnTXRETmNFalE9PSIsInZhbHVlIjoicWdJQWxjSXZMMnkyeVRIN2JYUytHYkRUOVlZRldEVUo4Z0RWTkdKVzBWcVJ0cjFGeHUzOUVWWnNCK3dvZ0V3Ni9jNTFBMFZBK0NBOG9tVDk5djR2alMzQ2g3VmJXd21mVXd4RjdGZE84bFhRSUdlNWhGRnBBdkZ2ZmhkZkpmakhSZGpuc3p6c2ZGbTNzdXdVd3gvZ2R3bWF0MFZ6UkVKR0Ftd3d5eGRBbmdrci81cVYyNTFLUWl4YjFabzRuRkRNdFMzNllxWG91TGF1L0JVZEVkdjVKZnY2L0o5dXkvWUFzVUo1Y3dOZmZQMjNCZ2d2TFg5ZitWRTlPMzJ1TnROQVBxZ01HSE81NFFqcU9IV05YZ3dseWh6N1VZNUo2QVE4bFNoQlFFZkRESkFmOTg4V3FSdWYvMGZxVjZtTU90b1c1aEpKdXhmTnVZNTdUWURTd2RLU2N5Z1I3TGJwL1pyWFVDRkxaTFZiSG4rV3BHcmRkSWZvM2NpZVBCbTlCTXNEdm9XTUg5RmlFdm5HeTBLNlhZRHBGVDEwSjErMG9IZkRsQ3dUN2ZBQnhPZ1lOZ3d0M0Z5b2F0bUd6dmRQb3YzbzREbGp3WUNDcWJ2bkVuR2RZdDFKanJUMjMxenRCdktaTnd4NXBsSkVrMFhQYjc1R29mQkNwNDU3R2J0NUk1K1EiLCJtYWMiOiI1YWY3OGYyMTllNDVlZmU3ZWFhYjlmODkxZWE3MzJhNmM0MjBhYmI5ZWYxZDc1NGVmNjViZjMyMDNlZDhlNWI4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:18:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjRBQWgyeUR2bFExYkhrZEhnSW1aNWc9PSIsInZhbHVlIjoiOWlpVk1PS0t2WHZkSThQMmdvWjc3dVFFa0JLWGNFU1B4QWJiUzVYelo3ekphU0xOWkl5cTFvZXJZUC9zMGdkamtRdDQvaHRXTGcrREFWK0pLMldwV3lMTmRSSFllVjc2RXVGYmR0MkhXcU5SRkhWYnFiRDdnZGJaNmhCK2hVdmF6OFFmMmlYMVB2RitUbFlvV1UwUkhEZkZZMDVxY0x5THFTTklONmZZekdvaGNzNEtoUnFyOTFveGhkMWtyM1RabjdYeXR3b0lXZFRZdmlNaWZnYWtWSXFoZEVObFFwVWhWVjZBNkZ2aGlZbGt5ZWRTN3NqLzQrWEwzMHlncFptb09tUDVQRHVseWQ3ZjU1ZnVvVFZ3SGVCbmkyMmE4bWdlYzFaQTNYZlF1UVh5WDhsYysrbmdJOEZEQm1JOTFPYVBjVHNLdTU0YzhEaXk3SDFkSVI3K09NNW1ia2JxaE93V2FwWFNuUm1GWDl4Tk9aUVpabzJaMEpoWjZDRzdMaTlpa2hGK2JIR0dwNnQ4ZlVTQWZiVFZjZ3VDc0VOcmRCYWRSMkFFanpLZ1R4RWxPOEhOQXpyRlJ6Zm1XUWxia0xZSGExcGF5bWFhaHJjbitvd3hQMlZpYjRzVFhCMW5wNDVLWnoxQmFHendGME5vaEVmcVJVV2tHQVU2eHVSQ0NJU2UiLCJtYWMiOiIzNjRhODgxMmJhODQ4NTMyMTIwM2EyZWIyZTVmMTQ4YWVhMDY5MDE2ZTc3NjVkMzY2MDQyZjU5M2E4N2YyNmYwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:18:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik1SenVzS242VzUzYjBnTXRETmNFalE9PSIsInZhbHVlIjoicWdJQWxjSXZMMnkyeVRIN2JYUytHYkRUOVlZRldEVUo4Z0RWTkdKVzBWcVJ0cjFGeHUzOUVWWnNCK3dvZ0V3Ni9jNTFBMFZBK0NBOG9tVDk5djR2alMzQ2g3VmJXd21mVXd4RjdGZE84bFhRSUdlNWhGRnBBdkZ2ZmhkZkpmakhSZGpuc3p6c2ZGbTNzdXdVd3gvZ2R3bWF0MFZ6UkVKR0Ftd3d5eGRBbmdrci81cVYyNTFLUWl4YjFabzRuRkRNdFMzNllxWG91TGF1L0JVZEVkdjVKZnY2L0o5dXkvWUFzVUo1Y3dOZmZQMjNCZ2d2TFg5ZitWRTlPMzJ1TnROQVBxZ01HSE81NFFqcU9IV05YZ3dseWh6N1VZNUo2QVE4bFNoQlFFZkRESkFmOTg4V3FSdWYvMGZxVjZtTU90b1c1aEpKdXhmTnVZNTdUWURTd2RLU2N5Z1I3TGJwL1pyWFVDRkxaTFZiSG4rV3BHcmRkSWZvM2NpZVBCbTlCTXNEdm9XTUg5RmlFdm5HeTBLNlhZRHBGVDEwSjErMG9IZkRsQ3dUN2ZBQnhPZ1lOZ3d0M0Z5b2F0bUd6dmRQb3YzbzREbGp3WUNDcWJ2bkVuR2RZdDFKanJUMjMxenRCdktaTnd4NXBsSkVrMFhQYjc1R29mQkNwNDU3R2J0NUk1K1EiLCJtYWMiOiI1YWY3OGYyMTllNDVlZmU3ZWFhYjlmODkxZWE3MzJhNmM0MjBhYmI5ZWYxZDc1NGVmNjViZjMyMDNlZDhlNWI4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:18:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1968421403\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1637769685 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1637769685\", {\"maxDepth\":0})</script>\n"}}