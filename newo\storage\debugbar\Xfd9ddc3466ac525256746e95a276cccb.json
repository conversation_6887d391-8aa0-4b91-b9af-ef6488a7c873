{"__meta": {"id": "Xfd9ddc3466ac525256746e95a276cccb", "datetime": "2025-06-08 00:40:16", "utime": **********.976312, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.163407, "end": **********.976334, "duration": 0.8129270076751709, "duration_str": "813ms", "measures": [{"label": "Booting", "start": **********.163407, "relative_start": 0, "end": **********.880932, "relative_end": **********.880932, "duration": 0.7175250053405762, "duration_str": "718ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.880949, "relative_start": 0.7175419330596924, "end": **********.976336, "relative_end": 1.9073486328125e-06, "duration": 0.09538698196411133, "duration_str": "95.39ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45052544, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00566, "accumulated_duration_str": "5.66ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.93392, "duration": 0.00404, "duration_str": "4.04ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 71.378}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.95207, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 71.378, "width_percent": 14.664}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.964325, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 86.042, "width_percent": 13.958}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/roles\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-9465685 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-9465685\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1159939048 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1159939048\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-507939147 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-507939147\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1320338255 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749343206865%7C38%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imo5OXNtOVBJbzJqdVpKeEhYVHNROHc9PSIsInZhbHVlIjoiNmliSU9iRzFZbG9LQlVxMCtQa2xwZG5uVnJ2UWw3eDI3Qm8ybkJkWEFOZCtkM0labHc2cktuN0hrMGplYW1uL2p0Z1hDaEdmMzFqZzVtcjVxSEpLa3diVjRaR3FDbEZOY29IN3FEVndUUVdaVTd3NWVkSzExclM2ckZtT0pvbnFUQlZNWUZveEk0Znc0bkpjYzdjSUY5VTF0dkJIQ1ZHK3JpZDhNVStzdldZcUcxakU1RUtlNkJxSUFuSHJ6VGhFNnU2dG4wZk5KczdoVHRpV1dQTFA3ODNoOHpadmhWUVB0MndNK2l4K3YwL3pqZVlDbTRqdWMydk5yRFB2aTQ4Z3czbGNhZGRsSFgvVHRXbmNnamZvd21KbHNrNXdVUWtBaTZsSmxvSTlsWXh0aFFqYkdiSysvYUNmcXRhMnUwd0tjMC90aTRKUE1ESFN3RVM3UWpTNVBsUVU5cXE2bmwzL25YZDJYTk5lcEZhMmtWeWlKVVVVYVRLTVJWa29lWWJDRDhOTFBDdTg4N1dldVZLQ1U1VjhVb3BnOTlIM3JjeHFkMTRJUVFuNnBXMzAxdDhnQzVRWW5oeVJXdFF4b0VxNVBZbHF1cFVaZ0dPajM3U1VsZVBDT3BxS1FwUlFpUnU5Tm1NOWpjZVB5cWQzMTlnb1EwVno2eWpxSnlBMm50WloiLCJtYWMiOiJiYWE2NDljNDljMmZlNWU3ODNmZTkxYzhjMmE0NzllYzJjMTkxMzY3ZDA5ODVlYzU3NWE3M2M4YmVlZjVhODUwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ii9aYjBGcFhpV2MyOXFlaUhWanFpM1E9PSIsInZhbHVlIjoidWV0R0RVa25yUnRaTjI2TEtIK0pyT3FnVzRiQlo4K2VvZnVpRURHb2dkeHJKdy93eENCRmdjU0pKU293ME1tSUY1eDRSWG9NYjBtT1ZXNEhIYXQ3VDBBcDRiMjA4NVhuczZqVkZ4UWRUTldsSUlRTUUvWjkwZDAwcHJPMTlaUGtkS09CNjlsOGpKZWNzd2hGTFBidkNpZDBDMFZiSEhBWUZPNVcyTXBWVnhHVm1tanpnemMyQTUwR3RheDZOR1hnbkdNWi9yQUJiN1lQeno3blR0dmM1aG9zbEhybDlDbUpmVEtGcExMemJPNUFFdS9iYXVVRmI2ZGNoOGhubGZHMm10emxJeEo2eU1Faytxb2tmam5FYzFXMDJJeG9ObnE5UzcvV3MxSlpRYSsxNWZCeHVjanp6dmdHaEhGeElwdVhFQlZON21uMzcvZytyOTlHUHNPVkE3aGlxRHBEd29jbTBDUE1XUXNoTVNRUC9PZ2RWRTc5eSsrUCtlMEExMWF1Vk1sRC9XNXVlRDkyYXQwVGdYTnBOamY3M2twTUdsS0llK3BPSXNDL0dvOTZ1bnBZbzNWc3JOdVYwYm9QK2Y4eTVUOThFV1MrM0xBcFBnZ3VxQkR0ZjF4Ynd1dERQSTZaek1TOHJOZm1vaWtLUGt1ZUtaaDFLdDNwUkxWWWFldisiLCJtYWMiOiJhN2NmYTU3YzM1OWI3N2ZkMmZhNGJiZTU0NWRkOTQyZWRmNjlmMzk5ZWI1ZDhjYmNkNDM2MjA1MTI3NTgzNGRlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1320338255\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1480541659 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:40:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkFENkZWcmVKZTExNlR0aUxtSThNcFE9PSIsInZhbHVlIjoiQnJoQXhRbEZnZklRNlB2TmdLMG8wS244ajAwZGkwQVFrcmo3d3NpK29PazIxSHZ2UCs5TGJRdkpLV2J1RTlsZDZNWElJUjRaM2FhcHBtaVlxZ3k0QVFqbGM4QytxN3diR0ZGc1FhMmU0MEpmK2JySVRZUGYveWc4Skg0S0ppQWdjQys1QzFkZzJDVjVOMnpHV2FCY2tqQmpGYkMvM1NVZXJhN3REOTAraU5jbG0vemdTeFhQVElvWTQ0VEN1Zm56UzZzQ1l5MnZLMnBrUC92bzJFU0hDN3dkWi83SU9Vc1NCZ0RkRVJYZHZOakJGVHJ5RHpZMDdkdGdDcm9WNXVHQmk1by9xOCtqOUJ4KzY5cTdGWU5jamx0SVlHU1N2REVVdWIrN1ZGenNETG9GczJtaUk0RkJWYUcreU9ZN2FHekpJR1FnOVNsb1VyTjR2SWs5ZUhlRDZ0ek1UN3B4Yng4Q1c5VUs2Y2s0aHRzYkZTS3BxMGRITXFvVVNVOUZyMWd2aXhlV2xIQ2dsdzdLSDAzald4NUVDR0NZRklIKzBsU0V0VGJub3ZDNHQxZ1ZQa21uazA2TnFxbTIvU3dQWjlrRmxxTUtyS3dieWFPSVJHaldxTkJEWWxJRVQzOW1XcVNvOHNWYkFPWHU1TG1qNU80dXBGRG5DQXhNdUlTYlBlSFMiLCJtYWMiOiJjMzM0NzJlMDNhZjE3NWE4NDY2YTg4OGZiOWU0ODFhOGE4NzEzNzdmMGZhOTUyYzMyNzg5MDZiOTQ0ODVkYTFjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:40:16 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjdodGhYRSt0ZDV4MTlIN3FHQXdad2c9PSIsInZhbHVlIjoiYzh4UzM2WFZCc3VEaUp4T0t2TGhsSm5IOEltSFc1aUV2ZkJmK0tPRGpDZHExVSttcUppR01ZUXB4MnJ6aHp1UktKYUVRMXR1TU9NdDc3amhhU0VwTVJqLzFRek9pRkFiWDNwaElkTGlTUDhkTm4wZUk4Z1hqaVJycVV6MWpoZlEwVXZTRlpWblJWY0p0OHNrUGdyU3VFazQzNWJ1bGwzZ09NZzI3dWk5cHRQTy9VTEdleWpPSXJZeFFhdUFCbUExNG9BV0dPK29lNHFvSi8xbFdENXFvSE9BN0ZLYkU1aERuL2xuWXhpTkpFUTFFdjkySG92TURsN0lRcStmSjd2UDlKbGRJbGphL1R4WnBuVThpaGY3OW1jNUdwL0JxZCtaMXFjdDlEdzBoeWZQWmFOU3FTVFcveFJKZy9yRkw3YnN3ZVV2bDJMYVhxZVpQa3p6WjVKamRnN1VWQkk3VzRGN1JmeEN4MjBDRHVyMTlJS2l0M2pBTlZnbVZrRlU5N2pLcjZVYTErOEUxdm41aSt4NXd1RWcwcU1xdzlPaFRwUC9wUGZzd0JoRW1Hd0FvcENUT1VKSVBhTGxrQ0RQamVzTXNqV29teTlTem5iUTRVUDRrc2Z4ZmFpaU5haE9LMnE3US85eU9XU2FDZHM2RWZrVGRnTTRlMW1jbTc4YllaY28iLCJtYWMiOiI4NjU0NGVmZDQzNzE1NjBhODNjZGM4Njk1MjkwMjkxYWNhYTIwOTAwNjdiZDIyNDAwMmI4ZTljNmUwNDAxODQ2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:40:16 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkFENkZWcmVKZTExNlR0aUxtSThNcFE9PSIsInZhbHVlIjoiQnJoQXhRbEZnZklRNlB2TmdLMG8wS244ajAwZGkwQVFrcmo3d3NpK29PazIxSHZ2UCs5TGJRdkpLV2J1RTlsZDZNWElJUjRaM2FhcHBtaVlxZ3k0QVFqbGM4QytxN3diR0ZGc1FhMmU0MEpmK2JySVRZUGYveWc4Skg0S0ppQWdjQys1QzFkZzJDVjVOMnpHV2FCY2tqQmpGYkMvM1NVZXJhN3REOTAraU5jbG0vemdTeFhQVElvWTQ0VEN1Zm56UzZzQ1l5MnZLMnBrUC92bzJFU0hDN3dkWi83SU9Vc1NCZ0RkRVJYZHZOakJGVHJ5RHpZMDdkdGdDcm9WNXVHQmk1by9xOCtqOUJ4KzY5cTdGWU5jamx0SVlHU1N2REVVdWIrN1ZGenNETG9GczJtaUk0RkJWYUcreU9ZN2FHekpJR1FnOVNsb1VyTjR2SWs5ZUhlRDZ0ek1UN3B4Yng4Q1c5VUs2Y2s0aHRzYkZTS3BxMGRITXFvVVNVOUZyMWd2aXhlV2xIQ2dsdzdLSDAzald4NUVDR0NZRklIKzBsU0V0VGJub3ZDNHQxZ1ZQa21uazA2TnFxbTIvU3dQWjlrRmxxTUtyS3dieWFPSVJHaldxTkJEWWxJRVQzOW1XcVNvOHNWYkFPWHU1TG1qNU80dXBGRG5DQXhNdUlTYlBlSFMiLCJtYWMiOiJjMzM0NzJlMDNhZjE3NWE4NDY2YTg4OGZiOWU0ODFhOGE4NzEzNzdmMGZhOTUyYzMyNzg5MDZiOTQ0ODVkYTFjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:40:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjdodGhYRSt0ZDV4MTlIN3FHQXdad2c9PSIsInZhbHVlIjoiYzh4UzM2WFZCc3VEaUp4T0t2TGhsSm5IOEltSFc1aUV2ZkJmK0tPRGpDZHExVSttcUppR01ZUXB4MnJ6aHp1UktKYUVRMXR1TU9NdDc3amhhU0VwTVJqLzFRek9pRkFiWDNwaElkTGlTUDhkTm4wZUk4Z1hqaVJycVV6MWpoZlEwVXZTRlpWblJWY0p0OHNrUGdyU3VFazQzNWJ1bGwzZ09NZzI3dWk5cHRQTy9VTEdleWpPSXJZeFFhdUFCbUExNG9BV0dPK29lNHFvSi8xbFdENXFvSE9BN0ZLYkU1aERuL2xuWXhpTkpFUTFFdjkySG92TURsN0lRcStmSjd2UDlKbGRJbGphL1R4WnBuVThpaGY3OW1jNUdwL0JxZCtaMXFjdDlEdzBoeWZQWmFOU3FTVFcveFJKZy9yRkw3YnN3ZVV2bDJMYVhxZVpQa3p6WjVKamRnN1VWQkk3VzRGN1JmeEN4MjBDRHVyMTlJS2l0M2pBTlZnbVZrRlU5N2pLcjZVYTErOEUxdm41aSt4NXd1RWcwcU1xdzlPaFRwUC9wUGZzd0JoRW1Hd0FvcENUT1VKSVBhTGxrQ0RQamVzTXNqV29teTlTem5iUTRVUDRrc2Z4ZmFpaU5haE9LMnE3US85eU9XU2FDZHM2RWZrVGRnTTRlMW1jbTc4YllaY28iLCJtYWMiOiI4NjU0NGVmZDQzNzE1NjBhODNjZGM4Njk1MjkwMjkxYWNhYTIwOTAwNjdiZDIyNDAwMmI4ZTljNmUwNDAxODQ2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:40:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1480541659\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1631031795 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1631031795\", {\"maxDepth\":0})</script>\n"}}