{"__meta": {"id": "Xcc48e5b86071d89a324b0e2d98745681", "datetime": "2025-06-08 01:05:21", "utime": **********.298831, "method": "GET", "uri": "/financial-operations/sales-analytics/customer-analytics?warehouse_id=&date_from=2025-06-01&date_to=2025-06-30", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749344720.670527, "end": **********.298856, "duration": 0.6283290386199951, "duration_str": "628ms", "measures": [{"label": "Booting", "start": 1749344720.670527, "relative_start": 0, "end": **********.162316, "relative_end": **********.162316, "duration": 0.4917891025543213, "duration_str": "492ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.162328, "relative_start": 0.49180102348327637, "end": **********.298864, "relative_end": 7.867813110351562e-06, "duration": 0.1365358829498291, "duration_str": "137ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46181280, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/sales-analytics/customer-analytics", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\SalesAnalyticsController@getCustomerAnalytics", "namespace": null, "prefix": "", "where": [], "as": "financial.sales.analytics.customers", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=359\" onclick=\"\">app/Http/Controllers/SalesAnalyticsController.php:359-488</a>"}, "queries": {"nb_statements": 11, "nb_failed_statements": 0, "accumulated_duration": 0.02658, "accumulated_duration_str": "26.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2126071, "duration": 0.00257, "duration_str": "2.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 9.669}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.22869, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 9.669, "width_percent": 2.257}, {"sql": "select count(*) as aggregate from `customers` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 368}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2337449, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:368", "source": "app/Http/Controllers/SalesAnalyticsController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=368", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "368"}, "connection": "ty", "start_percent": 11.926, "width_percent": 2.709}, {"sql": "select `id` from `customers` where `created_by` = 15 and exists (select * from `pos` where `customers`.`id` = `pos`.`customer_id` and `pos_date` between '2025-06-01' and '2025-06-30')", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 378}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2418332, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:378", "source": "app/Http/Controllers/SalesAnalyticsController.php:378", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=378", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "378"}, "connection": "ty", "start_percent": 14.635, "width_percent": 5.004}, {"sql": "select `id` from `customers` where `created_by` = 15 and exists (select * from `pos_v2` where `customers`.`id` = `pos_v2`.`customer_id` and `pos_date` between '2025-06-01' and '2025-06-30')", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 387}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.246892, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:387", "source": "app/Http/Controllers/SalesAnalyticsController.php:387", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=387", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "387"}, "connection": "ty", "start_percent": 19.639, "width_percent": 4.703}, {"sql": "select `id` from `customers` where `created_by` = 15 and exists (select * from `pos` where `customers`.`id` = `pos`.`customer_id` and `pos_date` between '2025-06-01' and '2025-06-30') and not exists (select * from `pos` where `customers`.`id` = `pos`.`customer_id` and `pos_date` < '2025-06-01')", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30", "2025-06-01"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 406}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.252102, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:406", "source": "app/Http/Controllers/SalesAnalyticsController.php:406", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=406", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "406"}, "connection": "ty", "start_percent": 24.342, "width_percent": 6.734}, {"sql": "select `id` from `customers` where `created_by` = 15 and exists (select * from `pos_v2` where `customers`.`id` = `pos_v2`.`customer_id` and `pos_date` between '2025-06-01' and '2025-06-30') and not exists (select * from `pos_v2` where `customers`.`id` = `pos_v2`.`customer_id` and `pos_date` < '2025-06-01')", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30", "2025-06-01"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 421}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.257012, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:421", "source": "app/Http/Controllers/SalesAnalyticsController.php:421", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=421", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "421"}, "connection": "ty", "start_percent": 31.076, "width_percent": 4.703}, {"sql": "select `customers`.`id`, `customers`.`name`, `customers`.`contact`, `customers`.`email`, COUNT(DISTINCT pos.id) as total_orders, COALESCE(SUM(pos_payments.amount), 0) as total_spent, COALESCE(AVG(pos_payments.amount), 0) as avg_order_value, MAX(pos.pos_date) as last_purchase_date, MIN(pos.pos_date) as first_purchase_date from `pos` inner join `customers` on `pos`.`customer_id` = `customers`.`id` left join `pos_payments` on `pos`.`id` = `pos_payments`.`pos_id` where `pos`.`created_by` = 15 and `pos`.`pos_date` between '2025-06-01' and '2025-06-30' group by `customers`.`id`, `customers`.`name`, `customers`.`contact`, `customers`.`email`", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 766}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 427}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.2622921, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:766", "source": "app/Http/Controllers/SalesAnalyticsController.php:766", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=766", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "766"}, "connection": "ty", "start_percent": 35.779, "width_percent": 15.049}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 776}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 428}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.2700741, "duration": 0.00507, "duration_str": "5.07ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:776", "source": "app/Http/Controllers/SalesAnalyticsController.php:776", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=776", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "776"}, "connection": "ty", "start_percent": 50.828, "width_percent": 19.074}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 776}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 428}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.278054, "duration": 0.00435, "duration_str": "4.35ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:776", "source": "app/Http/Controllers/SalesAnalyticsController.php:776", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=776", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "776"}, "connection": "ty", "start_percent": 69.902, "width_percent": 16.366}, {"sql": "select `customers`.`id`, `customers`.`name`, `customers`.`contact`, `customers`.`email`, COUNT(DISTINCT pos_v2.id) as total_orders, COALESCE(SUM(pos_v2_payments.amount), 0) as total_spent, COALESCE(AVG(pos_v2_payments.amount), 0) as avg_order_value, MAX(pos_v2.pos_date) as last_purchase_date, MIN(pos_v2.pos_date) as first_purchase_date from `pos_v2` inner join `customers` on `pos_v2`.`customer_id` = `customers`.`id` left join `pos_v2_payments` on `pos_v2`.`id` = `pos_v2_payments`.`pos_id` where `pos_v2`.`created_by` = 15 and `pos_v2`.`pos_date` between '2025-06-01' and '2025-06-30' group by `customers`.`id`, `customers`.`name`, `customers`.`contact`, `customers`.`email`", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 802}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 428}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.285671, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:802", "source": "app/Http/Controllers/SalesAnalyticsController.php:802", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=802", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "802"}, "connection": "ty", "start_percent": 86.268, "width_percent": 13.732}]}, "models": {"data": {"App\\Models\\Customer": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/sales-analytics\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/sales-analytics/customer-analytics", "status_code": "<pre class=sf-dump id=sf-dump-2010876231 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2010876231\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-463497509 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-463497509\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-811880621 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-811880621\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-687607386 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749344713396%7C49%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjVHS3Qwblg2Q2gwditYNHNTZ0RJdFE9PSIsInZhbHVlIjoiSDJTcWZqbWd3ZHVBWGIxcUdhV2xNUVB0K1Z2dDNMcHdSMkhTVngvbC9WU2ZKWUdRT25aN2hPMFQyaFVEZ2N2VjBOb2x1c0J3RThXOFFzOG00U2Ftd2hBOEZwdG92b3NvTHJEK2ZNZE9ZVG1zVHNaT2o0L3Jtd0UzUkFyeU16TGM2c1lFN0REZGt0akZPV0ExN2pHTVorS2l3TVhHbjRiRFZ6cDdEVnMzdDNpSWpac1ZpRGpkQzRUblE4TVd2NnF6VjdVZ3FiZVVpaWJYZGVMR2g2QTdhZkhwZEM5SEZ6NHBqaXN0MkxjSm1pQ2NEUnpPZkg0MExrSXcyQXhCSzMzTnJvdjdTUXl5TEdrZTdjOXNET3JiNnpkM1RDNkc1MzFjYzN0bS81VWdwd2svaWRCSEVoM1NXMkNkUGpmODJUNWR5ODNKOFVGcFBBVTNHRUtCLzNDL0hqdDdtSWlHUnJiZmlkSHlNdG1ZMXNZMUgyMWxta1daTVoyZC9xTjJYa1YxZGdOLzZhdmlDWmw5Qm44KzZ1a3lWck0zTFhiWXhQakU3RFNjNUpzaHJ5VnRmK1YxcDVqWGkydHpaMU13Ujk3MTB0ZTZhYkVCUnIzVEhTejBCVlpBUXdWeUE5Y0VacWVFTS9qUWVkeXdGdG5MN3lJN3dqdVZycnAwd1hwTHFhckIiLCJtYWMiOiI1NWQ4MmM5ZDg4YmVlNzUyNGMwYTRmZmFkYmFiZGVhZDI1MGJhMmQ3ZGFlODlkZWY3N2I3YjgyN2M0OTczZTQ0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IllyOXRzUC8yMWhWMHpteUdQczR0UHc9PSIsInZhbHVlIjoiM2tHa0F0M3ovQkpmbVN5ajRnRnVxeGpCZzFYVkloS253WmwyRkRpczhIUENNZGJ6MzJqdWEzemoyUGdIeXlObFJFdmJiSlRwNU9EL0E3aTVBaFRiNmZpYTFUK1dtbmJURkhPV055YXN6aEtqeEV2OUx5MlpaTlFSWFNJQ2JOTDkxL3RRdXVMNVpOK0poVFNBTkJZK1Z2ZE5OMG44M1NEUDVBY1Y0Z1FmQnR0NWhPVlJkb1hXZ2JMOU9EVnA1a0pxOVEyWXRPZThzcmVCZE5jUGdvRnBMbDZVaVoxeVZtWVdqcGJrODFRcWlFS3RUa3h1Y3JsemV1enRqTklJeWl2eElqbEhNeHhWYS81TE9Fc1UwN3F1TGVEMTJGWHk4ZFRReDNQOHJzRlVKU1J3RlcwMWYxNVVqcE1GdkIyS3hvdm9sOGZsaGh3cDZ2NWpZZldweGMycmkzYXg2MThPeU9vWGw5SDJUN2YyN2h4MStMUm4rdWQreVlDREJEMG13L1ZOTkFYM0pQU3JiWlRHdlluRkZNbTV0OWd2djdCN2wwQUlEMENTNU9RU0R1dEVSRVFhb3piUVh0ZHB5bzZJeEl6UTY0SEs5UUo0eTN2V0JJazJWQlJ2dnR0SHFtNmZRVi80NitnSWZIbGxtOW96eVRLME9VVlptRmxiaVZuSGNPcHMiLCJtYWMiOiJkNmM3ZjNmMDRmYWQzMGNiNmUwY2YxNjdhMjkwMmFiZjM1ZTkwYzVlNWZiNGFhYTNhNDVlZGFjYTRiOTlhYjU5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-687607386\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1010326824 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1010326824\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1932811721 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 01:05:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik5WcHJJKy9sSUJpU2IxMmdHWGFxOXc9PSIsInZhbHVlIjoiaHIrczJGQmlaaDNTeXJySnFlZjBQbzgwaFF5K09wL0ZjR2RVenRzYUNQWXNnR1VaOVhtSHBpdFY5M1loc1kzQnNqL0wrcVcycXA1cEliSVlPNEtvQlVQSmZoS2pYYUlXM0NHZHQ3N0NyN0tFYXd5N0dEbWEzUFd3OTZxdGcxcFhVU0ZNaVhrSmNlVnUxNHU5NkNnVU4rNDlhWnVLRkkwMGF1VEdjRUxIUnh4bmN1NlRXSnBKcnJ3ckR0bjRNZlNkOTY3b0YxcmlGc0RmWnkvdFdOQVpUdzFmbzFEZ0NvTEZpWmN4ZmxuZDYzZmkreVRla0VIMnpNaFlDbkoxeXpjcHNXMHFBRVVyUFk2RTVhNkFNbCtzYmlBMGJZSTdZcXVjelJqTVVhc3M2TSsrdkFOaC93blVTRkhMUjNPN2piN3dRV3gzbkl6WnovNnVrcFBSOEdxWDhkMGxoRUlzVXZvbzZtNENmTHg5MVBSbE80TWZXZlBoWXIvbUc1MnZhZ281RW52RldvQUlUT21JVHE3L0daeDJmdlBnUkQ5UUZweGYxTENDbisrQm9reVAzR054enNXRHg4SnEyNW1lRkZNNHVhMEFRZVA5dEZZYWpqbFhGNmNJMFJUNGx6UnVYbFRZR0dVd015ZlBZcm15SzROWFo0d3ZkOW1lUS84SjJMQzkiLCJtYWMiOiI4OTk3M2IzNzI5OWZlMTE0YzFlMGRjOTU5NDJiY2Y0YTU2ZjMzMWM2YzViYWZjOWRmZmIyOGRmOWQ0NjI3ZTUxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:05:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InUvRGJXZWIyWG92NGVMOFBldXpQS2c9PSIsInZhbHVlIjoiWWJST20rZmw4MGNTVStCY2xnQ0dOb0s5YWJXek5GN1NMS1RkdFFIdGhuNFU0ODBwQTUzUlN5VFNiTWpKM1poUnN3L1hMQ2VSbk1qdXVQdDRpbXdCQUdSUnB5TnhaRlR4TEZScWtJSUlJUW8rWE5UN3hCVVJLQ0pxUTcvSnVSY2RHUE5FbXFiQUxWanduYWVGWHV4MjVRU25SODg3Q0k4ZkdXZ1RkRm0vVTg1LzhmcXY2N2l6RE8rNjlWUzVnTzJwTk15SG1scG03dmVkSDFCa0JNeVdJSUdITjVRVEpZVW9vR1gzSWU4NHhRQzVLUEVVaVl1eEVmM0FpTGgzMWxCcEZEWlBMVFVIcU5LZ043U0tJMUpZSzhySFhRZkJiblFaUE10ZE5VK1c5UE9GQ3dMZFovSVJkcW5KZXNVcFBFKytLQlBPblVPNEFPMTdrMmRMNHJKbVd6UWN1L0JIdXlhODEvNHk0ODcxcXp4RCsySDhzRUVnZW1vbXV6M3NEZEs0Y2R2N1BWZUJWNDFPQ2Y4MHU2V1ZIRERUSjVVUS9UMEt0MzRHeWo0ZjJNdE5SRzdWVVJjNGdPVGhQZUpIOFd6bzVpS0M1UG9jcVZwcVJLYk42VWIzVFFRaFR2a3N5YmxpWXY0S3hlTmU2SnhpVTlkTHlha1RRVUNYV2QxRzkzbXAiLCJtYWMiOiI4OWEwNmRhOTU0NDA0MGZlZjlkZmRjNTYzMGI4MDI1Yjk0ZDUxMjdmYWY1ZmY0YTQxY2UwYmEwY2JlOTllMTczIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:05:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik5WcHJJKy9sSUJpU2IxMmdHWGFxOXc9PSIsInZhbHVlIjoiaHIrczJGQmlaaDNTeXJySnFlZjBQbzgwaFF5K09wL0ZjR2RVenRzYUNQWXNnR1VaOVhtSHBpdFY5M1loc1kzQnNqL0wrcVcycXA1cEliSVlPNEtvQlVQSmZoS2pYYUlXM0NHZHQ3N0NyN0tFYXd5N0dEbWEzUFd3OTZxdGcxcFhVU0ZNaVhrSmNlVnUxNHU5NkNnVU4rNDlhWnVLRkkwMGF1VEdjRUxIUnh4bmN1NlRXSnBKcnJ3ckR0bjRNZlNkOTY3b0YxcmlGc0RmWnkvdFdOQVpUdzFmbzFEZ0NvTEZpWmN4ZmxuZDYzZmkreVRla0VIMnpNaFlDbkoxeXpjcHNXMHFBRVVyUFk2RTVhNkFNbCtzYmlBMGJZSTdZcXVjelJqTVVhc3M2TSsrdkFOaC93blVTRkhMUjNPN2piN3dRV3gzbkl6WnovNnVrcFBSOEdxWDhkMGxoRUlzVXZvbzZtNENmTHg5MVBSbE80TWZXZlBoWXIvbUc1MnZhZ281RW52RldvQUlUT21JVHE3L0daeDJmdlBnUkQ5UUZweGYxTENDbisrQm9reVAzR054enNXRHg4SnEyNW1lRkZNNHVhMEFRZVA5dEZZYWpqbFhGNmNJMFJUNGx6UnVYbFRZR0dVd015ZlBZcm15SzROWFo0d3ZkOW1lUS84SjJMQzkiLCJtYWMiOiI4OTk3M2IzNzI5OWZlMTE0YzFlMGRjOTU5NDJiY2Y0YTU2ZjMzMWM2YzViYWZjOWRmZmIyOGRmOWQ0NjI3ZTUxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:05:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InUvRGJXZWIyWG92NGVMOFBldXpQS2c9PSIsInZhbHVlIjoiWWJST20rZmw4MGNTVStCY2xnQ0dOb0s5YWJXek5GN1NMS1RkdFFIdGhuNFU0ODBwQTUzUlN5VFNiTWpKM1poUnN3L1hMQ2VSbk1qdXVQdDRpbXdCQUdSUnB5TnhaRlR4TEZScWtJSUlJUW8rWE5UN3hCVVJLQ0pxUTcvSnVSY2RHUE5FbXFiQUxWanduYWVGWHV4MjVRU25SODg3Q0k4ZkdXZ1RkRm0vVTg1LzhmcXY2N2l6RE8rNjlWUzVnTzJwTk15SG1scG03dmVkSDFCa0JNeVdJSUdITjVRVEpZVW9vR1gzSWU4NHhRQzVLUEVVaVl1eEVmM0FpTGgzMWxCcEZEWlBMVFVIcU5LZ043U0tJMUpZSzhySFhRZkJiblFaUE10ZE5VK1c5UE9GQ3dMZFovSVJkcW5KZXNVcFBFKytLQlBPblVPNEFPMTdrMmRMNHJKbVd6UWN1L0JIdXlhODEvNHk0ODcxcXp4RCsySDhzRUVnZW1vbXV6M3NEZEs0Y2R2N1BWZUJWNDFPQ2Y4MHU2V1ZIRERUSjVVUS9UMEt0MzRHeWo0ZjJNdE5SRzdWVVJjNGdPVGhQZUpIOFd6bzVpS0M1UG9jcVZwcVJLYk42VWIzVFFRaFR2a3N5YmxpWXY0S3hlTmU2SnhpVTlkTHlha1RRVUNYV2QxRzkzbXAiLCJtYWMiOiI4OWEwNmRhOTU0NDA0MGZlZjlkZmRjNTYzMGI4MDI1Yjk0ZDUxMjdmYWY1ZmY0YTQxY2UwYmEwY2JlOTllMTczIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:05:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1932811721\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1093090408 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1093090408\", {\"maxDepth\":0})</script>\n"}}