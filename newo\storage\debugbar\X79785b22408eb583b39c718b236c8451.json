{"__meta": {"id": "X79785b22408eb583b39c718b236c8451", "datetime": "2025-06-08 00:57:58", "utime": **********.90319, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.217385, "end": **********.903218, "duration": 0.6858329772949219, "duration_str": "686ms", "measures": [{"label": "Booting", "start": **********.217385, "relative_start": 0, "end": **********.74725, "relative_end": **********.74725, "duration": 0.529865026473999, "duration_str": "530ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.747263, "relative_start": 0.5298779010772705, "end": **********.903221, "relative_end": 2.86102294921875e-06, "duration": 0.15595793724060059, "duration_str": "156ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48115192, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02733, "accumulated_duration_str": "27.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8006961, "duration": 0.02277, "duration_str": "22.77ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 83.315}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.84059, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 83.315, "width_percent": 2.634}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.8672311, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 85.95, "width_percent": 3.696}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.871204, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 89.645, "width_percent": 2.744}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.8815129, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 92.389, "width_percent": 5.269}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.888998, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 97.658, "width_percent": 2.342}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-925626213 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-925626213\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.879496, "xdebug_link": null}]}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1576881651 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1576881651\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1092515121 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1092515121\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1658061551 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1658061551\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-516928787 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i31pha%7C1749344276347%7C8%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ii8rdFAwaGUrV2Fjb3hHS0dQVTM5Rnc9PSIsInZhbHVlIjoiazlzQ1JZSC8yZURnTUIrRzB4SFBsaDkzN0dNZUdlUXdSb1VDY2lPTUM0SjhkcDE1YTFwTzhYbHBYa0J0WldGeWM4RVJIcmJ0T2t4RVRiUmRIRzF4NVkxTzZTYjcydHJ3TEdaNmhRM25xV0F4MkZmK3hTWTFNWUtYK3VobnRtM1dGdjhFbFlsSzcxbXFVK2tGeGtCVUNNazVOR2sxR283aVhudy9pa0Y0RzlvVHBLZWZkRUFQVWRUTkdrRUxVVFRSTGhoUWdoUFhxMnRnUGhacGJ1Um90cDN1QkxwSnlOdHhESXhiUTQ2d1ViaCszRGtKVnFuR1VXSHd3aFQ0SzJEdmtuanpwTUFlZWkxaWYyYzZtM3dyL1NqV20weHhFL3hPM3hTcjBnbnJ5L0FwZEs2TGl6aWJTdUJneDB5ZmRZM1FxQU9MMDkwdkhFc1FFQ0xOZlBxNzRqaEVwZi8wNWVUZ2s0Mm05d0ZENUhjSmUzUm5LenBRcElqU1hLUVRVTUtwR0EzWGR6ejl1MUlMZUZkL3RNejVUb1lpZ2lYSWlTYWRCMEh0QjhKM3hPZzZyZnZsMmlZUXRZSERUVnlPRGFSNVp1ck1hMWt5Nkg5MU5UWEVmYmkrR2FlSW95dFVnVnNqb1EwbHdCdEd2SzVodnVSVmY2RkFmQXNDczlRajhhUGwiLCJtYWMiOiI1ZDM2YTllZTY0ODVjMGM5MmU0Y2UxNTY2ZWNkNGU2MjQzNWY5NzVkYTVkY2VlMDFmMTVjMTM1MWJmYjQ5NmVjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Inc0cGhnQkljeXRSSXRadm9wSUtSY3c9PSIsInZhbHVlIjoiZDVYV2NsMDYzMSswREd6Q0tSSkN0NnFXMGk2TTlCaFNvaDdkbGs1UHA0VEEvWlhJV1lyWmR0T2puV21hZVhjZ01ZMkphZjVpb2pEOFcvdlY2RzdnaXJ3eFZrMXppenAvQmhRR25wdXlqU0g4eTd6N2FTblE0R0N6ejRSUC9xaGdHWEtxSUxKb0xOSitaMUlPU0RWK0RmU0FNOXN0TUxZbWpmVlZMUTBaLytFdUIvSHJyV21ZaEZSeE9mK2Z3R1ZCQ2loSUdwQmpLQlBOUUtFZ1lpdzVscHRoUGpJWGoxNlRxbUdKTVFFU3dxVzNuV2I4ZTVMSlAzWllJaEZUWVBqYmxVZ3dZSzMrRUQrZHd0ejJQZXRxRXBTWjJFRklWV3JEWkR5RzhLS2xlbHQrbDFnb2YrZnlSTGhlWlNkejZtYWpCTHNQbTVVZCtaRWJtZDJDOWdpS05RVW41NDFCbStSMTRGZmo1dUlSZDIzNjBpZGQzREIzMkRvWC9za0RyMnl4WFhmMFFMK1BSZGJaNUlBaXNiSWxSczJrNTF6ZEdDR1F3N2prY1NoeXJwdHIwMnBWb1FsY0pCNzBMZ2N6WGtneGx2aHFMazJCbTdMT2dRK3dwb2hQSmxBOFdWRDJ2cDUwK2k0ZTN2V2x5Yi95RGFzcld2U0ZZaXN0QkJJelQva2MiLCJtYWMiOiJkMmQzN2M4ZDk0NmY0NDAxYWVjYTE3MmZiYzljMWI1ZTUyMGE5MmM2N2RlNDY4ZDA3MWEwMmFjOGM1NzJkMDViIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-516928787\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-516983891 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-516983891\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-571169364 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:57:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFoS04zK0NUZ2VLSXYvTitUcnQyNmc9PSIsInZhbHVlIjoiWU5OUWFXbzFWU2I5czZyNlJWeENxakxCYjd0dFBPRStqQUlSOWNsenQrOWIxdy9ENVJZelUvMXExUWNBaVZ5SVd2aFZQMDFqWWdxMXMxMndqODhyWU1PVlFKZlcvQW5Wb3hNSVZ1RFJ4VnVpaWhsczBWblhwUTQ1RldJcWo1bFJDRU1QR0xOamlQRDlBNjZQYnRmckE3Sm1UbEZyd3phdVJwZnVoNzFBOGFHanJCK2UwNmlkWlp3TWZQeHNYYjlQb2tKM2hKYnF2UWJNVTljWXZWc05HR253M0hyNHR6THF3OHp3eWVhNXRUdXczeDVJRGhRQVVOTDltclJLTjNHdnNKa0JoVHdFU3hLSzdOVEdxYmoyOXd2MUg0N3JUZEVhdDhtVjNVbDBQYWZ6ei9SbWVXTUs0ZEZ0NUJQRElTN1lDZ2RTYkR1M2l6QTU1MGd0b1NTZFFXcFY4aCtpb0Ztb29WcS94SWVzUjJFMndpMnd1d2MvTjZ2cXNkK2xHcVBQeTBTTnc0YWduU1owTFgzOWRhMVBGNDdOcktUWnpNY0FEblZmeDduTW1lYXV1RUFrQ3doMEgwcThCSExMd2JuTnYzeUs4bVFXMytOSlJVMGJ6Z0VNR25PVS95bzRBNVZLRHRPOWU0TnhWYnh0YXJ0bVNUbnZJazVPYWJiSGR0TDEiLCJtYWMiOiI0MGE4NmM3MzllYmRmOWYzODc5NTJhZTAzNDM1OTRmOGVhNGY1YWUwNzIyYjY0OGM4ZTEzYzcyNDg3YjBiMzczIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:57:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IisxRTl3LzhmRE5udkFnOXFJVFRJalE9PSIsInZhbHVlIjoiYTg0Uko2bDZ5dERiQnVwRWNNQUNlOFcyRStqcVdsMG5ITUlENWR0ZEIvbXZIQlBNZklUQjc3d1BraUhXN0kvZGYrcCtIQWlzN20vblYrQkoxZTRadVFjdlVDMDExb1pseWpMWFpXczhGaE1nZkpvL1dPUDFXY29NV2pqZ2YzaUsxOS9yM3B1RFdiLytYUGRYYjRhQW5Ub3VYTzdOZXhJcE1jRzNOQTdCeHdJY245NWN5MUh6VWVYaFJVMi9SdTNhdEZXQVZ5a1U0SXllNEt1MUtsMXN1S3B4ZVFDd2NKUVBJWHZ1Y1N4UzhmWHl0cEg2N0tZNmE0SitVWE5Xemp3bFlERXhKaVFSTG50VU1ha2lGM1lzTk9iWHpmdGFSamJSSkpJZEFoRmV2T092QXlNM2QwVVo1V2ZmQzdZWjBNRTN1QW8zTml0c3VQWUx3U1lsY2s5bXZ5QmdqZjcreU1iclMwTDhycU1UcWxCalpMbGVPcnEycVN1K2R2YlhqSUZVM3RmcWx6V2VDVnFKQm1aNitnTHJFM0hxQktCWUN6L1JzTDBIb2JLejF1cy9LRjRqbFp1V0d4TTUvVGJ1dC9IbjRHOW1MRHh2Z3V3OXd1R3dBbGRYdUVoSGdXVWVZUU1TdDRyZS90TmJESzNxRVBpYWtPaThpODAxK3kxNHVUV1EiLCJtYWMiOiI3MWM1MzEyZjRmNjMzNzdhMGUzNDgzMjFjYWJmMDBkNjlkODgzMzZlNWI2MGEwMDYyYWI1MDZkNGQ1MWIwMjdmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:57:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFoS04zK0NUZ2VLSXYvTitUcnQyNmc9PSIsInZhbHVlIjoiWU5OUWFXbzFWU2I5czZyNlJWeENxakxCYjd0dFBPRStqQUlSOWNsenQrOWIxdy9ENVJZelUvMXExUWNBaVZ5SVd2aFZQMDFqWWdxMXMxMndqODhyWU1PVlFKZlcvQW5Wb3hNSVZ1RFJ4VnVpaWhsczBWblhwUTQ1RldJcWo1bFJDRU1QR0xOamlQRDlBNjZQYnRmckE3Sm1UbEZyd3phdVJwZnVoNzFBOGFHanJCK2UwNmlkWlp3TWZQeHNYYjlQb2tKM2hKYnF2UWJNVTljWXZWc05HR253M0hyNHR6THF3OHp3eWVhNXRUdXczeDVJRGhRQVVOTDltclJLTjNHdnNKa0JoVHdFU3hLSzdOVEdxYmoyOXd2MUg0N3JUZEVhdDhtVjNVbDBQYWZ6ei9SbWVXTUs0ZEZ0NUJQRElTN1lDZ2RTYkR1M2l6QTU1MGd0b1NTZFFXcFY4aCtpb0Ztb29WcS94SWVzUjJFMndpMnd1d2MvTjZ2cXNkK2xHcVBQeTBTTnc0YWduU1owTFgzOWRhMVBGNDdOcktUWnpNY0FEblZmeDduTW1lYXV1RUFrQ3doMEgwcThCSExMd2JuTnYzeUs4bVFXMytOSlJVMGJ6Z0VNR25PVS95bzRBNVZLRHRPOWU0TnhWYnh0YXJ0bVNUbnZJazVPYWJiSGR0TDEiLCJtYWMiOiI0MGE4NmM3MzllYmRmOWYzODc5NTJhZTAzNDM1OTRmOGVhNGY1YWUwNzIyYjY0OGM4ZTEzYzcyNDg3YjBiMzczIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:57:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IisxRTl3LzhmRE5udkFnOXFJVFRJalE9PSIsInZhbHVlIjoiYTg0Uko2bDZ5dERiQnVwRWNNQUNlOFcyRStqcVdsMG5ITUlENWR0ZEIvbXZIQlBNZklUQjc3d1BraUhXN0kvZGYrcCtIQWlzN20vblYrQkoxZTRadVFjdlVDMDExb1pseWpMWFpXczhGaE1nZkpvL1dPUDFXY29NV2pqZ2YzaUsxOS9yM3B1RFdiLytYUGRYYjRhQW5Ub3VYTzdOZXhJcE1jRzNOQTdCeHdJY245NWN5MUh6VWVYaFJVMi9SdTNhdEZXQVZ5a1U0SXllNEt1MUtsMXN1S3B4ZVFDd2NKUVBJWHZ1Y1N4UzhmWHl0cEg2N0tZNmE0SitVWE5Xemp3bFlERXhKaVFSTG50VU1ha2lGM1lzTk9iWHpmdGFSamJSSkpJZEFoRmV2T092QXlNM2QwVVo1V2ZmQzdZWjBNRTN1QW8zTml0c3VQWUx3U1lsY2s5bXZ5QmdqZjcreU1iclMwTDhycU1UcWxCalpMbGVPcnEycVN1K2R2YlhqSUZVM3RmcWx6V2VDVnFKQm1aNitnTHJFM0hxQktCWUN6L1JzTDBIb2JLejF1cy9LRjRqbFp1V0d4TTUvVGJ1dC9IbjRHOW1MRHh2Z3V3OXd1R3dBbGRYdUVoSGdXVWVZUU1TdDRyZS90TmJESzNxRVBpYWtPaThpODAxK3kxNHVUV1EiLCJtYWMiOiI3MWM1MzEyZjRmNjMzNzdhMGUzNDgzMjFjYWJmMDBkNjlkODgzMzZlNWI2MGEwMDYyYWI1MDZkNGQ1MWIwMjdmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:57:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-571169364\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1196573607 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1196573607\", {\"maxDepth\":0})</script>\n"}}