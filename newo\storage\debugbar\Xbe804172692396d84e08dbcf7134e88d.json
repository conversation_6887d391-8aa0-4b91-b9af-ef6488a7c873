{"__meta": {"id": "Xbe804172692396d84e08dbcf7134e88d", "datetime": "2025-06-08 00:58:26", "utime": **********.973574, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.30229, "end": **********.973601, "duration": 0.6713111400604248, "duration_str": "671ms", "measures": [{"label": "Booting", "start": **********.30229, "relative_start": 0, "end": **********.890888, "relative_end": **********.890888, "duration": 0.5885980129241943, "duration_str": "589ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.890905, "relative_start": 0.5886149406433105, "end": **********.973604, "relative_end": 2.86102294921875e-06, "duration": 0.08269906044006348, "duration_str": "82.7ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45576560, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00401, "accumulated_duration_str": "4.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.936579, "duration": 0.00278, "duration_str": "2.78ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 69.327}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.951197, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 69.327, "width_percent": 14.713}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9603162, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 84.04, "width_percent": 15.96}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-890897082 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-890897082\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1892097458 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1892097458\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-340678481 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-340678481\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i31pha%7C1749344302839%7C9%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImlOcVFuYzJoZkIwb2VVTXJOa1FtRmc9PSIsInZhbHVlIjoibEIyNWM1Q2wxcTNsZExxbzZCRlFNTTZjbzZya1hhK3NQMHdTZ0h1ZkI4VEF2T1hJRnJEdVBQSnYrMWxyb3RpVEZkV1dxUzc2dm1ESVJoQWhoSFU5REU3ZTB4bjVBbUFXTVNXWG9qM3BwUWZVK0FCcUxuejZXN3dTV1p3TG9OMWJyWElFdTZwTDlscVJIT3Y0eXdJY3orNHZrN1NkMlVERmVHdWIxQ2o3RytXTFdxbThQRHNaRlErbjh3WWUrMnZmM3ZHdjRjT0JSNTRlRk1vZy96MnNoeVowN0RZNFdwZktWbkNya0wvQWF1V0tQL0dWdTdqNGk2ZldGZUozT29oNzFORHdENExESFFoYjV3YUllSFFGejhUZDhWaEk1NDRkd3psL2NzRmJScGp6dlRXVXppRWJsaUpMNEdzdUhjaW9WZDY5OUxEV1dYSnJyUnY1clN6T3BnZmtnb0ljaHhVTEtiWWloL3QzZnZmeHAxZlZVK0FOcmtHcG5rQWlVc2loQmlOaUpUQVdWSXZDaWY3Y1l1TTI1NDQ1TGJYZTFoLytkUHRXVm1xOFMzZk8vUytsRmU1QWxUUVlGazNxWGpOREZTaFhzOExnV2pPYmR5MFl4b1RBZjJTWHlDSzJBcDNCYzgyMnlaeXo5cFhpZW1TZnFrVlkxOGJoYjVtUStUejMiLCJtYWMiOiIzOTIzY2I2MzI0M2Q5Zjc0YjNlYjA1YWJlZmY5MTg0Yjk4NTk1NzAzMDg3ZmZiNThiOWM5ZGM4ODY1NmVlMGVlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlZiZjFTeTJzNWJFMkVoVFlIbklkd1E9PSIsInZhbHVlIjoibGIrbHlhRWFpWDF0OEVFUFZsdFNKVU9ORlBPRVQxR1Q0czA3M0JKTFZpZWtvT1dEL3l1dzNmMEg4VUZoWVlHSEI0bTg2Q3hYeisySVhpb0h1aWJMbGp2SENBRnVsMUNiN2JhamhOMDJVVW9JaVhsSG43UDlIUmhxS0d1cEdLY3E2ZDNmOVNxRnJOeURkMVE0RHczb3BvbkhmYUdrVWJKY0ZJNG5HYlpnZFk4WWpZZG15aGRrWVNtcFVHdzJaM2dYbThjR2N3MzZQcTNhdTdtRmY3YXVIQ0lSQ24vUGE0TlVMTnhaZHc3dmw0QUFxVFF1UHA5N053ME1yVXh3YkdDMkZ5ZGltZUNVcm1QM2xRWXo0bE1Bdk9mcjZoYjFrM0t6bUZiSXZINndhaitvYjV2UnZEVGRPQnpVTFJTWUdCZ2xFU255RlVQNTlGSXBLSFJnR3FOTGpVWGwzTTA4Q2Rhc0E4S2ZEYTk1cEZDbjg1bDFpcThoWWdWUWs2ZVJQRkxEMk9DWU4wRkg1Y0pIS2N6U2lDMVNDR0dwWGFIK2xYOEVCSGNNNnNJTC9ILzFqVy9Zd2tHMUFDSFJ6WkcxYWJZRzI0T21UdmpIVWYyRVc0QXZYcGVtQ1V5aVU3c3JzZmNPOXUxbkxNMGNYWXFDQm9hUE9aZnhIL3pPdExiQlhMN3IiLCJtYWMiOiJkZmQ5MTk0NjAxYzdjODIwNmFkMjg2OGNmMGY1YjJiNTY5OTAxMDNkYjc1ODdiZGZkNTU4YzQ2NjNkY2FkMGY0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:58:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImpJc3BUM3k1RmY4MExPOWtIcFh6S1E9PSIsInZhbHVlIjoiUEpDc0p4dkE5VDROVkVJeVNJc3RzeU1wcDhRM3FQU1J4aE45MnNQeG5zTTZxaTd4bGhjMElmWVlYd00vSU1kVGd2ejF3OHBFanY2M1REcWtyYSsxNXlRTTVxS0ZmNEhGbmZ2ZnYxU1JnRW1uZmJoTWhnd1JERWxBRkdSSzh3a3BQSUdKU3F2SktCR01SZU95cWh6WDhCSW9XNTdqZEo1MHhraldWSjd4TEdGWHhkck5XWTg0bEdrSnFFSXR1a0IxZUJUR3NHYmwxQzhMZ3E5TURmd05CdmZrOXV4U3F1cngzbFQydE8wQWwvekh0RDRKQlpTMVRoS0dwZGhKYk81Y3A3QTVna1hpdDErWVNjYzN3T0s2SWhITlRUWExJUXY2Vld4WDdlYjZCVndNbHRpaGszTE1zSEhWUVV4WThOVWlJTWtjK0VuSXJEWmRxNFBuRkVaZEZwMVpkTlZHOWp1NlllM3pScHgwRFlzSGRWK1oybDJUWldtSXlyaUlVR2FjdFV4MEw1eVpBSklIckhoVDRMSFFhMkpiK25ZeG15d0tlbm16YzlVTy9JRzJMa1oyWjlNcWhqMFV4Mi9UeDRvRkNHV2RxUjQ5dGRLMU1sN2Z1bjlxUkg3SERjakhWZlhCTkk3UG9BeTE2KzdEODRuTktVYjRuS1hlK0w1aHF2TzgiLCJtYWMiOiJlOTNlNmJlODZhZjFlNTNhYjMxNWYwMDRkYzU3YmQ3ZTFmNTExZWVlNzc1ZmVkMWIxODk5MWM1MGYyNDhiODJiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:58:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im0vYmFyWitQN1RrRXNNYnhSKzVQRHc9PSIsInZhbHVlIjoiRVNodzJMbmJEakxPbExIT002Z1ZZMi9Tb2JmQ3BnU0NwaEZZT1JQblNDc1V0ZG9ma1JYd1lLUk5XY2IzUFJ2YjlPeGprKzI3SUhrdm04YXpBTVNrRmROWlk0VEN3Z1ZpWjg4YVNPNWJzL0lqb0RNZ2I1dmQzUXlLbExhT1Zib2VsM0x3aWdnSWFvNmlHdkE0WWlUZXk3R2E1aWZ5d21obXFhMS9jYXpXOXhsRzY4NGNWR2gvcjcyWjYwcDhpbCtpZjlCUjl2RXY4UzVBcVFRKzVESFlVL0s5YUhlazRibHFrekdGYTFxNUpmOUNWd2dobERIY3UwL0xLUXVHVEE0SStKRmxXd1Z1S2Q5VVJXNXF0aE9JaGxDUWZQY2hvcVFUKzdzcHdMODZ6eWJVRHN1cUFqQzVEVGRMaENZcHYzeWxKQm5kdHJFT08vVUdtQXNCOEZqUDdZUE9DQWk0WE1ldGd5ZWlCNWx6bGFaK2NoTERIdGV0V1NMdHVPQVBVWlE2T1QwUklqL1lRelJCdXVUMml6a0d4dGZoY1RXQW5LRHQ1SnhHL2NkMGY1alhReDlKOFk4ZVdVTngzYzJZa296UFduSkE1RjFjdFM5anYzdXppeko0a0JTUUJhRlFSK0VEd1l1Qy84ci9KSFUvYUNwTG1rbTVZRzRBMUQxTEpqQWYiLCJtYWMiOiJmOWNiZTJiYjVjNDkyNjQ3NzljZDMwNDVkZDhjYjczOTY0YWY3NWEwMTNjY2MzZDVjNjA2NWJiYWQwOGExYzZkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:58:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImpJc3BUM3k1RmY4MExPOWtIcFh6S1E9PSIsInZhbHVlIjoiUEpDc0p4dkE5VDROVkVJeVNJc3RzeU1wcDhRM3FQU1J4aE45MnNQeG5zTTZxaTd4bGhjMElmWVlYd00vSU1kVGd2ejF3OHBFanY2M1REcWtyYSsxNXlRTTVxS0ZmNEhGbmZ2ZnYxU1JnRW1uZmJoTWhnd1JERWxBRkdSSzh3a3BQSUdKU3F2SktCR01SZU95cWh6WDhCSW9XNTdqZEo1MHhraldWSjd4TEdGWHhkck5XWTg0bEdrSnFFSXR1a0IxZUJUR3NHYmwxQzhMZ3E5TURmd05CdmZrOXV4U3F1cngzbFQydE8wQWwvekh0RDRKQlpTMVRoS0dwZGhKYk81Y3A3QTVna1hpdDErWVNjYzN3T0s2SWhITlRUWExJUXY2Vld4WDdlYjZCVndNbHRpaGszTE1zSEhWUVV4WThOVWlJTWtjK0VuSXJEWmRxNFBuRkVaZEZwMVpkTlZHOWp1NlllM3pScHgwRFlzSGRWK1oybDJUWldtSXlyaUlVR2FjdFV4MEw1eVpBSklIckhoVDRMSFFhMkpiK25ZeG15d0tlbm16YzlVTy9JRzJMa1oyWjlNcWhqMFV4Mi9UeDRvRkNHV2RxUjQ5dGRLMU1sN2Z1bjlxUkg3SERjakhWZlhCTkk3UG9BeTE2KzdEODRuTktVYjRuS1hlK0w1aHF2TzgiLCJtYWMiOiJlOTNlNmJlODZhZjFlNTNhYjMxNWYwMDRkYzU3YmQ3ZTFmNTExZWVlNzc1ZmVkMWIxODk5MWM1MGYyNDhiODJiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:58:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im0vYmFyWitQN1RrRXNNYnhSKzVQRHc9PSIsInZhbHVlIjoiRVNodzJMbmJEakxPbExIT002Z1ZZMi9Tb2JmQ3BnU0NwaEZZT1JQblNDc1V0ZG9ma1JYd1lLUk5XY2IzUFJ2YjlPeGprKzI3SUhrdm04YXpBTVNrRmROWlk0VEN3Z1ZpWjg4YVNPNWJzL0lqb0RNZ2I1dmQzUXlLbExhT1Zib2VsM0x3aWdnSWFvNmlHdkE0WWlUZXk3R2E1aWZ5d21obXFhMS9jYXpXOXhsRzY4NGNWR2gvcjcyWjYwcDhpbCtpZjlCUjl2RXY4UzVBcVFRKzVESFlVL0s5YUhlazRibHFrekdGYTFxNUpmOUNWd2dobERIY3UwL0xLUXVHVEE0SStKRmxXd1Z1S2Q5VVJXNXF0aE9JaGxDUWZQY2hvcVFUKzdzcHdMODZ6eWJVRHN1cUFqQzVEVGRMaENZcHYzeWxKQm5kdHJFT08vVUdtQXNCOEZqUDdZUE9DQWk0WE1ldGd5ZWlCNWx6bGFaK2NoTERIdGV0V1NMdHVPQVBVWlE2T1QwUklqL1lRelJCdXVUMml6a0d4dGZoY1RXQW5LRHQ1SnhHL2NkMGY1alhReDlKOFk4ZVdVTngzYzJZa296UFduSkE1RjFjdFM5anYzdXppeko0a0JTUUJhRlFSK0VEd1l1Qy84ci9KSFUvYUNwTG1rbTVZRzRBMUQxTEpqQWYiLCJtYWMiOiJmOWNiZTJiYjVjNDkyNjQ3NzljZDMwNDVkZDhjYjczOTY0YWY3NWEwMTNjY2MzZDVjNjA2NWJiYWQwOGExYzZkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:58:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-576240170 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-576240170\", {\"maxDepth\":0})</script>\n"}}