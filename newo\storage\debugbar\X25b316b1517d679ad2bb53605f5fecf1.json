{"__meta": {"id": "X25b316b1517d679ad2bb53605f5fecf1", "datetime": "2025-06-08 00:58:22", "utime": **********.669576, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749344301.92678, "end": **********.669602, "duration": 0.7428219318389893, "duration_str": "743ms", "measures": [{"label": "Booting", "start": 1749344301.92678, "relative_start": 0, "end": **********.553171, "relative_end": **********.553171, "duration": 0.6263909339904785, "duration_str": "626ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.553188, "relative_start": 0.6264081001281738, "end": **********.669605, "relative_end": 3.0994415283203125e-06, "duration": 0.11641693115234375, "duration_str": "116ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45593608, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02732, "accumulated_duration_str": "27.32ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.600987, "duration": 0.02569, "duration_str": "25.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 94.034}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.640284, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 94.034, "width_percent": 2.562}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.652222, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.596, "width_percent": 3.404}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1554754630 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i31pha%7C1749344276347%7C8%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ijl1VzZtQWJWY3k4MGlEV0d0enNQbXc9PSIsInZhbHVlIjoiU1NaK0VWSlE2cGFUeVJYaHpqMDZhOHZENDJvMEhseUh6YkRYTnhXWlZBV0pMTTF0cnFVekhtNWFVMXB4Zm83NnhaQVJ5WGQ1YjJ2VEhvU1dHL0E5Vlh2cUpWQzNsZldBRFlmWUJ4WFNWNUpYemh6SkJWeEdiS2F4Nms4UGZPcDNvblJKZm8rWEpPSnE5cy9EcDk0RENQcmZpUUMzTXlPS3QzTmVlRnY1bDgrclpDTnV2Uk1na1NKZHp1Q3pkWlI1aitSYUpkMVN2ZVZyVXBncnRDV3B2bHBKZzBURHVKV0lidXFFbnB1ZjlHZ1AwZWZuWmVYV3BFZ2d2d1J2aHJWbEZWU21nY1ZRQlFhSXZaSDgzN1JxcGNUQWZwUjMrUGV6L2hxR0ZLTVo2bWxUV2g3eHN5dkpWYzFOdVJhd0VHemdzdXNiZGUxanBocWNoWFYyN2tJSEFEK1U5VW4va2JoR1NFWTVJaHFTbCsrTXR1MnpUZ2lMVXdtaFROYzV2RWtZc2EvZVlFOXZLQWJqU2EyMCtqeHFYRGRLUWpOeStXNU5iQnRvZnhobnB1SlZCZVpoNzB3L0FKMm56RGxway9qVm41L2Q4VHdGZzFIbml2ME83enJTdHhLay9JRVFTZ1c5Nzc1MlVqRmRmYlBnUk5ndWc4SlFMeTUxWmhQRUduUUgiLCJtYWMiOiI3YmEzZTQxZDVlMWFmNmMxM2RkZDM3NTNiZWY3ZjgzZmJiYmIxZGM5ZTZmM2MwMzljOTU2NzVkZTExZWViMGNkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkNCNTd3V0wvVTNvdnJjRFB1aWk4cUE9PSIsInZhbHVlIjoiT1NzdG1rTkZadHlVenJDTXRlSFNad2xRaVBmajVrSUlzeFdQUTRoZEljcktCSUdYUFNjS1Y2TURlNGVvTisyQkRPUGRkWGFtYWlzMi9nTzBkdDY3Wk5Temx1Myt0Qm5lb0FEc1B3R2ZhZHdWK3UzQjhKdzJROWR3NlhWK3dKQkRhbm5NZ2xOMEJsQXpNRnI3bmQ5WmVBMnQ5eWxnc3pqYWZvQ09iOUp3NHIzRjFDNEkyMlUrODI0blZBQ3Y3QTFIQnVtaGFaeXlHUzIzeUlDREsvOThsWnhvRFZXWWcvZWdXcjJpUTFPeHhBYnZHK0NTNWdacllTaXd6dW5xajdvM0VWVlEvNmpwK3BnUmFoVmhxTVNocEFxUE1WTkxQekw3bkNRUFFYS2hWQThCcDQvYndBdFd2bDdQMHExWmc1T2oyYjJyZGpEeEdzcExlak4zQmg4UGdxWlJqSEZqbWdEMnRudXc3cWVxS0JoNm9icGMwSEVnUTJRNkZHYVBQSzcrYm1NZnpEc2M2cXV5VUFUdEN1OEVmOXcvTnk3QlBua1R5KzBWcGUyQ1hLajFTUWE3b1RnaVpIOGlOOG1RTGY3c3dTUXFxNmF2SEI4TXczUXlqYjVJNFdmQ2pKaEt0UTJsblcyN1c1YThpMldGMnBnODhmcEhNNzFweVFMWDJLak0iLCJtYWMiOiJmYzIxZDIyNjZiZjIwY2U4ZTE0YjNjNDk4YzhlZDFmMWNiZjIzMWU2MTkyZGY4NmU0ZjEzYTk3MjIyNjQ1YzczIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1554754630\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1979124960 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1979124960\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-349529031 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:58:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxiVlkweGdXMElTN0p0THY0QS9hZlE9PSIsInZhbHVlIjoiMEFvNmFOMms2WnJMUEJuUU9rb2c5U2g2RGRzbXI5RDIyZVh4T2JoQkVWYnNrTzNEZ2NTMkViUFgzTXdZU1lKSmdqNENIdlE2clNoNk1KRko2MkZtZ1lCRXh4N2R1MXBvMFRqZmI5QzNLK3NWQjg3clVGR2Y5N1RucnYyYU1mb0htU2Q5dkdSN1lwT3M0L1BXYmtQdDN4elc2WmRybVRWaG43eWc0eWJ3MGlYSmovaEkzVkpVQWZhSkNYeUlubHkxSUdnTFRMOGdtSko1RU1KOWgvVmoyVy9URDZudFFJQ1o0NUwxOWdVWGhTTTBhemRMd0xyNjJaa0hZTE1ReURrWnF2SUhSNWt0OHdhMkJWNGcxaHh2YUxUN2NscGt6cEJZWWlVR2RJVmdaVnZSSk9kL1lKMis4UXIwb3YybDcyVjN4aEhhNGVMajFEUkN6ay9aVDNCaFErck8xV1hOaGsxYWxrUDIxd1BjZWlZaU9EUERGZFVhQnlFL3BQNlZ4Rk9vTmVMWGdUSlNBdzhyQ1dBY2RHT0lhaFA1akd1elNMWEJqZHpTZ2NDWU9HWnBLdVRKU0E2dmlnSEFUZTJINXVIRjV5cEs4dE9sQXpINHE0Q2R3NWFMUnM4eVd6WVRZYzZuNVJvTTF1MU5rejduVjRuaXQrb0trOXMwL2FpZHBzcGoiLCJtYWMiOiJmZTRmZTZjM2VkMmIyMTIyODM1NWE2ZTQ1MzRlNzdiM2IyNzJiNTFkOGFhZWNiYzdjMTk2ZTIwNTAxNDU4OGYzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:58:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ilk1YmU4M1JiTTBCUUZ5R3QvNk1Samc9PSIsInZhbHVlIjoibXJoL0U2S1dEVU8waTBmZCtiUWFJNzE1SXprME1yMVdGNzhGUlJVS0ZyK25EQkY5aUZRQnYwOHN0WXo3aG5aMFJVYjYrTDE4THJXVE81ZWF0V2Fnd29JZnA1N2YrSHJ6Wkcvajl5Q3ZIMUhPamZwRFR3QlJYYlRlQzBEbkgzenNlVExWWG1jZUNzMzZvSWNVRjVUSVc0SUZqdGZjMEFpVE5naEVrZGVGalE2d0t2ZG92VVREQVdNL1hmd3dqa1dSWTE2WjFrbXFGNHlWQWRib1lTNE90SGlTQnRXSEdlOHYvV1JJUTVYYlRUODlRZG43aGk3czNSYXlYMnMzZWw3ZitsRXZ0TEQrRzJETTh5OFdraGgrZWJ6SUVzN2V5bWdZUWRYQ2QwV0xSVzZkdFlsVnZPVVVGMWc1UENCYmN1MG1qVW1tYXJZd2wzZG1qNnFweWIzU1FtTjE4dGZRaHphaXlQVkhESXVEYjlhZmE0blVzOW1SelZIN0VnRUh0bnQwUG4xRkJ6RkhhM0JwTTZ1SnQwZUFTaWV0N0RJRE43aXErOWxoak9XQU10U25tc0czNVF3YUxPVHR0aUUxcUJNekEyRE40TnFxZEtMT2c0RGRLZXU5QVBJN21kVytXeGY1aDNlWUN1VDV4eWRLNkZ2cmFtQTZ5OXZ5RitYTFZlemciLCJtYWMiOiJhYzQ2NWYyNjRkNGFmZDRlMjNkZDk4YzE0OWUyYTc5YzE2MWJhZWFkYjA4NmM0ODk2ODZkYWYyYWIzOTE0NzdjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:58:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxiVlkweGdXMElTN0p0THY0QS9hZlE9PSIsInZhbHVlIjoiMEFvNmFOMms2WnJMUEJuUU9rb2c5U2g2RGRzbXI5RDIyZVh4T2JoQkVWYnNrTzNEZ2NTMkViUFgzTXdZU1lKSmdqNENIdlE2clNoNk1KRko2MkZtZ1lCRXh4N2R1MXBvMFRqZmI5QzNLK3NWQjg3clVGR2Y5N1RucnYyYU1mb0htU2Q5dkdSN1lwT3M0L1BXYmtQdDN4elc2WmRybVRWaG43eWc0eWJ3MGlYSmovaEkzVkpVQWZhSkNYeUlubHkxSUdnTFRMOGdtSko1RU1KOWgvVmoyVy9URDZudFFJQ1o0NUwxOWdVWGhTTTBhemRMd0xyNjJaa0hZTE1ReURrWnF2SUhSNWt0OHdhMkJWNGcxaHh2YUxUN2NscGt6cEJZWWlVR2RJVmdaVnZSSk9kL1lKMis4UXIwb3YybDcyVjN4aEhhNGVMajFEUkN6ay9aVDNCaFErck8xV1hOaGsxYWxrUDIxd1BjZWlZaU9EUERGZFVhQnlFL3BQNlZ4Rk9vTmVMWGdUSlNBdzhyQ1dBY2RHT0lhaFA1akd1elNMWEJqZHpTZ2NDWU9HWnBLdVRKU0E2dmlnSEFUZTJINXVIRjV5cEs4dE9sQXpINHE0Q2R3NWFMUnM4eVd6WVRZYzZuNVJvTTF1MU5rejduVjRuaXQrb0trOXMwL2FpZHBzcGoiLCJtYWMiOiJmZTRmZTZjM2VkMmIyMTIyODM1NWE2ZTQ1MzRlNzdiM2IyNzJiNTFkOGFhZWNiYzdjMTk2ZTIwNTAxNDU4OGYzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:58:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ilk1YmU4M1JiTTBCUUZ5R3QvNk1Samc9PSIsInZhbHVlIjoibXJoL0U2S1dEVU8waTBmZCtiUWFJNzE1SXprME1yMVdGNzhGUlJVS0ZyK25EQkY5aUZRQnYwOHN0WXo3aG5aMFJVYjYrTDE4THJXVE81ZWF0V2Fnd29JZnA1N2YrSHJ6Wkcvajl5Q3ZIMUhPamZwRFR3QlJYYlRlQzBEbkgzenNlVExWWG1jZUNzMzZvSWNVRjVUSVc0SUZqdGZjMEFpVE5naEVrZGVGalE2d0t2ZG92VVREQVdNL1hmd3dqa1dSWTE2WjFrbXFGNHlWQWRib1lTNE90SGlTQnRXSEdlOHYvV1JJUTVYYlRUODlRZG43aGk3czNSYXlYMnMzZWw3ZitsRXZ0TEQrRzJETTh5OFdraGgrZWJ6SUVzN2V5bWdZUWRYQ2QwV0xSVzZkdFlsVnZPVVVGMWc1UENCYmN1MG1qVW1tYXJZd2wzZG1qNnFweWIzU1FtTjE4dGZRaHphaXlQVkhESXVEYjlhZmE0blVzOW1SelZIN0VnRUh0bnQwUG4xRkJ6RkhhM0JwTTZ1SnQwZUFTaWV0N0RJRE43aXErOWxoak9XQU10U25tc0czNVF3YUxPVHR0aUUxcUJNekEyRE40TnFxZEtMT2c0RGRLZXU5QVBJN21kVytXeGY1aDNlWUN1VDV4eWRLNkZ2cmFtQTZ5OXZ5RitYTFZlemciLCJtYWMiOiJhYzQ2NWYyNjRkNGFmZDRlMjNkZDk4YzE0OWUyYTc5YzE2MWJhZWFkYjA4NmM0ODk2ODZkYWYyYWIzOTE0NzdjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:58:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-349529031\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n"}}