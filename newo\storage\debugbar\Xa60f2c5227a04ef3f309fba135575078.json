{"__meta": {"id": "Xa60f2c5227a04ef3f309fba135575078", "datetime": "2025-06-08 00:40:02", "utime": **********.372224, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749343201.575384, "end": **********.372255, "duration": 0.7968711853027344, "duration_str": "797ms", "measures": [{"label": "Booting", "start": 1749343201.575384, "relative_start": 0, "end": **********.244399, "relative_end": **********.244399, "duration": 0.6690151691436768, "duration_str": "669ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.244415, "relative_start": 0.6690311431884766, "end": **********.372258, "relative_end": 2.86102294921875e-06, "duration": 0.12784290313720703, "duration_str": "128ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45039840, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.030200000000000005, "accumulated_duration_str": "30.2ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.299607, "duration": 0.028730000000000002, "duration_str": "28.73ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 95.132}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3455281, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 95.132, "width_percent": 2.152}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.357083, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 97.285, "width_percent": 2.715}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-571321326 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-571321326\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1560131709 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1560131709\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-473526099 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-473526099\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-646697977 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749343199728%7C36%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlY3WW9yVnVKT2VVZVB5QSt2OGlMdEE9PSIsInZhbHVlIjoielJCb3Jqb0o5bjBIUVdMangwdkphenhnZHFSTGNGelNJK2RnTkV3VGNxMFhDSmRXaEVrM1l1TXNnQXpaNVovTUVsSEJEWjlWd1F2VWl2VlAyelh6R2p1K1ZtV2ZtazhHMkVGNkMyVWtHc2xDYzMxaHVRRlZzUHBNZ0JlNzVUaCtyS0EySVRlK3J3ZGVhZlUxakd0R215QnM3SUp6NW1RNXlqd0krcjE5WHpxdi9XUCtnNnF1ZjVSWGpzMEZpRTJ4UndkUGpRWnVNK0d2NFQ5SW94QytCczRwWDZBZHVtQTY4elBwckxBNTlMMno5Y0wxM1Y3eXh2VWs2S28vSk11clkvd0RwRlBUcnNHdmlTbm0vTFJMc043NGdlVXA4Yk9DQjFRK3FQMXN5dHUzVEdRSm5HZ2RZMUc4SmkzNUpDS2YvN2ZIWHFuYTBvVlEvR0lacmVhejlpbFkrK0hqMWYwNFhyMFdHNVJTaUxxVXFzMGZjRVJyNHdMcWhHOGhNTXQ4M1J2RmN0bHlPTXVqVkU1UDNLT1ZoZXNjSE1VZ1F0MGxZVWtpV2dQQVNuTUhNUGROUzUvczVHc091UEw1N0xzV0RCSHYvTzViK1pCWlJSUktEbmVUalR5MXNXUHM2VVB3Wlh3WmphcDBEcWxBRFFXeUFRWjJ5VStOZUFzb0NTNnUiLCJtYWMiOiJkMmFiMDU4NjMyYWVhNzVmNDkyY2IxNWEwMjE2ZjM0MWMyZjgzOWYwYjRkMzE4ZWFjMzQ5NmU5ZTI2MTlhNzIzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Im1WcSsrSWdOVGJhcmJxanJ1SXNVR0E9PSIsInZhbHVlIjoiYXNWUmxQcUU5SzFQbWtZSC9jZjJGdkJacDE1Zk5JWCs0UC8wcHoxRituMGtiRi8vMFRPMTZtU3NPak1udXIzYTdFcGQweXpMWjlMMnREVGFaY2Q4VnRsVFlaam5oTmRUdFBFODdHNU1jUCs3NHh4eWxyQjUwYjhTWTcxc2Q4VnBuQURCWTFJamwzVjFVc2FUVEVwUFRMY1NZZ0M2UGdVVXpjVHFVTmF5a2VGQzJ2d0xmblpzWGYzYlRwbHRWNlN5b2ZPKzYxMThNQXNJN2xBZno1RnJuemdyQmlMOWlRdVE5MndLMTJPNkZPV0h0UDB5TE15ZGIrNFZpQnBKemgxbDl6Z2FNYkpYek51U3JtT1U5aHBTUjN5TVNkY2pOY0I2SVBDVVBoeXo5U2w4dCtZc05udnN1NmF6V0N0ellxV2xqYXlRMW4xaDdYOFBSMXJMaFFaeHJuU0lBTkVad2lOVU15NjRBR3RkWGJvSmhQOEtkTXF3MDJRR1pwb3Y5alhJMFdDWnJYQ3hBR0Y3dzk4M05wd1M2cUl1M3RKNjNJVVBKNTUxT0NULzIvUjZKVHYzYVFITzFoaG01ai9yUWcxTkFNKzY4WWxVRm5oT1J0dHBFS1BEZjMvaW11TUxuSENicDhsYVh1VlRDcEFYS2I5c0FWRjloWDlzMGsvTWVaaWYiLCJtYWMiOiI3MGY0NWUwZjYyNDhlMGRmZTgxMzFjOGFlYjgzYmZkMzc1ZjQ5ZWNkMThkNWEyZGUxMzE3MmE1NTEzYTM1M2YyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-646697977\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1047608243 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1047608243\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1301365903 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:40:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImIxODlSN2FtTGJGQ2VTeVZWTGNaVWc9PSIsInZhbHVlIjoiZ1dZOWNBZVZlSW9LRmlhVEVRTC9nYU9CRThpUUZvNkcyMnFtWkdIa0RobitXaXRzd2I0RnFSZmI5Q0laVDBIVWdLMWhSQjd6Ty96WEtPZ2VvU3d4dEpncGJ3MHJ5T2p1RzlwbWU3WE4zMVQzT0lRb1FEc2tNclhJUlNCNVRwcmZ4TlplNDdqYU9SM0pNYS92ZGdXc3hmQmJpaTMzWTdCZ3V4S0p4MC9BZk1YcjllVEMyeXNFZkk4ZWlHVnZWZFFyTXFrTTNaWjV6dEk1RlJSaVFxOWMwbzFPWVVITWdaY3dZTGhFYjZ4eGVSWHdnU01CVG9SN2NlV3FMV3g1NW9jYnlWcFZMdlFlQ2x2eGZEN1g2MFF0WVNETHNxenZ2aWszdGlxOXMrd0NIVDlFNGtoT25MZ2c5STFDa1dKbEt2ajdWaEoxUjFZblR2WHFLTGFYaXc4V3JQTm12UTlVTG45QnUvSHBxNm1nYlYwMG9QaGNqYWU4VzgyMlZIVVIrNjMza3RQZHI4OFYwWE0xbVo0OGNMd1FoNDRSSkxVekxLeXVvNFkwWlA5ZTRDRlkra3hDclA3VFlUUUhDZFdUSkFmeHRUd0QrTjkrU2t6Zm5vb09uV1h2dWtMdkNyZTlFU2hXZW0vME5YUlhNY1JlTWRkaXEvcG1hckxXTFJQVStvNXoiLCJtYWMiOiI2MDgyOTc2NzVhMzBiMzA1ZjFhMDY4ZmYyNjAxYWFhNDFiMzQ5Zjg0MzM1NmVkNDYxZjlkNTk4ODE1MzlmMTliIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:40:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlVDN0ZtU2RvcktPL29qWGVBMXNWL2c9PSIsInZhbHVlIjoibkNURnEzc0orZ1o1enNkMStzY1FybXIrWE1BSWNUdHlrRGduTDF5WkNXTDcrMC9zQk5oQnJoOERsV2I3WEhtRG5vZDRCMFp5dWF6aHBFbW1IelRIS3hvckpwdXFYbG83WTdveUNVM1lITUtzODJZUnZkV2lCMGdoNXZLc0xtOXp2Z3hLZ3JSNWF3cndMVnM2amFwWmdRNEpxcHJYTTJJOEpXN2d0Vm5WRnBRN0lFZGkwTjhRUGpSdFBiUnVNUTZ1RC82eUZqTzU3bXZnWUNOWFI3ZUdocXNmeVpld1ZBZFJaemt4L3BYNWhsWHgzTUllRk01SVkvNWQ3TkhxTVFSUTI5Q21TNXNocTZtOTduRHVSaFhEcTQ0a2F4TlR1M0o3NWZxYmZwazkydHpLM0VDRWRJNUU3RVg3enZVK0FlZG9GZ2tYMkdJc0h1ODBoamdmZ3pQY1ZLVFA3VGEvNk5Xd2IyUHIzT2dnM3NERi9GNkNtY3hDOE5LUXhXa3B5OFAvTmY2WW1CcU52N2srd1BVZDlIbmdUZkdMVHZyNkwxK2FoZXF5bkdBUkoxZ0tCTkpmUCt3OVJQdjhMdFVSWGhBdXZJT1lYYTViTGYwKzBEdDYwajgxaTRkWlliSEgyTzBvZjk0VlkvRHVBcVkyU2RSWU1KdlhKRjdGZFNxa2ZvS0ciLCJtYWMiOiIzYTNlOTMwNmU5OGZhZTg0Y2MxM2ExNDc3MjhkZmM1YWMxNDMzODc0OGM2ZmUzZGI5MjM3MGUxYjA0OTZhMmZiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:40:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImIxODlSN2FtTGJGQ2VTeVZWTGNaVWc9PSIsInZhbHVlIjoiZ1dZOWNBZVZlSW9LRmlhVEVRTC9nYU9CRThpUUZvNkcyMnFtWkdIa0RobitXaXRzd2I0RnFSZmI5Q0laVDBIVWdLMWhSQjd6Ty96WEtPZ2VvU3d4dEpncGJ3MHJ5T2p1RzlwbWU3WE4zMVQzT0lRb1FEc2tNclhJUlNCNVRwcmZ4TlplNDdqYU9SM0pNYS92ZGdXc3hmQmJpaTMzWTdCZ3V4S0p4MC9BZk1YcjllVEMyeXNFZkk4ZWlHVnZWZFFyTXFrTTNaWjV6dEk1RlJSaVFxOWMwbzFPWVVITWdaY3dZTGhFYjZ4eGVSWHdnU01CVG9SN2NlV3FMV3g1NW9jYnlWcFZMdlFlQ2x2eGZEN1g2MFF0WVNETHNxenZ2aWszdGlxOXMrd0NIVDlFNGtoT25MZ2c5STFDa1dKbEt2ajdWaEoxUjFZblR2WHFLTGFYaXc4V3JQTm12UTlVTG45QnUvSHBxNm1nYlYwMG9QaGNqYWU4VzgyMlZIVVIrNjMza3RQZHI4OFYwWE0xbVo0OGNMd1FoNDRSSkxVekxLeXVvNFkwWlA5ZTRDRlkra3hDclA3VFlUUUhDZFdUSkFmeHRUd0QrTjkrU2t6Zm5vb09uV1h2dWtMdkNyZTlFU2hXZW0vME5YUlhNY1JlTWRkaXEvcG1hckxXTFJQVStvNXoiLCJtYWMiOiI2MDgyOTc2NzVhMzBiMzA1ZjFhMDY4ZmYyNjAxYWFhNDFiMzQ5Zjg0MzM1NmVkNDYxZjlkNTk4ODE1MzlmMTliIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:40:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlVDN0ZtU2RvcktPL29qWGVBMXNWL2c9PSIsInZhbHVlIjoibkNURnEzc0orZ1o1enNkMStzY1FybXIrWE1BSWNUdHlrRGduTDF5WkNXTDcrMC9zQk5oQnJoOERsV2I3WEhtRG5vZDRCMFp5dWF6aHBFbW1IelRIS3hvckpwdXFYbG83WTdveUNVM1lITUtzODJZUnZkV2lCMGdoNXZLc0xtOXp2Z3hLZ3JSNWF3cndMVnM2amFwWmdRNEpxcHJYTTJJOEpXN2d0Vm5WRnBRN0lFZGkwTjhRUGpSdFBiUnVNUTZ1RC82eUZqTzU3bXZnWUNOWFI3ZUdocXNmeVpld1ZBZFJaemt4L3BYNWhsWHgzTUllRk01SVkvNWQ3TkhxTVFSUTI5Q21TNXNocTZtOTduRHVSaFhEcTQ0a2F4TlR1M0o3NWZxYmZwazkydHpLM0VDRWRJNUU3RVg3enZVK0FlZG9GZ2tYMkdJc0h1ODBoamdmZ3pQY1ZLVFA3VGEvNk5Xd2IyUHIzT2dnM3NERi9GNkNtY3hDOE5LUXhXa3B5OFAvTmY2WW1CcU52N2srd1BVZDlIbmdUZkdMVHZyNkwxK2FoZXF5bkdBUkoxZ0tCTkpmUCt3OVJQdjhMdFVSWGhBdXZJT1lYYTViTGYwKzBEdDYwajgxaTRkWlliSEgyTzBvZjk0VlkvRHVBcVkyU2RSWU1KdlhKRjdGZFNxa2ZvS0ciLCJtYWMiOiIzYTNlOTMwNmU5OGZhZTg0Y2MxM2ExNDc3MjhkZmM1YWMxNDMzODc0OGM2ZmUzZGI5MjM3MGUxYjA0OTZhMmZiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:40:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1301365903\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1562377685 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1562377685\", {\"maxDepth\":0})</script>\n"}}