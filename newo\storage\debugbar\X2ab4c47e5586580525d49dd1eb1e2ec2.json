{"__meta": {"id": "X2ab4c47e5586580525d49dd1eb1e2ec2", "datetime": "2025-06-08 01:14:52", "utime": **********.306884, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749345291.585886, "end": **********.306904, "duration": 0.7210180759429932, "duration_str": "721ms", "measures": [{"label": "Booting", "start": 1749345291.585886, "relative_start": 0, "end": **********.206834, "relative_end": **********.206834, "duration": 0.620948076248169, "duration_str": "621ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.206854, "relative_start": 0.6209681034088135, "end": **********.306906, "relative_end": 1.9073486328125e-06, "duration": 0.1000518798828125, "duration_str": "100ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45040704, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02709, "accumulated_duration_str": "27.09ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.247845, "duration": 0.02566, "duration_str": "25.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 94.721}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.287668, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 94.721, "width_percent": 2.141}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.296365, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.862, "width_percent": 3.138}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/customer\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-236829906 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-236829906\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-557362478 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-557362478\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-422317660 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-422317660\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1627315922 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749345290058%7C52%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IllqZTd5ZjZnTDJ3QXYvaFB3WjZjREE9PSIsInZhbHVlIjoiWWo1Q0RHK01oUy9BaXFlelFmTGVuZXF0bXRLRkhVaVNSMXFDZHQzUVlRME1tQ2F4RUFPenpDU2RWUFNXYUhkaDMwL0FkYnRBT25jOUdBRFhaUUc3S0hZUXBPUFFvbnUxOTNXa2lMRU12ZEMyVXY0YTREY21sc3M5NkVJV0YwenRZM1JFQ3E1QmVGUDdWakQwd0lkOFBQQU5qZWtlN3M0cTV2R2FZUmdRUWZvVVU0aXovcHBZQVJRMWhSdGpMczM0UnZ0K0RyVHR6bG11TUYxZmhnaWcyZ0FEOXlGc1VXdzQ3VUtXUEdad1lMWlNyTHJaRUpNVHZLSThQSytGMTZHZnNyeXV5S29sK1BMcFhUOE45MkdNRHh4NERRaUZ0QzV6cExHTVVyb2gvYm9RMFhkbUx3dWFZY0MzOVpKb2R5NDRNejZicU43M3NwZ3hGOW1uMUhQYmdyT2p0VXc2TGRnSzVyTmxITDNIS1RzaVBpMzdiVXlZaXNLdnZNSWpmSG5VZ1R6cTdLSlRCcjU5M2U5VW1Nc3Y3bW1WYnJybk9MdXQxUG1GUHhzaExNaXJTYjd5ZzZEQXNha1B0REs1OHJnRCt5ZENYRHFRTUNLcmZ6QVZzdnlQMVFWUCtuRFQ5Y2t3NDlyWGpZbFAyQ3kwbkNzZWdSOG9nMkg0L2E5aTliZXAiLCJtYWMiOiI4ZGMzNzkzNTZmMzFiMmQ0MGNjYTU1ZmNjZmZmOWI2ZDMwNzQ0YzlkYWUwNDFmZGY3NmJlNzU1MzQ4ZTU0N2E5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkdNcENxVXdwYzlxSEczWnFWZkZNM1E9PSIsInZhbHVlIjoic2JVeXR4c2c1Q2grV3JIVDNOUFNQK1g3Qk1xVEFrN0p5NXdxY05Ea3h0MzVVY09nUFM1R1N3d0NCYnRzVWZaSTZWTlhPeWYwNUVncUxLTm81eDNWa2ZpaXJhbWFrd3gwRnA3Z3hxS1o0djFoSGFQUFFxNGk3bDRtd0F3MzZTbENXS2xlYXBWS0c1a0tnZDlNb2ZId3NzaldFQktaZVJYNFdYU2FqSkU5K2xSaTUxVkdEMkhkbHBlbjJuSGJBY0MwZytUN05wQTY3eUdRMnQvRElpaEZXdXprR0FBQWduT25qRkZqbXNNakR2WGtyaE80SUdOY05FaEJWM0R1VnVDdUpLdXozemREUkNRMkVGZ2JRSlVhNFlLMVU0U3lOUEJqa2kzeGZ1eldQaHN3Nk5tVlFrL1h3VUdwSUFpRHZMTnByQlFibXJYL0JRUm1LcjR1SFF6STZwRHRUdVAvQ0RreUdKTklUTlFFQVVEcHE2ZHExVkdjaFlVWm92K0tueExySDN3dmFsWHpyNU1vWlFENGI0V2tFTVErZWxLWFQ5amlwM0xOeFdxcGFMMW9tL2dYcW0vWHhaQk1UU2NTcmJEcmNQTFFyRFUzeEh1REwxaDNnb1RRTnBEWDBqRk5hK29aODlGM09lS0IvbnRZeXV5RkU0V3Vpc0JOc2UrYXo4NXMiLCJtYWMiOiIxZmJkYzUyODVlMTNhYjdkNDNiNWE4Mjc5NDdiYWNlMWM4ZWVhMGM1NTAzOGE5ZjcwZDQ1NzYyOGU1OGI2MjA0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1627315922\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-579067702 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-579067702\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1764138878 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 01:14:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImpLSndyUDZIQW9NWFlwS0t2dW9ZSVE9PSIsInZhbHVlIjoiV2hXVU9jQmpIZEw5WlFubmlSSWdYbXpJTDJuSE82T0N5VFFrQlB0Y0cydEtqcXRQQTQvUlBQVVh0UzVuQStNeHVjK2pGQWZmcURUeFkzeHh0SXFIRzB1MnJwalkyZ1JDejJGb2lhM3lTbzRFa29WZkppK2J3NlVFMEdoOG1RZDcxeFdCSEdvL1NkM295L0J3VmhrRXUrZUtVcUNyT0Q4cmdXZnJLYW1kMk5xeVlCaHhRMXAydVYwdkdYcGtvYUxacFNCUCtOcEVBbEtmemhobHVsdGI2NEZXMmx3VzByYmordFBCdVNsMWM0VkRia0dCb2kwZk5IMnBBY1J3cHdKQ0JYTjZ6bXpCbk1wOE5qNFgvTG5IdmVPaFdIQTlUZkNSZVVtWXZDaHpMQU1RK2RQTWVVWjRJdlJtUVFjZDlSbllsMllZUFhac1NqTjVDT1hxTGlMLzg5UjlETVJOcnBkNzd6TEZkTGJzUE5PUDBWZ1oxUGo2Mm54enJNT05CV2xGZStaeFp2UjRhK3JaKysxdDFPU2JnT3lhSDZlYkFiTkZ4aVp2dGpRSnM3NHAyVWlpZG13VnBxY0hKQzFKcDRmOXJvMGRWUDJuQkhaWlBpRVE0U3B5Ukc0TzJNRjQrMUhZbkw2UkljVUJpUEdOT1BCZTBUbG5mMUdXQTFDOHBNNWgiLCJtYWMiOiI1NWViZTMyNTAwODgyZDllOWMyZGM5MjMxMGZmZDMyYTllZWQxM2M0N2U2Y2RhZjgxNmVmOTQxNjYxMDBjOTZkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:14:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImlwVElVMUVoeWpkeXRtaGRmZ1RpNFE9PSIsInZhbHVlIjoiL2VLcEdkdFg2R2N3RDNyVjJQcnhnTHAyNmZPTW1Rc3ZjWmV4UnZsVERkcTJKRkRrUm5kYkdsdzNlMXZMSUNCM0thZUhQNXhlWFl2WmVqN2FJb2c4aXllditOZnBZdDliUFFhUEwvbzdrcG54Mys3aTh4MGJtQnIzNklVMmVpSlNKbm1YcXBxQVJjeGYrU3llK2p6WXBPRmpKemFEZ0JiZmhLamxxa1hYakJLMjM3S2NrdVNnV0RLamdWZzd6d3B6MTNCNGpHMXBjdmNBaXhLbnBlbmNsT2srczJxclVtRjViZWx2TkxHdklad01aaVRXdldLRm9EMGhkdWZFVndLRDJiR05wRmd6eG1Ucm56QWh5YTNNMCtwZkhEU3ljeFlGbXJIQUZORzNTVEo5VmIyOGNVN2RCT1pkc1pRTHhkajNESFMvQXh3UFQwVmJqM0tFMTVZdGc4aXZmcnEyejRXYzBqZFlZQXZUcHZGQllKdFdobW8ra1I5bkdTVUFBSm0xU2cveGRPWG1vTllUSXkxYmg5T1lTNFg0VHE4SU1MOXZ1VUZzN1pMNDhsMEFFdUhoQ0UyTW1mSWRVSnlQMEoydXRsZHliUVp2RUl3MDdzVm00Qm5lQ0E0R1cwTDFvRFFLMEtSNGN0UmU0V0pRd3JzeWs4N1o3WlRMUVR0cUI3ZzIiLCJtYWMiOiI5OTg1ZDA1NGJkNWRiM2VkMTBiNThmYmIxNzAyMGQ5NTZkNjBkOTgzZDgwNDZiMWRjZjdiNjExNjA2MmRmY2NlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:14:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImpLSndyUDZIQW9NWFlwS0t2dW9ZSVE9PSIsInZhbHVlIjoiV2hXVU9jQmpIZEw5WlFubmlSSWdYbXpJTDJuSE82T0N5VFFrQlB0Y0cydEtqcXRQQTQvUlBQVVh0UzVuQStNeHVjK2pGQWZmcURUeFkzeHh0SXFIRzB1MnJwalkyZ1JDejJGb2lhM3lTbzRFa29WZkppK2J3NlVFMEdoOG1RZDcxeFdCSEdvL1NkM295L0J3VmhrRXUrZUtVcUNyT0Q4cmdXZnJLYW1kMk5xeVlCaHhRMXAydVYwdkdYcGtvYUxacFNCUCtOcEVBbEtmemhobHVsdGI2NEZXMmx3VzByYmordFBCdVNsMWM0VkRia0dCb2kwZk5IMnBBY1J3cHdKQ0JYTjZ6bXpCbk1wOE5qNFgvTG5IdmVPaFdIQTlUZkNSZVVtWXZDaHpMQU1RK2RQTWVVWjRJdlJtUVFjZDlSbllsMllZUFhac1NqTjVDT1hxTGlMLzg5UjlETVJOcnBkNzd6TEZkTGJzUE5PUDBWZ1oxUGo2Mm54enJNT05CV2xGZStaeFp2UjRhK3JaKysxdDFPU2JnT3lhSDZlYkFiTkZ4aVp2dGpRSnM3NHAyVWlpZG13VnBxY0hKQzFKcDRmOXJvMGRWUDJuQkhaWlBpRVE0U3B5Ukc0TzJNRjQrMUhZbkw2UkljVUJpUEdOT1BCZTBUbG5mMUdXQTFDOHBNNWgiLCJtYWMiOiI1NWViZTMyNTAwODgyZDllOWMyZGM5MjMxMGZmZDMyYTllZWQxM2M0N2U2Y2RhZjgxNmVmOTQxNjYxMDBjOTZkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:14:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImlwVElVMUVoeWpkeXRtaGRmZ1RpNFE9PSIsInZhbHVlIjoiL2VLcEdkdFg2R2N3RDNyVjJQcnhnTHAyNmZPTW1Rc3ZjWmV4UnZsVERkcTJKRkRrUm5kYkdsdzNlMXZMSUNCM0thZUhQNXhlWFl2WmVqN2FJb2c4aXllditOZnBZdDliUFFhUEwvbzdrcG54Mys3aTh4MGJtQnIzNklVMmVpSlNKbm1YcXBxQVJjeGYrU3llK2p6WXBPRmpKemFEZ0JiZmhLamxxa1hYakJLMjM3S2NrdVNnV0RLamdWZzd6d3B6MTNCNGpHMXBjdmNBaXhLbnBlbmNsT2srczJxclVtRjViZWx2TkxHdklad01aaVRXdldLRm9EMGhkdWZFVndLRDJiR05wRmd6eG1Ucm56QWh5YTNNMCtwZkhEU3ljeFlGbXJIQUZORzNTVEo5VmIyOGNVN2RCT1pkc1pRTHhkajNESFMvQXh3UFQwVmJqM0tFMTVZdGc4aXZmcnEyejRXYzBqZFlZQXZUcHZGQllKdFdobW8ra1I5bkdTVUFBSm0xU2cveGRPWG1vTllUSXkxYmg5T1lTNFg0VHE4SU1MOXZ1VUZzN1pMNDhsMEFFdUhoQ0UyTW1mSWRVSnlQMEoydXRsZHliUVp2RUl3MDdzVm00Qm5lQ0E0R1cwTDFvRFFLMEtSNGN0UmU0V0pRd3JzeWs4N1o3WlRMUVR0cUI3ZzIiLCJtYWMiOiI5OTg1ZDA1NGJkNWRiM2VkMTBiNThmYmIxNzAyMGQ5NTZkNjBkOTgzZDgwNDZiMWRjZjdiNjExNjA2MmRmY2NlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:14:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1764138878\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1951916552 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1951916552\", {\"maxDepth\":0})</script>\n"}}