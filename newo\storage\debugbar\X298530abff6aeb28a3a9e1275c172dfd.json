{"__meta": {"id": "X298530abff6aeb28a3a9e1275c172dfd", "datetime": "2025-06-08 00:28:21", "utime": **********.559762, "method": "GET", "uri": "/add-to-cart/3/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749342500.626634, "end": **********.559807, "duration": 0.9331731796264648, "duration_str": "933ms", "measures": [{"label": "Booting", "start": 1749342500.626634, "relative_start": 0, "end": **********.359659, "relative_end": **********.359659, "duration": 0.733025074005127, "duration_str": "733ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.359675, "relative_start": 0.7330410480499268, "end": **********.559812, "relative_end": 5.0067901611328125e-06, "duration": 0.20013713836669922, "duration_str": "200ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53615336, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1322\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1322-1579</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.01433, "accumulated_duration_str": "14.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4384482, "duration": 0.00521, "duration_str": "5.21ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 36.357}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.463413, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 36.357, "width_percent": 10.258}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.497995, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 46.615, "width_percent": 14.027}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.503457, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 60.642, "width_percent": 14.166}, {"sql": "select * from `product_services` where `product_services`.`id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1326}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.513278, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1326", "source": "app/Http/Controllers/ProductServiceController.php:1326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1326", "ajax": false, "filename": "ProductServiceController.php", "line": "1326"}, "connection": "ty", "start_percent": 74.808, "width_percent": 6.769}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 3 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["3", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1330}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.523067, "duration": 0.00264, "duration_str": "2.64ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "ty", "start_percent": 81.577, "width_percent": 18.423}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-518162208 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-518162208\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.511742, "xdebug_link": null}]}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:1 [\n  3 => array:9 [\n    \"name\" => \"حمد\"\n    \"quantity\" => 1\n    \"price\" => \"10.00\"\n    \"id\" => \"3\"\n    \"tax\" => 0\n    \"subtotal\" => 10.0\n    \"originalquantity\" => 15\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/3/pos", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1523342478 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1834 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749341283326%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkczMzVacmZnNlgxYnpRWGNhNXJJRVE9PSIsInZhbHVlIjoiUGxyKzBQcFoydnY3dTdGNTJmZVdJajdaaURaOGt2QmV3cUkyemFOMWZnMGlLQmcwNndEMjN0L28vZG04KzQvYndGNEROZ1FSN2x5Q0tpNUdzdnNLVlJKNVhCZzRIcWJJSVZvSklqQlppcURURzdqd1FOaDk2a2dqWGFnUnZhYVhXeHRUZ1U4ZUF1eTk3RkwxSzA0Rm9WeXozRkRGMWpXYW9JOHE5aitpSHRiY0xHMlNLT1ZRYytYOWlVaDNqWnArakNtVFRaYTljaHZGSGNTbk01a1NaNmR3d3Z0aDhQZWQ4RHNvdWFjWWxGcGxiQzZJVTdzbWRQY2xkT09ndGl6Y0dpZlg2L1VRU0E2OFlJZzllWDFiMEx2SFZ2bEpKRU16OU9jZW53RFAvbEJLeHp0WDZDR2xwemNoOGNDNE4xcXZrTk9aMS80elVXRWZVQjVDejVCcTRtSk9vZUtNTFBiTGt0Yk16Tk84UURPV0w4d0JxQWYvQmViOGloTTBWbHBjQ2YxV0JoZE5xK25rYktiQUJOUCtvK0U0RkJjSHpjN0hScTFkelp0QU5BYzREeDF4ZVprRVQzc0ZLQXJoc2lBQmNmbFczeS9kbysyK29FZGh4Tjg2U0x5dGZNMGJwbTNSaHdrVks1ZVV0V3ZPZm05clp4RUhnWlRieFVubmZoWlYiLCJtYWMiOiI4YzFhM2Q3ZGI3OTQxNDdjMDI4OWIyZGIwMzYyOThlNWMzNmEyZjZmZWJhMGMwYzNjMDljY2MyNzA3NjAyNzNmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkhLeHg3RVgyNVhtVTNkWVBwMlM1Nnc9PSIsInZhbHVlIjoiZHVLazZEc1ZBWml5TDlJeHlpKzd2ZnZINDJ4anBZMWNxaUpCYm5CZ1cwNG55OUlsaGJkdnJIdkpWT1E5TE1UVUdmUG1XM3gwNGJuQnpPcFRFblo5dG1WaVYxU21mWnJFNHJJVFhtTVR0SFRvQTBvWmNCamtsc3VVWnZYTVNhbGVJUEtXMnM2RjdMeGdVbmFzTEtjamd1NzJFMzJsbUkrT25xSjIyRjA1dExnRjlFWUozZFBqcjhsa3pTWUpRS1BsREdVRENBZWg5NkFlSlVneFRFbVRzZkFkQjZXbXJTaUtoMzBQY2FyaWJINDYzOW5rVUdDNllVV2lEVGRReXc2NUJiY2JQRDMxK1Q2ZHFYYnBSaXVDTURTdW44OFBEcmp2N0ZVc1Vxb0FkK1pQbXZOdjVxOGRJTXdMTXNaRnpmK2M3eWFhT2dTZmN3NVVvVlhwc0VHaktQcWlpMkQyMlVIYVRCbzJLWjViYW1WNU9FanpLM09FeXdUcm1aKzJlaEx5SjRienBiR2duL0FwRUhoTVptMGNrNTJxZzJ6TFc2eDVJMk5kUnBoTDBzK2llTFZ1NU1lMml4cjFWb3h4VDF4bFFmY1dTUGYwTmhRVWtRTWljeEMyMjFkdmh5V0dtTWdnK0ZMMDRIU3pJZnVBUUVZRU80WDJZcUlpYjZiNVdtUTAiLCJtYWMiOiI1NjM0YWUzZjk5NTYzNmMyNGJiN2Y2MGY4OTFkNGVjZjZiNmZiNGRiMzgyMzM1MWFkNWYzMzNhOGU3ZjM3ZDJkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1523342478\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-429064452 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-429064452\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-115576915 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:28:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlMyM0pUdGxQTVhxZ3pYQjlaZkFlRFE9PSIsInZhbHVlIjoiOStjSWlrejZObjU3dkhMOUZkZUJ5Um52cVZmSmlIMGVsV2ZmNFowYWFPVnEvMy96aDlBay9EYUdKNEhzVmJWY0pYSjR1aGVVRVdzTlBnTllxU2hNZW9HZW1ZNzJoNnF4ejY4bnZGd2NDZ1lNMjNaTUNxTDZoMXRURFpqbXFYdU5HL2hEYXppNHJscXFvRkJHT1hDOVh2eUJTVm9pR1lQN2h4U2ZzdExEWlluOTdqclZMZzdFYzh4YzdPYy9SMCtVTGczYWRRbmo0dUgxTW9rMXY5WmpyalI5MlJweEtOb2ZNOWdxcW0wMHJwN295VjM3N1VYYVAveVIySkl4VzV1Vm53cnZFZERwK0lNSEUyL3RPaUppYkFhVkgySlE0dmpydmI3ZmZEZUllbDNOYkVCeVhjVTZtUEFQQXhKMG1nczVjTnBYQncwSm10d2RTUkxsZjBOTVcyTGFwYnFveERtbWcyek5qbzJ5UGRodGRzM21NU3o1SzhKY09VSUUvTy8rcitQNzJzMDBxckdaUE52dWRKRTZDQStGaC9GV2FBWFBHL005UVJTSmlFSlRSN1VmTTdIcHJhVzk5QkxMZ1czaVpoNjZHeGVtdTlzRDJoTHROeXFVbWFmcWIra2VZZ0NUMzU2TlJ1d3lMYUdIMXlIeG1IaXFHYzBVSE1CSngvbmgiLCJtYWMiOiJiYWYxZDM0ODg0MDIwMGM3ZmQ5OThhNjVlNDc4ZDFkZGQzYjIzMmQyNjI2YzMwZGJjMGVkYjBmYjFkNDQyZGY2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:28:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlpMQzlMNGNhdDZUUWoyWHZOeWs5WFE9PSIsInZhbHVlIjoiRjNwS2lCcEFDbVZsODBYOGMyNit2SHh5U0dTcDgrdlR4eDZ3Zm9reUVUTFVVMElFR1FRdm9FcU92SWg3TTIxVyt3VkJ6VXkvTFRHYVp4ZC90NG9VTFFxYXM3THJQUzB4aXdtNTl4Uy9nOEVsMDFSdDg2QWtMZGNJcDJsZy9WUzhLeVlnQUlhejFXRjd1OVNHYkh6TUJvN2ZvZkRPMG5UY1lLa1Y0bVQ5NmpMVkd5dmV2amEzbWxiOXFZNzA4eE5MZW9MY0V0T2lNRHBOWFZobE92YkIrMXdZdVdVeTRRMHNCNVdUazZqMHVVdHhxZ1I5TnZnS0F1MlVJcDJHRm5keXRUZzVWQ0xDb3l0ZG56cjlqYmZzakVzYjF6eGo3OVNOOUZWVmZpV3JHZ0E1RWZmS0lCVlRvYXAveHU4VDJqS2RFMTc3R1Q3UzVyTCtwUkt2QWh3QUhtejd1Z01JVCtRNllBYXF3REx6M245bXU4Yk14ME50Y3VMcXQ2QjcwU05iTVhmckVxdEpZdEIxblhjeW8xM2VMVkhzOFYxOGNqbThyS0t6ZmdBNVhqT1pZcFc0cFp5ajY3N1kwWTlTaCsxR3VVeE5nK2EyOTMxNnZUcHdtQ1Fua0xwOFZiT0Q3SzkwREZPZVFCT0JIVExlNjh2ZXVsVnh4WC9OT0dnMHlIeFMiLCJtYWMiOiIxNDg1MjU1MTQ5M2VhOGI3OTg4NzIxYTU0NmIzMzQ4MzNmMTE3ZTdmZjljMzM1NzZjN2E0MzJhMmRhYmQzZmJjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:28:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlMyM0pUdGxQTVhxZ3pYQjlaZkFlRFE9PSIsInZhbHVlIjoiOStjSWlrejZObjU3dkhMOUZkZUJ5Um52cVZmSmlIMGVsV2ZmNFowYWFPVnEvMy96aDlBay9EYUdKNEhzVmJWY0pYSjR1aGVVRVdzTlBnTllxU2hNZW9HZW1ZNzJoNnF4ejY4bnZGd2NDZ1lNMjNaTUNxTDZoMXRURFpqbXFYdU5HL2hEYXppNHJscXFvRkJHT1hDOVh2eUJTVm9pR1lQN2h4U2ZzdExEWlluOTdqclZMZzdFYzh4YzdPYy9SMCtVTGczYWRRbmo0dUgxTW9rMXY5WmpyalI5MlJweEtOb2ZNOWdxcW0wMHJwN295VjM3N1VYYVAveVIySkl4VzV1Vm53cnZFZERwK0lNSEUyL3RPaUppYkFhVkgySlE0dmpydmI3ZmZEZUllbDNOYkVCeVhjVTZtUEFQQXhKMG1nczVjTnBYQncwSm10d2RTUkxsZjBOTVcyTGFwYnFveERtbWcyek5qbzJ5UGRodGRzM21NU3o1SzhKY09VSUUvTy8rcitQNzJzMDBxckdaUE52dWRKRTZDQStGaC9GV2FBWFBHL005UVJTSmlFSlRSN1VmTTdIcHJhVzk5QkxMZ1czaVpoNjZHeGVtdTlzRDJoTHROeXFVbWFmcWIra2VZZ0NUMzU2TlJ1d3lMYUdIMXlIeG1IaXFHYzBVSE1CSngvbmgiLCJtYWMiOiJiYWYxZDM0ODg0MDIwMGM3ZmQ5OThhNjVlNDc4ZDFkZGQzYjIzMmQyNjI2YzMwZGJjMGVkYjBmYjFkNDQyZGY2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:28:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlpMQzlMNGNhdDZUUWoyWHZOeWs5WFE9PSIsInZhbHVlIjoiRjNwS2lCcEFDbVZsODBYOGMyNit2SHh5U0dTcDgrdlR4eDZ3Zm9reUVUTFVVMElFR1FRdm9FcU92SWg3TTIxVyt3VkJ6VXkvTFRHYVp4ZC90NG9VTFFxYXM3THJQUzB4aXdtNTl4Uy9nOEVsMDFSdDg2QWtMZGNJcDJsZy9WUzhLeVlnQUlhejFXRjd1OVNHYkh6TUJvN2ZvZkRPMG5UY1lLa1Y0bVQ5NmpMVkd5dmV2amEzbWxiOXFZNzA4eE5MZW9MY0V0T2lNRHBOWFZobE92YkIrMXdZdVdVeTRRMHNCNVdUazZqMHVVdHhxZ1I5TnZnS0F1MlVJcDJHRm5keXRUZzVWQ0xDb3l0ZG56cjlqYmZzakVzYjF6eGo3OVNOOUZWVmZpV3JHZ0E1RWZmS0lCVlRvYXAveHU4VDJqS2RFMTc3R1Q3UzVyTCtwUkt2QWh3QUhtejd1Z01JVCtRNllBYXF3REx6M245bXU4Yk14ME50Y3VMcXQ2QjcwU05iTVhmckVxdEpZdEIxblhjeW8xM2VMVkhzOFYxOGNqbThyS0t6ZmdBNVhqT1pZcFc0cFp5ajY3N1kwWTlTaCsxR3VVeE5nK2EyOTMxNnZUcHdtQ1Fua0xwOFZiT0Q3SzkwREZPZVFCT0JIVExlNjh2ZXVsVnh4WC9OT0dnMHlIeFMiLCJtYWMiOiIxNDg1MjU1MTQ5M2VhOGI3OTg4NzIxYTU0NmIzMzQ4MzNmMTE3ZTdmZjljMzM1NzZjN2E0MzJhMmRhYmQzZmJjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:28:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-115576915\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-86309961 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1581;&#1605;&#1583;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>3</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>15</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-86309961\", {\"maxDepth\":0})</script>\n"}}