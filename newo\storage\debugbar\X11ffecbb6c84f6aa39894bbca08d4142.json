{"__meta": {"id": "X11ffecbb6c84f6aa39894bbca08d4142", "datetime": "2025-06-08 01:14:13", "utime": **********.39099, "method": "GET", "uri": "/customer/check/delivery?customer_id=7", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749345252.756046, "end": **********.391012, "duration": 0.6349658966064453, "duration_str": "635ms", "measures": [{"label": "Booting", "start": 1749345252.756046, "relative_start": 0, "end": **********.326337, "relative_end": **********.326337, "duration": 0.5702910423278809, "duration_str": "570ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.326357, "relative_start": 0.5703108310699463, "end": **********.391015, "relative_end": 3.0994415283203125e-06, "duration": 0.06465816497802734, "duration_str": "64.66ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43925424, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0050100000000000006, "accumulated_duration_str": "5.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.369823, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 79.641}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.378767, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 79.641, "width_percent": 20.359}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-969908764 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-969908764\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-967082934 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-967082934\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-421895313 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-421895313\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-733430332 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1939 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i31pha%7C1749345246637%7C11%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im1iY0ZES3FwSlk4MS9FOVI1YVF1cXc9PSIsInZhbHVlIjoiaDZ5R2dESDBiVU8wY3d4RldmWEFzdXRUSGJ1UzI3NGlFMGZNKzByZFhQdmFJbTZ3cE0xdnZrVXduZFNvdVhWMUZjaWdFQ3daaWExT1ZCYzZGS0tUQ1Zabk5zOTY2Z0MvaHRMdnErQTNQdVdaUkhGanFwaVBBMWZrUDlITFZqRTVFMnNhUDJVb3AwMStXMThzcFdUTEZlVUYzN3RCcUlQL0JjYSt1V3pOYnVmOWZUR0JrSFZTODZiVHRLN282dmExRWh0YTVyVjJzQ0I1dnJjMVVWVGpoSk5rYmx4WkdlQ1hvcDNnMkREcXE5Z3lWaEliYjB1anVSbGRNK3h6TnlsN0doYmJJMXJyNWwzbUUzNUtha3lGTkw5bmc1QitCQzF6L0RtN1FHM3pWTThvbVRyN051anYzZy9OT0dVWmQ0YVJrSzNya0RhYWU3ZzZXR2dyZC8xalE1ZytkL2tUbERLc3BTUjdXV0VJcW1YL2pBcWdsZUlWb204U1VmcUhZSDZ3ajM3QXlSbVRTbk5aYlNCbmxsR3YzM2l6WkZTR1dYS2t3SkdrWTZ1NEt0enFTOHBZNjlvMVBTVDVuK3dDZXBjN2JqN09MMmxjVDd1dVlhWisrSG4zRFprbVRJeTVGVFF4SExwUGk3MHZSb29WWWlHR1RkQU1vR2srYXBVV3dBYWsiLCJtYWMiOiJjMzc5ZmFlZDYzYTFkMjkyOGQ2Mjk4ODllMjM5ZjRkNWIwNGIzN2NjNmEwNmY4Yzc4M2JkZmUwM2VlMzMzNjE5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImlDR21uS0xTNS9DZmVCei9kakdVM1E9PSIsInZhbHVlIjoidHZqNldsbEdKNzltWThLeWZ4RTRUaXJjb2lWT1dONEFqV2hiY2dGdEl3VGdVY2kyd0xMSU9YcDdEZWVCaEVST1hld2xBQlBxZ1ZlZzllVGgzMWFFUEZwN2p0S1lGU2tycXZ3V090c0t2QXo2TnhzaURubTFpa21MRVh6RHBnaFBpZEc3QmdBeDByMUU1WnU3aFNTaFhmSGFWU0dKQzhVQjlmQUJJOG9EVWJ0SERGNFBTcVVQSkczbEVQbElEYUFscnZZVk0vT2RpY0N5Z29rNytwbU9BSVhDRWd4UlVlKysybzFadlgrazlKZnQ3QWtvR25NNTNKTFpOTzBZZTF3ejBZYStHTUhEUWlwczQ2VllxMmxpV3ZaUDIrRUE3WEEwbXJyZjNOcDM0ZE9jaE5WOWpaTXliNHU2L3NpSVpWeUQ2dTFxc2VucXJ1ZjEyM2xlbGVtOHRxbXlTSXVBcVVmWmpGZ3NIQ0tvVkJUbEVRMmdVNXp3SGY0R3BjVW55akMzbWhNZnhBMi9iR0xPbnB1QW1DOUdBbm5oVzdHQWRkVXduaFg3eFZCVStrT1N5UzdEVmJwUUlQTkxCSldScVV4aWVKMndEZ3g1WHF1bkhHS1hLdU1xUlU4Z0hwUVJYN0ozRnpnL1NnbWQ0WEFIMGJON3M3M0dwcUEvcDBuaFZBTnYiLCJtYWMiOiI4ZTc0ZGZjY2IzNTdkYWY0Njk2OTA1MjdhMDM0OWIzZmMzMzczODZjOGM0YzgxZWM1NjVkN2M5OTQzNThhNWM5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-733430332\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-559382963 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-559382963\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 01:14:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjIwSW9YOU5yYTNmKzJFeDFDTERtS2c9PSIsInZhbHVlIjoiTlFxaStCNHVqODJSUnU1eHczR2IxbEhIdGNnSnlxVDVSWDBPNEZ1WmpyUEJpREFhS0ZhQlBzSWc5S1RGbUE3MFk1QlpVNHBsc0QwcjFUTHJIdTYxNG8ySFBneS9FbVdDNmFnS1BUK3hVemwya2ZReDZpeUFkSDN4N2pXQnhrU0hJZDZDZzRFcXVaM2c2dElJVFFEQWV3V2FVbjM4ZFUvR21xT1pUQlBObzVZaTRmWlZ2emhxc1ZzRkV4ekpJRVo1aEV5R3hKeGpEd09SOEVYRUdTcXVSa3JuZUlaVjU1K2k5eEhmRDlYV09HODRaMU55cWhFSWoxYTBrdlY0VEU0dGdZaDR3WWpqTTNyL2UvbHFOd3grbG9ObmhNRGFyNWMrbDVtTy8zVVZ2MTdMSXhUeXNqNTV4UHN0MkN6K2k1UUVKWW4yWDNkeEwweWkwUFppTGsxTkdhZ2FrV2R2bHJhQ05Xb3JhbEV0ekRVZzJIelA1a0hXOEsrSFpjL2JNcGFuaWRHZEZNNjZDQjR1Y1d4ZUxxa0ttT1lqOXRQL2tRTmVqK2szTFd2eU9VZHRXaXU5a3RaM29LTzcrNVR0K3J1R2Flczc1WHdYSXo0eldBVXJlMjFjN1BNMStpVllFSHdXaWh3SG9MMFZBNzNNd3phdDhhaEQ4OTVwcktoNEF2a2wiLCJtYWMiOiJiN2Y1M2M0OWRmYzM3ZmVlY2VhOGM5OTQ5YWRlYzQ1NjI2MTMzZTRiYWFkZGRhNzYyM2M1ZGVjNzY5YzFmMDAyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:14:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkF1NU91cUVIOW92QW92Vjh4MHE2SkE9PSIsInZhbHVlIjoiZzNXYTYxVkpDdFdyNUJsUm5zbS81Q2hYZU1SSVViTWJDYW1RdmFGcHdGRnhISmptQlhTZU81MmZIWE8rbkkzM0hMWUtXTVlXTEltK1oyNEtZUGZhb0J5c21RdUttdG5PVFp2M2VUZHR2aUV0amlNMXNxN256TDdQOENESSsyVXBNL2dzSktSTm8yUGMwcWU2aWRZc3Fra3l5cU9IUTdBWnMybWtGMHNkMUlrOGp2MkNwSW05TUFzZHozUnlDeVQ2VzVaMHJ1R2xvdWg0RStWa0psREJ3dWUzeWE5YUFNdklpUTF3bFBEYS9MRU1pdWFYTnhrRmtQL00vaGM3TTF4Tm43VlNUZ3JmRzgzQ3NhRjE3RXFwVWdLck1odmU2VmlldGdiT2ZudGpWMDR0TitrSTVNNWxPR21Bb3Y5Rnk2WEoxaHl3bWI0WDJFKzIyWTFGR2VPSFZFandmQkNTRW9ld0tWL295RG5NWTg3UzBIMStFUzdKY0tVelNJRzUxais4WC9RYjJoRlZpOE85a0liTjFRMkh5YWhuVEVXdDlmSTBBQWhHSkxwdlRReWkzY1hxVUpZaHUrZkx3TCtvVHRFbk9DSTE5RWU0TWF4R0lVSVg3NC9lenpwOU55MWMveURMNDRhYmRrTURJcnlnNXhWWjQ1Wm5tL2g1NVJKbzdqOE4iLCJtYWMiOiIzYTAxMDhhM2Q4YWEyNGYyNGFlNTg0YTM1NWQ5ZGYzNDFjNDY4NWFkNjJjNzRiODBhYmJkN2ZmZjc0NTg5NzRlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:14:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjIwSW9YOU5yYTNmKzJFeDFDTERtS2c9PSIsInZhbHVlIjoiTlFxaStCNHVqODJSUnU1eHczR2IxbEhIdGNnSnlxVDVSWDBPNEZ1WmpyUEJpREFhS0ZhQlBzSWc5S1RGbUE3MFk1QlpVNHBsc0QwcjFUTHJIdTYxNG8ySFBneS9FbVdDNmFnS1BUK3hVemwya2ZReDZpeUFkSDN4N2pXQnhrU0hJZDZDZzRFcXVaM2c2dElJVFFEQWV3V2FVbjM4ZFUvR21xT1pUQlBObzVZaTRmWlZ2emhxc1ZzRkV4ekpJRVo1aEV5R3hKeGpEd09SOEVYRUdTcXVSa3JuZUlaVjU1K2k5eEhmRDlYV09HODRaMU55cWhFSWoxYTBrdlY0VEU0dGdZaDR3WWpqTTNyL2UvbHFOd3grbG9ObmhNRGFyNWMrbDVtTy8zVVZ2MTdMSXhUeXNqNTV4UHN0MkN6K2k1UUVKWW4yWDNkeEwweWkwUFppTGsxTkdhZ2FrV2R2bHJhQ05Xb3JhbEV0ekRVZzJIelA1a0hXOEsrSFpjL2JNcGFuaWRHZEZNNjZDQjR1Y1d4ZUxxa0ttT1lqOXRQL2tRTmVqK2szTFd2eU9VZHRXaXU5a3RaM29LTzcrNVR0K3J1R2Flczc1WHdYSXo0eldBVXJlMjFjN1BNMStpVllFSHdXaWh3SG9MMFZBNzNNd3phdDhhaEQ4OTVwcktoNEF2a2wiLCJtYWMiOiJiN2Y1M2M0OWRmYzM3ZmVlY2VhOGM5OTQ5YWRlYzQ1NjI2MTMzZTRiYWFkZGRhNzYyM2M1ZGVjNzY5YzFmMDAyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:14:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkF1NU91cUVIOW92QW92Vjh4MHE2SkE9PSIsInZhbHVlIjoiZzNXYTYxVkpDdFdyNUJsUm5zbS81Q2hYZU1SSVViTWJDYW1RdmFGcHdGRnhISmptQlhTZU81MmZIWE8rbkkzM0hMWUtXTVlXTEltK1oyNEtZUGZhb0J5c21RdUttdG5PVFp2M2VUZHR2aUV0amlNMXNxN256TDdQOENESSsyVXBNL2dzSktSTm8yUGMwcWU2aWRZc3Fra3l5cU9IUTdBWnMybWtGMHNkMUlrOGp2MkNwSW05TUFzZHozUnlDeVQ2VzVaMHJ1R2xvdWg0RStWa0psREJ3dWUzeWE5YUFNdklpUTF3bFBEYS9MRU1pdWFYTnhrRmtQL00vaGM3TTF4Tm43VlNUZ3JmRzgzQ3NhRjE3RXFwVWdLck1odmU2VmlldGdiT2ZudGpWMDR0TitrSTVNNWxPR21Bb3Y5Rnk2WEoxaHl3bWI0WDJFKzIyWTFGR2VPSFZFandmQkNTRW9ld0tWL295RG5NWTg3UzBIMStFUzdKY0tVelNJRzUxais4WC9RYjJoRlZpOE85a0liTjFRMkh5YWhuVEVXdDlmSTBBQWhHSkxwdlRReWkzY1hxVUpZaHUrZkx3TCtvVHRFbk9DSTE5RWU0TWF4R0lVSVg3NC9lenpwOU55MWMveURMNDRhYmRrTURJcnlnNXhWWjQ1Wm5tL2g1NVJKbzdqOE4iLCJtYWMiOiIzYTAxMDhhM2Q4YWEyNGYyNGFlNTg0YTM1NWQ5ZGYzNDFjNDY4NWFkNjJjNzRiODBhYmJkN2ZmZjc0NTg5NzRlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:14:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}