<?php

require_once 'vendor/autoload.php';

use Spatie\Permission\Models\Role;
use Illuminate\Foundation\Application;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== جميع الأدوار الموجودة ===\n";

$roles = Role::all();
foreach ($roles as $role) {
    echo "- {$role->name} (ID: {$role->id}, Created by: {$role->created_by})\n";
}

echo "\n=== البحث عن أدوار Delivery ===\n";

$deliveryRoles = Role::where('name', 'like', '%delivery%')->orWhere('name', 'like', '%Delivery%')->get();
foreach ($deliveryRoles as $role) {
    echo "- {$role->name} (ID: {$role->id}, Created by: {$role->created_by})\n";
}

echo "\n=== انتهى الفحص ===\n";
