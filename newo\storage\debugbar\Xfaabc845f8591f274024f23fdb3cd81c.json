{"__meta": {"id": "Xfaabc845f8591f274024f23fdb3cd81c", "datetime": "2025-06-08 01:14:15", "utime": **********.025683, "method": "GET", "uri": "/add-to-cart/5/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.400369, "end": **********.025706, "duration": 0.6253371238708496, "duration_str": "625ms", "measures": [{"label": "Booting", "start": **********.400369, "relative_start": 0, "end": **********.892557, "relative_end": **********.892557, "duration": 0.4921879768371582, "duration_str": "492ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.892569, "relative_start": 0.4922001361846924, "end": **********.025709, "relative_end": 2.86102294921875e-06, "duration": 0.13313984870910645, "duration_str": "133ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53622264, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1322\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1322-1579</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00711, "accumulated_duration_str": "7.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9443238, "duration": 0.00272, "duration_str": "2.72ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 38.256}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.959771, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 38.256, "width_percent": 12.799}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.98411, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 51.055, "width_percent": 13.783}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.988013, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 64.838, "width_percent": 14.205}, {"sql": "select * from `product_services` where `product_services`.`id` = '5' limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1326}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.996017, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1326", "source": "app/Http/Controllers/ProductServiceController.php:1326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1326", "ajax": false, "filename": "ProductServiceController.php", "line": "1326"}, "connection": "ty", "start_percent": 79.044, "width_percent": 10.127}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 5 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["5", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1330}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.003341, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "ty", "start_percent": 89.17, "width_percent": 10.83}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-77977233 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-77977233\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.994566, "xdebug_link": null}]}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:1 [\n  5 => array:9 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"id\" => \"5\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"originalquantity\" => 22\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/5/pos", "status_code": "<pre class=sf-dump id=sf-dump-1527417740 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1527417740\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1007180134 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1007180134\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1049860862 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1049860862\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1160219417 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1939 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i31pha%7C1749345246637%7C11%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjIwSW9YOU5yYTNmKzJFeDFDTERtS2c9PSIsInZhbHVlIjoiTlFxaStCNHVqODJSUnU1eHczR2IxbEhIdGNnSnlxVDVSWDBPNEZ1WmpyUEJpREFhS0ZhQlBzSWc5S1RGbUE3MFk1QlpVNHBsc0QwcjFUTHJIdTYxNG8ySFBneS9FbVdDNmFnS1BUK3hVemwya2ZReDZpeUFkSDN4N2pXQnhrU0hJZDZDZzRFcXVaM2c2dElJVFFEQWV3V2FVbjM4ZFUvR21xT1pUQlBObzVZaTRmWlZ2emhxc1ZzRkV4ekpJRVo1aEV5R3hKeGpEd09SOEVYRUdTcXVSa3JuZUlaVjU1K2k5eEhmRDlYV09HODRaMU55cWhFSWoxYTBrdlY0VEU0dGdZaDR3WWpqTTNyL2UvbHFOd3grbG9ObmhNRGFyNWMrbDVtTy8zVVZ2MTdMSXhUeXNqNTV4UHN0MkN6K2k1UUVKWW4yWDNkeEwweWkwUFppTGsxTkdhZ2FrV2R2bHJhQ05Xb3JhbEV0ekRVZzJIelA1a0hXOEsrSFpjL2JNcGFuaWRHZEZNNjZDQjR1Y1d4ZUxxa0ttT1lqOXRQL2tRTmVqK2szTFd2eU9VZHRXaXU5a3RaM29LTzcrNVR0K3J1R2Flczc1WHdYSXo0eldBVXJlMjFjN1BNMStpVllFSHdXaWh3SG9MMFZBNzNNd3phdDhhaEQ4OTVwcktoNEF2a2wiLCJtYWMiOiJiN2Y1M2M0OWRmYzM3ZmVlY2VhOGM5OTQ5YWRlYzQ1NjI2MTMzZTRiYWFkZGRhNzYyM2M1ZGVjNzY5YzFmMDAyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkF1NU91cUVIOW92QW92Vjh4MHE2SkE9PSIsInZhbHVlIjoiZzNXYTYxVkpDdFdyNUJsUm5zbS81Q2hYZU1SSVViTWJDYW1RdmFGcHdGRnhISmptQlhTZU81MmZIWE8rbkkzM0hMWUtXTVlXTEltK1oyNEtZUGZhb0J5c21RdUttdG5PVFp2M2VUZHR2aUV0amlNMXNxN256TDdQOENESSsyVXBNL2dzSktSTm8yUGMwcWU2aWRZc3Fra3l5cU9IUTdBWnMybWtGMHNkMUlrOGp2MkNwSW05TUFzZHozUnlDeVQ2VzVaMHJ1R2xvdWg0RStWa0psREJ3dWUzeWE5YUFNdklpUTF3bFBEYS9MRU1pdWFYTnhrRmtQL00vaGM3TTF4Tm43VlNUZ3JmRzgzQ3NhRjE3RXFwVWdLck1odmU2VmlldGdiT2ZudGpWMDR0TitrSTVNNWxPR21Bb3Y5Rnk2WEoxaHl3bWI0WDJFKzIyWTFGR2VPSFZFandmQkNTRW9ld0tWL295RG5NWTg3UzBIMStFUzdKY0tVelNJRzUxais4WC9RYjJoRlZpOE85a0liTjFRMkh5YWhuVEVXdDlmSTBBQWhHSkxwdlRReWkzY1hxVUpZaHUrZkx3TCtvVHRFbk9DSTE5RWU0TWF4R0lVSVg3NC9lenpwOU55MWMveURMNDRhYmRrTURJcnlnNXhWWjQ1Wm5tL2g1NVJKbzdqOE4iLCJtYWMiOiIzYTAxMDhhM2Q4YWEyNGYyNGFlNTg0YTM1NWQ5ZGYzNDFjNDY4NWFkNjJjNzRiODBhYmJkN2ZmZjc0NTg5NzRlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1160219417\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-486458534 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 01:14:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InNwNDY4OGx3eFZ2NGl2R3ZUQkMvSlE9PSIsInZhbHVlIjoiVTNZazdhQ1ZpeW94M0JNVEdRK3lFUGFUcUJ2UTJ2MnNJWmlJSlhPNFFyMWUvdmx0MExDaUhhS2JhUWQ2QVV1K2g3ZExSWW1RYmFRSFRWemxZR3NkdU52RFBCTGdrSTV1SWpaNGFKbnV4ODBjdVZyVmtSYmdQNzBFam8zdkdxYkFTN0VyZmpTTTFiNmNLbURwNFc0WENQVE03YjRsUmJpeXRtdWd0SENhRHVwYU9sUTVCaUFRTXZnQ1VnU0dwb3A4V0Nvc0l1V3NpTjJRTlo5V0RIYmIyRkQ5OStySDBvSTZVbFR4RHp1NkRaZXRJNURtS1FrUHM0UVRGLysrelhpWkJOejB0N0hUQ2xURDluTlRva3dXeDdsb2FmRGFEaU1RVW1WL29RSktPcmV2b0hUTXpPbHlyODBPRGllRFZXRXBqdnpheXhGWHNSd2Y1QlNTMWJlNWhGNzJOc1pQWU1SZ3ZaZnNteDg5WUFLbm85aHZ2bHdhZnV6L2tFdloyK0NYTlNiNXRZWjIrdXZrOHF1OFlsR2lFWXlDSWdNTlVCcXFiYWdlRUkwMXlyeDg4YlRPSndKeFdGUE5sWlFRbXgvUjNsTDlkdnRkTk4wODdCdWFhdWVVMUNwdW1ITkw2ckdOYmlUTTdJQTZyYlJYRFlKQ3NHNm9CY0lHeXFhYzl2SVEiLCJtYWMiOiJhNTczZjZmYWY0ODM2ZDdmMWFmM2IzNGRlMTUzZmYwMjYzMWViMDZmMTcxMTJkYzYyNjRmYWM3YWU3N2I2OGIzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:14:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImgyeGxyMlZJSUJVdzBwRzRYbE1TMlE9PSIsInZhbHVlIjoiRnNpS2xneS9RTTVEblJkamp0ZHlPVE9GSDdqSHVjbzlvdVFJeTJRcTU0bFNlWXpKTEdBNlk5RWxTb0hBRE0zbWhZYVB3OFZqa0Q1Nmh5bk4vS09NNllOMlg1czZHcmpsTjBNRk5ENGNmTUltWHFqOWZGd3hLNnE1VWQ4UEt5b0NpSUFNOEl5NTNhZlhSRHBtWTBteHIzdWYvM0NMWld4bmlMck4vT0k1YUVpaXdsU1liM0FlQVpTU0h4OUwrWDZ4U09xZDFqakZqRVgyV2NxNkNHa2dWaHFpTnlPYThCT3BTRC9pSUtjaG5Fc1l2Z0ZENStST1RDSVJIemp5NnFqYlkxZEdmSTdHckE5ZWlXLzZKUWRVUTJYMDFJaForeXlSYWRIaG5ra3VQWXdNaXNMSUJrRUFzSk1pajV3YW45WmVwSnR2VlpKWTdLYUU2dGFNREJlOU1iWUpQQVZ2NzlaU2tiaXlERHR3VmxrcTNraUhhWnk2Q2dSSGtINTdraEVoSUEwOUgycko2RXBTb0pJdzN2ZHhRL1JlWlAwb29kb1I5USs3eGc2ZkZmb0NzVmdES0hxM1B4QnkwL1FCcjhQWTF4UmtKN3lNZDdZdWwzYVZpV1FJZmpBUEVWUVhIWVlmSnZZV3Q3UjdlUVZFdlFmc0UyRnlGdlJkcjhQaENLUGIiLCJtYWMiOiJjNWVlMmExM2UzYTg0M2EyOTA2NDViMjFlM2ZjNDI1MmU0YTNjMDBiMjNkYzAxMzQ0MTY5NmYzZmY5ZGI3NjM4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:14:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InNwNDY4OGx3eFZ2NGl2R3ZUQkMvSlE9PSIsInZhbHVlIjoiVTNZazdhQ1ZpeW94M0JNVEdRK3lFUGFUcUJ2UTJ2MnNJWmlJSlhPNFFyMWUvdmx0MExDaUhhS2JhUWQ2QVV1K2g3ZExSWW1RYmFRSFRWemxZR3NkdU52RFBCTGdrSTV1SWpaNGFKbnV4ODBjdVZyVmtSYmdQNzBFam8zdkdxYkFTN0VyZmpTTTFiNmNLbURwNFc0WENQVE03YjRsUmJpeXRtdWd0SENhRHVwYU9sUTVCaUFRTXZnQ1VnU0dwb3A4V0Nvc0l1V3NpTjJRTlo5V0RIYmIyRkQ5OStySDBvSTZVbFR4RHp1NkRaZXRJNURtS1FrUHM0UVRGLysrelhpWkJOejB0N0hUQ2xURDluTlRva3dXeDdsb2FmRGFEaU1RVW1WL29RSktPcmV2b0hUTXpPbHlyODBPRGllRFZXRXBqdnpheXhGWHNSd2Y1QlNTMWJlNWhGNzJOc1pQWU1SZ3ZaZnNteDg5WUFLbm85aHZ2bHdhZnV6L2tFdloyK0NYTlNiNXRZWjIrdXZrOHF1OFlsR2lFWXlDSWdNTlVCcXFiYWdlRUkwMXlyeDg4YlRPSndKeFdGUE5sWlFRbXgvUjNsTDlkdnRkTk4wODdCdWFhdWVVMUNwdW1ITkw2ckdOYmlUTTdJQTZyYlJYRFlKQ3NHNm9CY0lHeXFhYzl2SVEiLCJtYWMiOiJhNTczZjZmYWY0ODM2ZDdmMWFmM2IzNGRlMTUzZmYwMjYzMWViMDZmMTcxMTJkYzYyNjRmYWM3YWU3N2I2OGIzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:14:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImgyeGxyMlZJSUJVdzBwRzRYbE1TMlE9PSIsInZhbHVlIjoiRnNpS2xneS9RTTVEblJkamp0ZHlPVE9GSDdqSHVjbzlvdVFJeTJRcTU0bFNlWXpKTEdBNlk5RWxTb0hBRE0zbWhZYVB3OFZqa0Q1Nmh5bk4vS09NNllOMlg1czZHcmpsTjBNRk5ENGNmTUltWHFqOWZGd3hLNnE1VWQ4UEt5b0NpSUFNOEl5NTNhZlhSRHBtWTBteHIzdWYvM0NMWld4bmlMck4vT0k1YUVpaXdsU1liM0FlQVpTU0h4OUwrWDZ4U09xZDFqakZqRVgyV2NxNkNHa2dWaHFpTnlPYThCT3BTRC9pSUtjaG5Fc1l2Z0ZENStST1RDSVJIemp5NnFqYlkxZEdmSTdHckE5ZWlXLzZKUWRVUTJYMDFJaForeXlSYWRIaG5ra3VQWXdNaXNMSUJrRUFzSk1pajV3YW45WmVwSnR2VlpKWTdLYUU2dGFNREJlOU1iWUpQQVZ2NzlaU2tiaXlERHR3VmxrcTNraUhhWnk2Q2dSSGtINTdraEVoSUEwOUgycko2RXBTb0pJdzN2ZHhRL1JlWlAwb29kb1I5USs3eGc2ZkZmb0NzVmdES0hxM1B4QnkwL1FCcjhQWTF4UmtKN3lNZDdZdWwzYVZpV1FJZmpBUEVWUVhIWVlmSnZZV3Q3UjdlUVZFdlFmc0UyRnlGdlJkcjhQaENLUGIiLCJtYWMiOiJjNWVlMmExM2UzYTg0M2EyOTA2NDViMjFlM2ZjNDI1MmU0YTNjMDBiMjNkYzAxMzQ0MTY5NmYzZmY5ZGI3NjM4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:14:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-486458534\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>22</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}