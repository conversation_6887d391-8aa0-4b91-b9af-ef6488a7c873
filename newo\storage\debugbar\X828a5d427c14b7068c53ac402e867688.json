{"__meta": {"id": "X828a5d427c14b7068c53ac402e867688", "datetime": "2025-06-08 01:05:14", "utime": **********.128747, "method": "GET", "uri": "/financial-operations/sales-analytics/realtime-dashboard?warehouse_id=&date=2025-06-01", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749344713.365751, "end": **********.128771, "duration": 0.7630200386047363, "duration_str": "763ms", "measures": [{"label": "Booting", "start": 1749344713.365751, "relative_start": 0, "end": **********.008795, "relative_end": **********.008795, "duration": 0.6430439949035645, "duration_str": "643ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.008809, "relative_start": 0.6430580615997314, "end": **********.128773, "relative_end": 1.9073486328125e-06, "duration": 0.1199638843536377, "duration_str": "120ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46124584, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/sales-analytics/realtime-dashboard", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\SalesAnalyticsController@getRealtimeDashboard", "namespace": null, "prefix": "", "where": [], "as": "financial.sales.analytics.realtime", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=77\" onclick=\"\">app/Http/Controllers/SalesAnalyticsController.php:77-354</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02008, "accumulated_duration_str": "20.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0607228, "duration": 0.0032, "duration_str": "3.2ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 15.936}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.081172, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 15.936, "width_percent": 3.187}, {"sql": "select count(*) as aggregate from `pos` where `created_by` = 15 and date(`pos_date`) = '2025-06-01'", "type": "query", "params": [], "bindings": ["15", "2025-06-01"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 103}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.085877, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:103", "source": "app/Http/Controllers/SalesAnalyticsController.php:103", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=103", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "103"}, "connection": "ty", "start_percent": 19.124, "width_percent": 7.122}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'ty' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 125}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.090932, "duration": 0.006860000000000001, "duration_str": "6.86ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:125", "source": "app/Http/Controllers/SalesAnalyticsController.php:125", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=125", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "125"}, "connection": "ty", "start_percent": 26.245, "width_percent": 34.163}, {"sql": "select count(*) as aggregate from `pos_v2` where `created_by` = 15 and date(`pos_date`) = '2025-06-01'", "type": "query", "params": [], "bindings": ["15", "2025-06-01"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 133}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1030579, "duration": 0.0074199999999999995, "duration_str": "7.42ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:133", "source": "app/Http/Controllers/SalesAnalyticsController.php:133", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=133", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "133"}, "connection": "ty", "start_percent": 60.408, "width_percent": 36.952}, {"sql": "select count(*) as aggregate from `pos` where `created_by` = 15 and date(`pos_date`) = '2025-06-01' and HOUR(created_at) = '01'", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "01"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 167}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.113264, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:167", "source": "app/Http/Controllers/SalesAnalyticsController.php:167", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=167", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "167"}, "connection": "ty", "start_percent": 97.361, "width_percent": 2.639}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/sales-analytics\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/sales-analytics/realtime-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-915360590 data-indent-pad=\"  \"><span class=sf-dump-num>500</span>\n</pre><script>Sfdump(\"sf-dump-915360590\", {\"maxDepth\":0})</script>\n", "status_text": "Internal Server Error", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1943782207 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1943782207\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1999176854 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1999176854\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-688065999 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749344699171%7C48%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjE0RTlSZDkvU2krZVA1T3lQS0VQK2c9PSIsInZhbHVlIjoiNGl3dmJ2K21MWC9TZjNTVS8zdEpURHRaSWprVitybnlvMGp2RnVPRkQxckZGQ2NpMXkzR0N3V05WQkFBUWxWSXg5a2I2ejlxWmlrdDRTTStNV3JUb2d3dUI2b1RKOTJGLzhHN3FxbkVSR3NuTW1QaExGRUY2MU55Rk5UM3dwMHRrQTNodVZWU1RNcm1HT2hZZnlJaGw3M2Fsd2FXWUVuWUpMakJuVXRKcGFSdEVVSEQ2K1dYWTBJcTVlU3dFbjh3QnMyRXBSYVZpVlRUQkpxVlVjcnQyWjFIT0RGM3F2aVZtQWJGZTZKNmZwb2l0elNIbFpTZTZVNmVMWlJKZ25aTUQzdzQ1Z0crRFRwN0xrWlA3QjM5dWs1TVNtRldvL0NpdzliSk5NOU1VWUV2elpuUmM1clIyeXRWRW4zODA0RVlzUFF1TExWWkU4RWZxQVo4OXA4cU9tanJWVnJoenp2TXNyaVlYSXVOaHRoRnI2UjRKdllYZkMxdStBUUlrckU1K0FoOUg1ai9mQ0hSWGd6Z01seHBzN1N3WmpuSS80eTZ1b2lqak5BSGErRCtpeGR5a2RTYS9vbklHUkovMUpWa0xwcnpoMVErRDZid2lmcVpRWGJ4SVo3Tmc0ekNoSjE0UWFmNS9mYzlYSGZrc2Z5c0J1Z053UVkvZ2QrK000TzkiLCJtYWMiOiI4MTFjOGQyN2VmZGU0ZDVjYWY4ZmNhZGNlYjNiZWIxYjdlMDQxNjMwNDE2YjdkYWM1NTdiNDBjNWNmZDQ5OTBlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InYvYWFFemw0ZlE0UTZtclRqUlZuVkE9PSIsInZhbHVlIjoiSDZaV1FYNkhjZ3g3SGRHL0tEeU9BdXY0U3RwdDViRm42ZWxpZlVuTGNhbHhyc01KVVNJS2ozK1FDMVVZMnl2Sm5EbXVVSHZ4a0JCM3dZOEt6aVFCNzdmbkwvLzFPREc4VUVuTzdtK1JXaDRjN2FsWjBRZzlJSGlBQ0xrajRGdXlnencrODRjS3Y3S3crVUtoSmxxNnFmQkd1dVc1eFdjM04reks5RlZnNjRMalg0SGZCWU5CVVNDcnJpTStnSUMvR3VUSGpEM3BxeEpNRnAzZG00Q2JlMjVUck5jQ3E2SmNOYmxiT1QvRm9KWGJRZk5BeUZtcTQwaU9YSmdUVVRDdUFYenhMMExIUkhwYzRWZTU5NmtKMUpUNnQydWV0elozTlpoV2tMTmFwdkd0ZGx4WmJ1SGc2ZXpqRDBOYytKeXl0RHh5SFZRamZGY3MwNWlaazFkQTFBUnU1RE9CY1JlL3A5VFIzMEhJVTFzMnFpVGhlSWlQOFJYWXA0SXIvSzdSVHlpVzNEQlZ0ZllGb2xWazhyMURHWTlsZ3dHZGxQQUxacUdBVWFvTVV4SmhBTVdaN3EvTGNzNEhhVlFmZ0RSQXhBTDZqeVJqSWUxa2Q3S0tnanovUFZJTzQyQkI0OXYwbVRvK20xay9uMytWRTRPSUhPQ3k3ZWlkRzZ6UEF0M24iLCJtYWMiOiIwNzA5MWEzODVhNmY0MDI5MDdmYjVjOTJiZWE2M2JhNTY5Yzg3ZTViM2Y0NTFjYjY3ZWMxZTA1NjBkMjllMTQxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-688065999\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-440856015 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-440856015\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-486310781 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 01:05:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkI0a1RvZW13NVdjdFJkNDhFNmRrcXc9PSIsInZhbHVlIjoicnVhN1RwZkJmdWNsT3V5LzIyT0tidGxEcDNaWC9mMGlhWmRwbEEzaEs0WFo4bUVRcnl0ZFQ1YW9aMjAzd0dCbEtqeXEvRUppWUdtWjMyQm4xMU1KdjF0ZEQwNnBqbnJVdGZiNDRZNUR5dzBqc1lzNWNDQktmbytpRWdaQUNGUlYvRkV0Mm1vWXJkT0dreVZ1d05neEE5UDZ0L3RUMFJrS0FxaUF4dndrcVAxKytrZ2haTkNnaU1FQythZU9GYVh6TnBodXVyNFdhakdHNklQbnRJbFI5RUpYclNlTnhGSzl4SkV0SlhvU0tuYzNZRno2MkhLSVh0LytqQ0w5SEhPeVlvOENMZUZDanRsdlJNcVMxdkt3R0M2NmN0NFVkUTlGSTA4YVJoMTJrSlc3cGU1L1YxWmk2M05MRjg4d3NUekZ5NHo4YThCQi93ZkZFUDcrVjJOWWp1Y1N3OVVEcEZ5SE5QaGY0NE1kWDZwT2ovZDRhblBkZkFHR0trdmxhekZ5UktFTVBRanArSkVYcHVWa3ZJMnJQcmJiWjU5Z0lNOUx4TEVxQThOSkR2a1pDNWljZ0l0Y1dUdmplQU9QYUZmVjBjMEZoVjNxdW1DYTJ1QVV5K2R1VVRWZEYwMElkL1VEaXY1bTVrS2cyaDZQV29xZUpOWUpFWm8wbm9lbUdBWksiLCJtYWMiOiI1MDE2N2Y5NzYxNjFkZTJiMDkxODYyOTJhYTcwYWY0MjFkOTZhNjcwMzI0OTc1OTYyM2QxZTE5ZjI4Y2M0ZWNmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:05:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImQrd3hxSE85N1VKZEVBOURETk5sMnc9PSIsInZhbHVlIjoielJBbHFWMU52MUY3WVVmUnhON0NRL0dqeCtJbnJJZWdOaXZsbC8wbEQ4T3F3SlhPL1VSQ25WcnEzSnVsRGMyY1dZZmVSTGQ2bk05V3l4WkQ5VWs1RHVuQU1RbndPWUoyNXhNRVFCL0toMW0yQ1RON3d5LzlTWDJScmwrZGZJcng5djJUWTFqSXF4bHZvb2VuR0pWSE50eForSnVLK3dZNlpxMkVFV1QxQWkwSHZwY0EzblMzQWZScXNiaTl6d0hPOTNkRC9PODRha1Y1d3BCRTBrRHNlMC9DUVc1SVd4M0NVNkg5elVMWkEvWlN4ZE5nZUZKeDUrS0FXOGd2NWJBNitMUjB4YzB2S2RaRUxlaUVWaWV5N3hzR0g1MDFXWkEwRk1uUThoWFg4dFNpNGhCN0pXandpUHdnNlVna21TcEJUdDhsaXRWN1FTcTZveWw1cnpDMEhuUlFpMllyb2FZZUVSWEVNUk9vQnczekh3Y2J1Z0Ywem9IWnVOaWREQUd4VjRPVGVnV3o3cEhlRnY3c0J1bllmcmszWUxtL3dVZ1R1UzBzQkZNcVE1SW00c29FY0plc2tNdzZHZDVpVzdNTWJid0hCMmVGdkNqNXMxbXdJTHh6eDdubFhVdDlPMzlSMWdENkRtbEYzVldjd2wwNTA4RXRjTXhtRS8ycVYvaGUiLCJtYWMiOiIwZTc5YzE5ZmY5MzlhODYyYTc4ZTdlNjgzOTNmNDNhYWYxNGM4YTA2ZTUxMmE4OTE4YzQ3YTYwZTIxOTk2NjkyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:05:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkI0a1RvZW13NVdjdFJkNDhFNmRrcXc9PSIsInZhbHVlIjoicnVhN1RwZkJmdWNsT3V5LzIyT0tidGxEcDNaWC9mMGlhWmRwbEEzaEs0WFo4bUVRcnl0ZFQ1YW9aMjAzd0dCbEtqeXEvRUppWUdtWjMyQm4xMU1KdjF0ZEQwNnBqbnJVdGZiNDRZNUR5dzBqc1lzNWNDQktmbytpRWdaQUNGUlYvRkV0Mm1vWXJkT0dreVZ1d05neEE5UDZ0L3RUMFJrS0FxaUF4dndrcVAxKytrZ2haTkNnaU1FQythZU9GYVh6TnBodXVyNFdhakdHNklQbnRJbFI5RUpYclNlTnhGSzl4SkV0SlhvU0tuYzNZRno2MkhLSVh0LytqQ0w5SEhPeVlvOENMZUZDanRsdlJNcVMxdkt3R0M2NmN0NFVkUTlGSTA4YVJoMTJrSlc3cGU1L1YxWmk2M05MRjg4d3NUekZ5NHo4YThCQi93ZkZFUDcrVjJOWWp1Y1N3OVVEcEZ5SE5QaGY0NE1kWDZwT2ovZDRhblBkZkFHR0trdmxhekZ5UktFTVBRanArSkVYcHVWa3ZJMnJQcmJiWjU5Z0lNOUx4TEVxQThOSkR2a1pDNWljZ0l0Y1dUdmplQU9QYUZmVjBjMEZoVjNxdW1DYTJ1QVV5K2R1VVRWZEYwMElkL1VEaXY1bTVrS2cyaDZQV29xZUpOWUpFWm8wbm9lbUdBWksiLCJtYWMiOiI1MDE2N2Y5NzYxNjFkZTJiMDkxODYyOTJhYTcwYWY0MjFkOTZhNjcwMzI0OTc1OTYyM2QxZTE5ZjI4Y2M0ZWNmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:05:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImQrd3hxSE85N1VKZEVBOURETk5sMnc9PSIsInZhbHVlIjoielJBbHFWMU52MUY3WVVmUnhON0NRL0dqeCtJbnJJZWdOaXZsbC8wbEQ4T3F3SlhPL1VSQ25WcnEzSnVsRGMyY1dZZmVSTGQ2bk05V3l4WkQ5VWs1RHVuQU1RbndPWUoyNXhNRVFCL0toMW0yQ1RON3d5LzlTWDJScmwrZGZJcng5djJUWTFqSXF4bHZvb2VuR0pWSE50eForSnVLK3dZNlpxMkVFV1QxQWkwSHZwY0EzblMzQWZScXNiaTl6d0hPOTNkRC9PODRha1Y1d3BCRTBrRHNlMC9DUVc1SVd4M0NVNkg5elVMWkEvWlN4ZE5nZUZKeDUrS0FXOGd2NWJBNitMUjB4YzB2S2RaRUxlaUVWaWV5N3hzR0g1MDFXWkEwRk1uUThoWFg4dFNpNGhCN0pXandpUHdnNlVna21TcEJUdDhsaXRWN1FTcTZveWw1cnpDMEhuUlFpMllyb2FZZUVSWEVNUk9vQnczekh3Y2J1Z0Ywem9IWnVOaWREQUd4VjRPVGVnV3o3cEhlRnY3c0J1bllmcmszWUxtL3dVZ1R1UzBzQkZNcVE1SW00c29FY0plc2tNdzZHZDVpVzdNTWJid0hCMmVGdkNqNXMxbXdJTHh6eDdubFhVdDlPMzlSMWdENkRtbEYzVldjd2wwNTA4RXRjTXhtRS8ycVYvaGUiLCJtYWMiOiIwZTc5YzE5ZmY5MzlhODYyYTc4ZTdlNjgzOTNmNDNhYWYxNGM4YTA2ZTUxMmE4OTE4YzQ3YTYwZTIxOTk2NjkyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:05:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-486310781\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1932382157 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1932382157\", {\"maxDepth\":0})</script>\n"}}