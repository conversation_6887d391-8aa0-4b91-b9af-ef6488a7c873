{"__meta": {"id": "X69405126f01d89965cba160abc0f88ae", "datetime": "2025-06-08 00:58:22", "utime": **********.611717, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749344301.923158, "end": **********.611749, "duration": 0.6885910034179688, "duration_str": "689ms", "measures": [{"label": "Booting", "start": 1749344301.923158, "relative_start": 0, "end": **********.521992, "relative_end": **********.521992, "duration": 0.5988340377807617, "duration_str": "599ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.522006, "relative_start": 0.5988481044769287, "end": **********.611752, "relative_end": 3.0994415283203125e-06, "duration": 0.08974599838256836, "duration_str": "89.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45384624, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00591, "accumulated_duration_str": "5.91ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.57364, "duration": 0.00432, "duration_str": "4.32ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 73.096}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.5957131, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 73.096, "width_percent": 10.829}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.600988, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "ty", "start_percent": 83.926, "width_percent": 16.074}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i31pha%7C1749344276347%7C8%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ijl1VzZtQWJWY3k4MGlEV0d0enNQbXc9PSIsInZhbHVlIjoiU1NaK0VWSlE2cGFUeVJYaHpqMDZhOHZENDJvMEhseUh6YkRYTnhXWlZBV0pMTTF0cnFVekhtNWFVMXB4Zm83NnhaQVJ5WGQ1YjJ2VEhvU1dHL0E5Vlh2cUpWQzNsZldBRFlmWUJ4WFNWNUpYemh6SkJWeEdiS2F4Nms4UGZPcDNvblJKZm8rWEpPSnE5cy9EcDk0RENQcmZpUUMzTXlPS3QzTmVlRnY1bDgrclpDTnV2Uk1na1NKZHp1Q3pkWlI1aitSYUpkMVN2ZVZyVXBncnRDV3B2bHBKZzBURHVKV0lidXFFbnB1ZjlHZ1AwZWZuWmVYV3BFZ2d2d1J2aHJWbEZWU21nY1ZRQlFhSXZaSDgzN1JxcGNUQWZwUjMrUGV6L2hxR0ZLTVo2bWxUV2g3eHN5dkpWYzFOdVJhd0VHemdzdXNiZGUxanBocWNoWFYyN2tJSEFEK1U5VW4va2JoR1NFWTVJaHFTbCsrTXR1MnpUZ2lMVXdtaFROYzV2RWtZc2EvZVlFOXZLQWJqU2EyMCtqeHFYRGRLUWpOeStXNU5iQnRvZnhobnB1SlZCZVpoNzB3L0FKMm56RGxway9qVm41L2Q4VHdGZzFIbml2ME83enJTdHhLay9JRVFTZ1c5Nzc1MlVqRmRmYlBnUk5ndWc4SlFMeTUxWmhQRUduUUgiLCJtYWMiOiI3YmEzZTQxZDVlMWFmNmMxM2RkZDM3NTNiZWY3ZjgzZmJiYmIxZGM5ZTZmM2MwMzljOTU2NzVkZTExZWViMGNkIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkNCNTd3V0wvVTNvdnJjRFB1aWk4cUE9PSIsInZhbHVlIjoiT1NzdG1rTkZadHlVenJDTXRlSFNad2xRaVBmajVrSUlzeFdQUTRoZEljcktCSUdYUFNjS1Y2TURlNGVvTisyQkRPUGRkWGFtYWlzMi9nTzBkdDY3Wk5Temx1Myt0Qm5lb0FEc1B3R2ZhZHdWK3UzQjhKdzJROWR3NlhWK3dKQkRhbm5NZ2xOMEJsQXpNRnI3bmQ5WmVBMnQ5eWxnc3pqYWZvQ09iOUp3NHIzRjFDNEkyMlUrODI0blZBQ3Y3QTFIQnVtaGFaeXlHUzIzeUlDREsvOThsWnhvRFZXWWcvZWdXcjJpUTFPeHhBYnZHK0NTNWdacllTaXd6dW5xajdvM0VWVlEvNmpwK3BnUmFoVmhxTVNocEFxUE1WTkxQekw3bkNRUFFYS2hWQThCcDQvYndBdFd2bDdQMHExWmc1T2oyYjJyZGpEeEdzcExlak4zQmg4UGdxWlJqSEZqbWdEMnRudXc3cWVxS0JoNm9icGMwSEVnUTJRNkZHYVBQSzcrYm1NZnpEc2M2cXV5VUFUdEN1OEVmOXcvTnk3QlBua1R5KzBWcGUyQ1hLajFTUWE3b1RnaVpIOGlOOG1RTGY3c3dTUXFxNmF2SEI4TXczUXlqYjVJNFdmQ2pKaEt0UTJsblcyN1c1YThpMldGMnBnODhmcEhNNzFweVFMWDJLak0iLCJtYWMiOiJmYzIxZDIyNjZiZjIwY2U4ZTE0YjNjNDk4YzhlZDFmMWNiZjIzMWU2MTkyZGY4NmU0ZjEzYTk3MjIyNjQ1YzczIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1714329944 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:58:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkVuVi9Nb2dOQ0RwWGl1U3JIc2R4QUE9PSIsInZhbHVlIjoiQjFCT1N5ZVhXT1R6ZHZxckRTZTB3MFhmY0U5bjJtMWtOM3lCc0h1THN5ODRJTUZTM0QzSFhhbjB4RVBYQ2tXTEl2YkpZQmUxZWh1cjVwaHByODFuKzFQeW1ZS0VoSGQ1bUxralJUckk1Y3VTNlhsZFJJalhkZ0FQVHNIZjlKOWQvY2d2b2taZG5QQnFSV0NROFhwY3VaVkRuNnpDWkhGNUhEamtnUW53cUUvUFdPWW5mNERMWjlUQituWmpvMTI4S25rMU8rQzhLUVJaZjZkZGNjRVo5QkRsRmZOQzFWY3NhMUlpeGYxeFNNMjF5MU1RM2FSU1N6SDdzaHAreWhXL3d2bnRUbk9jeEdsdW5BS2oxZFIrWlFJRlVIRXJYbXFYeGU1RjJEczV5Vm00Vkg3U3ZqbnMySW8rZ0NIbW9vZmNNS0szRWlKWGlOdmVtcXFHUzBXbjRqSVdrLzljRk10QmsyMG5aTUN5cEwyU3dBMGg2VU1CWUtqUFN0ZmtaaThkMzAySXd3cXJaYmNzYlJKODFOL0R0M1VyTkhUZWh5R2NJdUhiS2dqMDJIdkRnQkhISW5GNEVBOGdqK0FOK3U2VzdzUlB5TkRxS3ZSajVJcTlqZi82MER1Zk0rdHpOeEFIUUJGanRKelRuVEMybmovUElLWU93TUFsYWZ6Uk42RzEiLCJtYWMiOiI1NjQ5ZjM4NjNmNWY0ODBiY2YwNGFmNDVmMjMxOTlkMWM4MTMyODViZmI2OTczZmZhZTE4ZjU2MGMzZjcyNDZjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:58:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InErUmFNbFVnT00wYTRMa0pQaU8zV1E9PSIsInZhbHVlIjoiUU5PUVVCcVo5aWxQam9mZTMyMFc0WFZmZXBUSjRVVGx0eHUzeWUyV05OcVdRQ1FubzJsaTN0NUNQcDZyUEJueDVqMkM5eisrZEY3SVlRbk8zK2ZrcmdBRzlTZ1ZsWmlMaU1vQ0NUMGNsRldiRXNHL3U2NG9TMEtrVFFjTkxoRzJwTnVvL2JWdWRxOTQvQkdrcE5HbkRuYzdqU2ZxZFhvWGdzamtESXNrQ2V4NnRQMnJnRHVqb3JibmdtT2trSGdkdFpFcmRzNVJSTktIYWdyZCtRcDl0NXljUmdWNVVTNmZIaUFxVkZndkdrTmNEZWpqTExUbStpNnJMZDVLaHMrMUpTV2duSSs3bks4UUNWM0F0dlNtOGlUaytZR2NmMEh1VmRxY1BoUjNGeE8wa2dreXozd3FIaHkrY3RHUHBZT0QyY0pvY0lEV1d4VVpVejFyOEp3UTdWMVdXZEdMRjVsa21mc3NydGRWVGZIVmtFd05rOEdnY1JpalJUbVZmWXVRSm95NlZiS3E4LzZnS21LOHREdlRmOFBZdUR1TnB6YnRNMEJPY1lqSk1BajBqMXV2cnFIM2c1T3FzdnNoL3lvaWY3SHBUOE5iWUNBRkpmTGtFU3lpZEVvRUd2TFJ3aXdQdHJBbGdzUEV5aElxb3JPZWE1MDBCS0VWMVlqay9qSzgiLCJtYWMiOiIzNGRmYWY2NDM1OTUzMjU4NTk0NTg0OTJlOWM4ZTcxZDc1MjRlY2U4NTFmNzU5NGUxNjNjMjVlMDY2ZjYxYWQyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:58:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkVuVi9Nb2dOQ0RwWGl1U3JIc2R4QUE9PSIsInZhbHVlIjoiQjFCT1N5ZVhXT1R6ZHZxckRTZTB3MFhmY0U5bjJtMWtOM3lCc0h1THN5ODRJTUZTM0QzSFhhbjB4RVBYQ2tXTEl2YkpZQmUxZWh1cjVwaHByODFuKzFQeW1ZS0VoSGQ1bUxralJUckk1Y3VTNlhsZFJJalhkZ0FQVHNIZjlKOWQvY2d2b2taZG5QQnFSV0NROFhwY3VaVkRuNnpDWkhGNUhEamtnUW53cUUvUFdPWW5mNERMWjlUQituWmpvMTI4S25rMU8rQzhLUVJaZjZkZGNjRVo5QkRsRmZOQzFWY3NhMUlpeGYxeFNNMjF5MU1RM2FSU1N6SDdzaHAreWhXL3d2bnRUbk9jeEdsdW5BS2oxZFIrWlFJRlVIRXJYbXFYeGU1RjJEczV5Vm00Vkg3U3ZqbnMySW8rZ0NIbW9vZmNNS0szRWlKWGlOdmVtcXFHUzBXbjRqSVdrLzljRk10QmsyMG5aTUN5cEwyU3dBMGg2VU1CWUtqUFN0ZmtaaThkMzAySXd3cXJaYmNzYlJKODFOL0R0M1VyTkhUZWh5R2NJdUhiS2dqMDJIdkRnQkhISW5GNEVBOGdqK0FOK3U2VzdzUlB5TkRxS3ZSajVJcTlqZi82MER1Zk0rdHpOeEFIUUJGanRKelRuVEMybmovUElLWU93TUFsYWZ6Uk42RzEiLCJtYWMiOiI1NjQ5ZjM4NjNmNWY0ODBiY2YwNGFmNDVmMjMxOTlkMWM4MTMyODViZmI2OTczZmZhZTE4ZjU2MGMzZjcyNDZjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:58:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InErUmFNbFVnT00wYTRMa0pQaU8zV1E9PSIsInZhbHVlIjoiUU5PUVVCcVo5aWxQam9mZTMyMFc0WFZmZXBUSjRVVGx0eHUzeWUyV05OcVdRQ1FubzJsaTN0NUNQcDZyUEJueDVqMkM5eisrZEY3SVlRbk8zK2ZrcmdBRzlTZ1ZsWmlMaU1vQ0NUMGNsRldiRXNHL3U2NG9TMEtrVFFjTkxoRzJwTnVvL2JWdWRxOTQvQkdrcE5HbkRuYzdqU2ZxZFhvWGdzamtESXNrQ2V4NnRQMnJnRHVqb3JibmdtT2trSGdkdFpFcmRzNVJSTktIYWdyZCtRcDl0NXljUmdWNVVTNmZIaUFxVkZndkdrTmNEZWpqTExUbStpNnJMZDVLaHMrMUpTV2duSSs3bks4UUNWM0F0dlNtOGlUaytZR2NmMEh1VmRxY1BoUjNGeE8wa2dreXozd3FIaHkrY3RHUHBZT0QyY0pvY0lEV1d4VVpVejFyOEp3UTdWMVdXZEdMRjVsa21mc3NydGRWVGZIVmtFd05rOEdnY1JpalJUbVZmWXVRSm95NlZiS3E4LzZnS21LOHREdlRmOFBZdUR1TnB6YnRNMEJPY1lqSk1BajBqMXV2cnFIM2c1T3FzdnNoL3lvaWY3SHBUOE5iWUNBRkpmTGtFU3lpZEVvRUd2TFJ3aXdQdHJBbGdzUEV5aElxb3JPZWE1MDBCS0VWMVlqay9qSzgiLCJtYWMiOiIzNGRmYWY2NDM1OTUzMjU4NTk0NTg0OTJlOWM4ZTcxZDc1MjRlY2U4NTFmNzU5NGUxNjNjMjVlMDY2ZjYxYWQyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:58:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1714329944\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-13******** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-13********\", {\"maxDepth\":0})</script>\n"}}