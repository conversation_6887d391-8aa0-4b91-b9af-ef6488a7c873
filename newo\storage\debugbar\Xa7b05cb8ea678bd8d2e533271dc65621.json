{"__meta": {"id": "Xa7b05cb8ea678bd8d2e533271dc65621", "datetime": "2025-06-08 00:40:00", "utime": 1749343200.011996, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.219606, "end": 1749343200.012023, "duration": 0.792417049407959, "duration_str": "792ms", "measures": [{"label": "Booting", "start": **********.219606, "relative_start": 0, "end": **********.889983, "relative_end": **********.889983, "duration": 0.6703770160675049, "duration_str": "670ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.890001, "relative_start": 0.6703951358795166, "end": 1749343200.012026, "relative_end": 3.0994415283203125e-06, "duration": 0.1220250129699707, "duration_str": "122ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45052544, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.022390000000000004, "accumulated_duration_str": "22.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.948912, "duration": 0.02091, "duration_str": "20.91ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 93.39}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.9872, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 93.39, "width_percent": 3.35}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.996343, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.74, "width_percent": 3.26}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1529223217 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1529223217\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-460642529 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-460642529\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1958809311 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1958809311\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1903991768 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749343190671%7C35%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InJQMEI5c09lNU5zWWppOHY2Y3lKWUE9PSIsInZhbHVlIjoiTjJldHBDRXNudG9ZUXhKQmlOZUVWcWxjRys4cUwxVEJtY0tpMndjVE5QTy9kOVZ3eXptbXdSTTJERWkwMHQ3OW1wa2kxc0wwYWkzaldNRzU1d3VvNkFQZ2Zoa3prWVZaY3FLVlZ4Uk01R2ozODBSRGRRd1g3emJsSUhGM29NaUpFSnlqZmZ3Z05lb3BNNTFqS3dmS0NFL01qR3dxcUtyOUtOcWJLaGFicG41Qm1iQUNwZ2QvcFY1Q0k2dXYyd0hnYjBrbHZ0b3FMRXJIWUhQbjBOTFFYcld2NUZ3czl4R1NuMThKWXZwK1BibmdIME1JUFJkWEVrRWZtRDgydDFhUzUxQXl3V2Y4QkFDUHhsaHVsbmlPT2pvOEJFRHpHRmxxb1ZGYWUrQTk3SUVQdWU5eFJjS21DWXhjRWJ2RHRlcVFpZTJBVEhVbkZmNjhSNGRKR0YwWjBMQnpkcHMzbE16ZTFLQlFRVVZ1eno3UWcxRHhCVzNERkpYZEVjN3ZnaTVERnRhOXBUS3NpZVNLOHZIL1ZINU50VnBRcFJkRVBQYVIxemZ2bHphWWtrUDlPajZsZ0hTNUFJcnRKL2l5eWdKaUhZQ3JmRVlWYXVZL1VKejI2Q3l1OVZJWjFlelRDMzJCUktWOVBjYmhRTXpFMVFTdnJKOEl0TldvZkFoVHoxM1oiLCJtYWMiOiJiNGJiODRjODZhMTM1ZmQ5M2I3YmEwNDllODk1ZjY5M2Y0Yjk3ZmM2ODA4ZTJkZDA1NzU2MmJkOGY0MGUxNjgwIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IllkUnpjRG1wNGRaVnFYaTBFdDhkREE9PSIsInZhbHVlIjoiWDNieTBmT2k3MnJIOERqN29zbm1nblJBVnIzMFg3bEhVT1QyNFByM1Q3UXFpUTBqVEdCZWZheTRLcFM3Y053UkRSOXpqK3Jock1wVFBMYlVhTnBuUVFCNWpRdDEwbXpHR2tDVWx5YVNucUpNS1NYZkJ2eWVqdWVjb3RxeDUrb0Jxbjl2TCtHemlDRHdaYnFZZFIreXVRYy83U2swZ2JpMHU4TDFKVU1xRTlvM2F1MG1FaVJxelZKU01tMGxIOWM2dVVRWmlJaGFpeW03Q0xEb1JlMlNtREFqeVVuOXNrbFZpaEYvTFNPdEtjUGNyTjduU1V1U05TcGVucDBzYnpWNW1MUmZmRVE4V3BiZFBLTHBteDBOVllnSDY3anFoK3lwVXlHU2l3VXdJT2V4b1hvbzRSSjV4SVRoQTVGd2I2WlRPbmR3NVE5ZDFVM3BlZEc5M0diR254VEZsNVhIcWZQRE1CR2dDaTBFSElzTjR4TWUyMUlrWmRTRy82OWVDL1BKSWgxbWZoalY5S3N6dGtjemgrd0p3QkoxRFhwUlVuVVY3N29PQ1hDSU5YS0NCQ3FjRndtbDJjOUJEUHB2MUtQeTdtWUloOXd0Y0d4UHY3eHF4WU1rVFR5ZzdVWC95OENyYUFUMHNmTHJlWDd5V1lOZjZMZllXMG96cjRIcENCR2kiLCJtYWMiOiI0ODJiNmI1NDVkNzcyNmQ5OGMyMTZkZTcyZGUzOTY2Mzc0MmQwNzUxMjU3ZjBiOWIxYmIzOTY1MjZiMmY1ZDI0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1903991768\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1096353331 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:40:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlhqMjFQSkh6cUlTc0g1N0daLzNzQUE9PSIsInZhbHVlIjoiMUtUY3hmdGUwNFRXak8yYU9KMzlKcjRoZW05Z3Nqam1sekNZR3BKN1U2Rk9wYUk0MEQ3OW5MWjlSZ0ovZGlKU1RiOTFmU0h6REJjNDFGMkY3aGdOQkpvRzZpRTRhZDZKTk9iaVFIN2hVb1Rjc2t0elRLUU5nc1NzenNYOFJkTEpSWXpZbHQxOEJwb25DZk9jU3dKcGN3OHlsZDNURkNnNU54Tys5NSt1RjJhTURYTWJOSXRyTWk3V0QrZVNwODZYbkpwM0lTQ0tEYmF1QTRnU1N0K2QwYVhIZ2NFQXUzYTJlY2VTelFEY0wyMnFIVStzQUdKTXNYZWJ6ZFhvUm5MWFgrWWk3KzcyYkxMd1RYZnZxZWhuWkRXWGdUTjZoMlA1L0FsblRpS2hyb01vSjV0aHQ2NXdyNmlqK3hYVWw2VEgzYjVPYzhDbTJYQUJ5R1liVmhKUmlwVGdaZ3lvTU1Sd2kzRWJGdjZ2MXB3VWRzUE1qNi9qYmZ3eHFhVjBKak5GM0tqWFZlRXpqekNsRkFNcjlBd1Fxdk0reElyaEZQTDhEV2dObkkyM3l5MjRJdkNydlRNTnUyRUJzeHc4S3hOQ1BJLzVsTXhoamtLcm9GdzFvTWlRd0QwQTFjSDBMNzArMGVac3lab2ZzbzQ0M3E3eEpJdW16TXVYalJqeFNGb2giLCJtYWMiOiIwMjY4ODUyZjMxZWM5MTA5YTRjMWM2ZTgzZTExMjFmNmYzNTNlODA5Yzc1ZjBkZGQzNjFjZTI5MjM4YWViNjBhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:40:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InVNNlhrMmRoK21SZitmY3FlUVJOR3c9PSIsInZhbHVlIjoiVWpQOVgxdlFaY0lkOVZtRDlMNEFHSVhDTGRIWm40Z1pyb1U4cDVGem9tVVlEUnM0M2ROZk14bExLSno2NWRFMkZmeHYwNWZPYmNzWW9KWm8wcmluQlEvOEE1RU0wTCttSlhaRE1ELy9VajVsZGt6TFYzREtQQzNXcHJsdndwRmtlUnZiRHk2OE1CTHBiUFJHMTk1L0ZYdmtUUTdtc0dFM0pIdzdHMXNHL3ViaXVVOXFYRGlDNkx6VWlYeC9rSmxEMlJNODJJVWZkQlVZc3Y0L1hyZ21ocEx4NXAzUTg2bDlyd0dJMUpTUEg2VnpFWXRRZ2ozMC9ZdzlXK0ZjMFFNVTBJbjBHVnhWWHRGTVdENEFhTHd5NXRNNVM3THlkL1hIRU4yYVNyL2xDS21BNU50a0ZjZUJpeDBUVXRYU21NQ2JXMDlRZjBVMGdVRXJqRVFXUEJpRENCTExmNmlYZXFLV1hvU2lkbWhmSENvNnMxMnNsakc1UE94bFVvb2h3b292aTVQdG5WbnMzSkxwKzRwaEFjVzIySGsxK3lnYnRMakhqYnpUUmdNZHFHUnZBSENCL1VSR2hVL3dRdndOM0hGODF0V0ZCbEdZNGo0cUJTK2xMc1VOVXdsYkhqYldrb1JMMnk0bWEwSk9KU011YVl1cmkwZVNEV3UyOElEN2VIdXciLCJtYWMiOiJkNDBlZmQ2MTcxZjc4N2Q0NDhlYjBiYzk4ZGQzMGZjZmRkOGY5MWJiZjkzNzcxMTE4NWY0NjQ5ZjBkODhjY2Y5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:40:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlhqMjFQSkh6cUlTc0g1N0daLzNzQUE9PSIsInZhbHVlIjoiMUtUY3hmdGUwNFRXak8yYU9KMzlKcjRoZW05Z3Nqam1sekNZR3BKN1U2Rk9wYUk0MEQ3OW5MWjlSZ0ovZGlKU1RiOTFmU0h6REJjNDFGMkY3aGdOQkpvRzZpRTRhZDZKTk9iaVFIN2hVb1Rjc2t0elRLUU5nc1NzenNYOFJkTEpSWXpZbHQxOEJwb25DZk9jU3dKcGN3OHlsZDNURkNnNU54Tys5NSt1RjJhTURYTWJOSXRyTWk3V0QrZVNwODZYbkpwM0lTQ0tEYmF1QTRnU1N0K2QwYVhIZ2NFQXUzYTJlY2VTelFEY0wyMnFIVStzQUdKTXNYZWJ6ZFhvUm5MWFgrWWk3KzcyYkxMd1RYZnZxZWhuWkRXWGdUTjZoMlA1L0FsblRpS2hyb01vSjV0aHQ2NXdyNmlqK3hYVWw2VEgzYjVPYzhDbTJYQUJ5R1liVmhKUmlwVGdaZ3lvTU1Sd2kzRWJGdjZ2MXB3VWRzUE1qNi9qYmZ3eHFhVjBKak5GM0tqWFZlRXpqekNsRkFNcjlBd1Fxdk0reElyaEZQTDhEV2dObkkyM3l5MjRJdkNydlRNTnUyRUJzeHc4S3hOQ1BJLzVsTXhoamtLcm9GdzFvTWlRd0QwQTFjSDBMNzArMGVac3lab2ZzbzQ0M3E3eEpJdW16TXVYalJqeFNGb2giLCJtYWMiOiIwMjY4ODUyZjMxZWM5MTA5YTRjMWM2ZTgzZTExMjFmNmYzNTNlODA5Yzc1ZjBkZGQzNjFjZTI5MjM4YWViNjBhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:40:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InVNNlhrMmRoK21SZitmY3FlUVJOR3c9PSIsInZhbHVlIjoiVWpQOVgxdlFaY0lkOVZtRDlMNEFHSVhDTGRIWm40Z1pyb1U4cDVGem9tVVlEUnM0M2ROZk14bExLSno2NWRFMkZmeHYwNWZPYmNzWW9KWm8wcmluQlEvOEE1RU0wTCttSlhaRE1ELy9VajVsZGt6TFYzREtQQzNXcHJsdndwRmtlUnZiRHk2OE1CTHBiUFJHMTk1L0ZYdmtUUTdtc0dFM0pIdzdHMXNHL3ViaXVVOXFYRGlDNkx6VWlYeC9rSmxEMlJNODJJVWZkQlVZc3Y0L1hyZ21ocEx4NXAzUTg2bDlyd0dJMUpTUEg2VnpFWXRRZ2ozMC9ZdzlXK0ZjMFFNVTBJbjBHVnhWWHRGTVdENEFhTHd5NXRNNVM3THlkL1hIRU4yYVNyL2xDS21BNU50a0ZjZUJpeDBUVXRYU21NQ2JXMDlRZjBVMGdVRXJqRVFXUEJpRENCTExmNmlYZXFLV1hvU2lkbWhmSENvNnMxMnNsakc1UE94bFVvb2h3b292aTVQdG5WbnMzSkxwKzRwaEFjVzIySGsxK3lnYnRMakhqYnpUUmdNZHFHUnZBSENCL1VSR2hVL3dRdndOM0hGODF0V0ZCbEdZNGo0cUJTK2xMc1VOVXdsYkhqYldrb1JMMnk0bWEwSk9KU011YVl1cmkwZVNEV3UyOElEN2VIdXciLCJtYWMiOiJkNDBlZmQ2MTcxZjc4N2Q0NDhlYjBiYzk4ZGQzMGZjZmRkOGY5MWJiZjkzNzcxMTE4NWY0NjQ5ZjBkODhjY2Y5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:40:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1096353331\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-574446241 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-574446241\", {\"maxDepth\":0})</script>\n"}}