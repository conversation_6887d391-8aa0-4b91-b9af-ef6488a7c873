{"__meta": {"id": "Xd5ca2e79f523dd292d2cc96eba9c57b7", "datetime": "2025-06-08 00:40:06", "utime": **********.898214, "method": "GET", "uri": "/roles/19/edit", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749343205.962104, "end": **********.898245, "duration": 0.9361410140991211, "duration_str": "936ms", "measures": [{"label": "Booting", "start": 1749343205.962104, "relative_start": 0, "end": **********.603462, "relative_end": **********.603462, "duration": 0.6413578987121582, "duration_str": "641ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.60348, "relative_start": 0.6413760185241699, "end": **********.898249, "relative_end": 3.814697265625e-06, "duration": 0.2947688102722168, "duration_str": "295ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 55487800, "peak_usage_str": "53MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "1x role.edit", "param_count": null, "params": [], "start": **********.753408, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/role/edit.blade.phprole.edit", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Frole%2Fedit.blade.php&line=1", "ajax": false, "filename": "edit.blade.php", "line": "?"}, "render_count": 1, "name_original": "role.edit"}, {"name": "1x components.required", "param_count": null, "params": [], "start": **********.820507, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.required"}]}, "route": {"uri": "GET roles/{role}/edit", "middleware": "web, verified, auth, XSS, revalidate", "as": "roles.edit", "controller": "App\\Http\\Controllers\\RoleController@edit", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=99\" onclick=\"\">app/Http/Controllers/RoleController.php:99-127</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.012889999999999997, "accumulated_duration_str": "12.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.652221, "duration": 0.00437, "duration_str": "4.37ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 33.902}, {"sql": "select * from `roles` where `id` = '19' limit 1", "type": "query", "params": [], "bindings": ["19"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 961}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 42}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 64}], "start": **********.6639261, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:61", "source": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FImplicitRouteBinding.php&line=61", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "61"}, "connection": "ty", "start_percent": 33.902, "width_percent": 5.198}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.675854, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 39.1, "width_percent": 5.818}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.699312, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 44.919, "width_percent": 6.129}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.7037652, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 51.047, "width_percent": 6.594}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 2", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/RoleController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\RoleController.php", "line": 114}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.712216, "duration": 0.0033799999999999998, "duration_str": "3.38ms", "memory": 0, "memory_str": null, "filename": "RoleController.php:114", "source": "app/Http/Controllers/RoleController.php:114", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FRoleController.php&line=114", "ajax": false, "filename": "RoleController.php", "line": "114"}, "connection": "ty", "start_percent": 57.642, "width_percent": 26.222}, {"sql": "select * from `plans` where `plans`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Plan.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Plan.php", "line": 65}, {"index": 21, "namespace": "view", "name": "role.edit", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/role/edit.blade.php", "line": 2}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.808851, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Plan.php:65", "source": "app/Models/Plan.php:65", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FPlan.php&line=65", "ajax": false, "filename": "Plan.php", "line": "65"}, "connection": "ty", "start_percent": 83.863, "width_percent": 9.077}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 19", "type": "query", "params": [], "bindings": ["19"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/konekt/html/src/FormBuilder.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\konekt\\html\\src\\FormBuilder.php", "line": 996}, {"index": 24, "namespace": null, "name": "vendor/konekt/html/src/FormBuilder.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\konekt\\html\\src\\FormBuilder.php", "line": 938}, {"index": 25, "namespace": null, "name": "vendor/konekt/html/src/FormBuilder.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\konekt\\html\\src\\FormBuilder.php", "line": 911}, {"index": 26, "namespace": null, "name": "vendor/konekt/html/src/FormBuilder.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\konekt\\html\\src\\FormBuilder.php", "line": 888}, {"index": 27, "namespace": null, "name": "vendor/konekt/html/src/FormBuilder.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\konekt\\html\\src\\FormBuilder.php", "line": 851}], "start": **********.824426, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "FormBuilder.php:996", "source": "vendor/konekt/html/src/FormBuilder.php:996", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fkonekt%2Fhtml%2Fsrc%2FFormBuilder.php&line=996", "ajax": false, "filename": "FormBuilder.php", "line": "996"}, "connection": "ty", "start_percent": 92.94, "width_percent": 7.06}]}, "models": {"data": {"Spatie\\Permission\\Models\\Permission": {"value": 521, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Plan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FPlan.php&line=1", "ajax": false, "filename": "Plan.php", "line": "?"}}}, "count": 525, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => edit role, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1932554558 data-indent-pad=\"  \"><span class=sf-dump-note>edit role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1932554558\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.711025, "xdebug_link": null}]}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/roles/19/edit", "status_code": "<pre class=sf-dump id=sf-dump-340371360 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-340371360\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-149311881 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-149311881\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-62149276 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-62149276\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1460220555 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749343201961%7C37%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ii9wK05BUURhbENCc2xnbTlSRE9YeVE9PSIsInZhbHVlIjoidGxlZmdPN1Z1YnhtZGNSRkIrS0JDUTRhTXlMTDZvdWRtSG94RUxzVmRzK20vMHJWa3QrNU0wdE93YVdwcHVKOTlLYWJ1dzFzMjZ5YmV2aGhFRE84b0FsZTZoMTUyMjg5ZTJsaCt4czBVaGMxWlE4L2hpekNUeXVPTjhNbVhpYlBRQkFDamt4bjB0c2NLMnpmVjFpdSs4R3N1OXE0eUloYjRVYmNkTHp0dDRuS2lmRGNScVZ5TDRhWHpiLzNkZkppQ0xaY2NOWm5yRXlYQkN1OWtPNGJGV1N3bExNNFp3VmhRQTBHNkxGUkNSaS8xczFiaStoWk1CK2dFSG1rLzlEcTMyM2ErdUdnajM0UXRYZXN4V09sY0V6cjNaUGZ2MWxBUHB4SWZyelZqWFZ2ODJWK29zUFVWTTRFZHFQQ0RqMnF5VWRKNkJyVlpMWlcvay9LUnNsQklIeUxOTWNoTzFMdDhjV0pZOHBnQ0g4U2pyaDZCamtKS2xlMEdBSXJuL3ZBV3RKV0J5aW5BcDlWZis4VkNBallzZ0FTMW1kckxZWUUwdXIvVGtmMWVCNkJFRTBlcUxnMlJxbUJTeGN1Q0JwNzIrbG1kOFNMSHV3SENFYzlneEtiTkF3Tk9pcUpaRGFEblFMK0hTeW51bFhSYTEwcHF4MHR4SkpNY0xvTk4zaWkiLCJtYWMiOiI3Njc0NmQzYzVkNzliODM2NGFmMTc2MTkzZGE4ZDFhMDNkOTQ4Njg1ZTJkNWZmODNmZTZkMmQzMmFhOWRkZDhlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjBwdjZUNlpmR1g3WGhCeCtkaGZTY1E9PSIsInZhbHVlIjoibElSVlZINmhSdlJEL3VlZ09TN2pRY3FNQk5EbEttdHFnSlVERTlFVCtyemx2akJMeURCaHNQNmN6RnhhSXJrblhUcGtQS0EwUHllcW1pdXA3U0VaOGttcS9OUURURzdEbEMzWHVwL1FhR0NycXpVNEhaNDFUaHM1Y05KZFRJczZXYzZXb2N3S0JCaXQ4czZKSkFGNjZrMW5MS1k1ektIN0luMXMrc3dKWFRCMHJpQXgrM0hHNnUxTHFzSmVuamZmcTl6ZURwTFMzb2puWi8ra21FbnFKbDBrckNZaEs2OFFWb0FRTXdoTW5MbERHREQvNkxSTjR3eXVxSkVOLzhWWEh5OSsrR1FNU1JSUWh3cG8wRnVZVk4rS1pDaXFPekVJWDFGaUZGR3VEUkJSUTU4UktxSGQ0WkJZVWducEwrdFZzdGZ0dDdBZkp5K1BnWWpyTXVEN2RCdG9DUkEyWFd0Q0RNYlluQmhMTjhVWlF5dSs5T0dHODFYRUdkZTFpME8yZjZxSjkwd2xhY0NERlNnT09mY2R4SHFXSkFTQW9GeU0wcmdwcUJ1ZERQdFFLNmtQVldQQkJBRGdkRHUrcEdQc1lFdDZ0cjMzZ0dyNGt2WW9ScGkwUW5xUEgyWWQzNEQyVkJwSkhsU0I4RmNtOXFCVDlram5IOEdFa0F2bGc0WmYiLCJtYWMiOiI5ZDIzNDU4ZGYxNTVlZWEzNDg5NzViNjk2YjczOWNmZmViN2JmNTM3YzExYTQyNGRkZWViODQ5NDI3NGZhM2JmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1460220555\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-186407708 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-186407708\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1357099736 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:40:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImdCL0NIZnJJNFVoRmt2OHBYTTV0YVE9PSIsInZhbHVlIjoiUUtGaWNMV1FaYThnOVliV1lnSkRXWVkrVGRobWY2OUpabXJmQjhwYk85R2t0c0dSa2hyNkVQT0xKMFl6bzY4d0NsdWVpcXRMdlRTMWVLUDJLNllHcHR4YnZBUlpOaW9zMkZoMEpZWXJLTUtjbmNQVklvNHRxcWQxdnNSbHhqajZySHJBR3llMEFQNTVFMkREbytZeHJXLzNvUUpCTDlnV0EwTWdaSVpmZjJlV0VSUnptb2puTWJQUmV5SzFzZ25SU09EZGlZWmZ0MllrZ3FXaTd1MDVxNWFXVG1oUWtNUXpBMExaQWwyTXRlc2c2TlRkZU1pK2IvWG4xb0NMZXp2ZnRwTDBNa3hpVkdaOHJ5OWNZOERWbHdIZFkyK1EyaWNDWWJHU1p2VzBaSmsrd3JwTHRZM0JGc2ZPUGthOGVkcnpLbXFOZTB0eWJMZ0ZmZEtpY09pVkNCYWRKbmd4em13QWw2VUJNbk5zUVBlaVZiWVlISlFTTTZ0c2Zycko1bU9Sb3A0a0EreURkT21pS2dnVFZvR25XMEFPWVRBV0RERTN2R2tUTmtqU08xb2g0QTN0bE1HTHB3RVJQcU43cDFiQkRscVJTMzdNeXJTeWVDTVF1WXJwUmVPbXdkbmY5MnZqSmMvWksxenBDOTlxRXN1L1U1Z1dnUXhabFdNZU93VCsiLCJtYWMiOiJlZjM3OWU0YWUxMjFlNTMyMWRlZGZhMzBmMjA3NmJjYjFhOThmOTU4NGI1Mjc3MWVhNzNmMTQxM2U3YWNmNDUxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:40:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImY2RTUrZlZuZUc5U3VMM3BNRGRtTlE9PSIsInZhbHVlIjoiRUlucytsNXh1SlRWK0tzeW5HMEdTQjdUQmVsdWtKK3kwS2x1UlFHTGJYNXRFL3dLRC9WSmo3ajJFaTVXd2pjSCtKTStoYyswMU0vT09ZbEJSbTRoM05KcWlESzBuY0hIY2FUdGszaGxNTzY0OXVPQlIvZ284UElpT3hJQlVyQkhac1QvSVlOckFjR294R09hdlRSQUNBeFRqOG0vR3VPKzJIVksxMnJlWkxpa1VzSDVmQUVJVmNJUHp4TDlIYmRCajk0REtYS0lRaW9CQnhpaUMrcU9QMUl0OGtyNjdKT21ZcEpBOXNXeWptUXBGOVU0M2N4ditWN28vZ3VRQmQ5ZXpTNUNIQ2JhWi9XSk5GR2lyOEFBNU5uRTRnS1lTU09CVTF1dVlRWjRvTDNzRzFzNndVMnZLK2pHeXA4OThhT3VnZW5OU2NyYlFZUzJmZW52OE12dEdHbVlJUmdUbW5LdnlqRjdRUG9jOWxUUTVqQUpPMGU4a201dUJLbklpanRoejhkNEtTTVRiSEYzU1ZDWGtwM08ySmoxNmJoVVh3S0ppaWN1MzZKQy9WYlRPQUtBa1NrWjR6ekY4cldpdzNpM2ZjemNyRkRidXVYVmpua1FRZUcxTEFCd2tUQXlIVGExWUhIR0tSakQ2OHFPQ1kwbkxiS3oxcXlDY1JyYWl4NUEiLCJtYWMiOiI3MjRhMzMwOTgyZDA3NTU5NGIyYjVlYTU3ZGMyZGRlOGU2Mjk1ZWI3N2NkMzVjN2YyNmRkNzI4MzMyMzU3NGYyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:40:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImdCL0NIZnJJNFVoRmt2OHBYTTV0YVE9PSIsInZhbHVlIjoiUUtGaWNMV1FaYThnOVliV1lnSkRXWVkrVGRobWY2OUpabXJmQjhwYk85R2t0c0dSa2hyNkVQT0xKMFl6bzY4d0NsdWVpcXRMdlRTMWVLUDJLNllHcHR4YnZBUlpOaW9zMkZoMEpZWXJLTUtjbmNQVklvNHRxcWQxdnNSbHhqajZySHJBR3llMEFQNTVFMkREbytZeHJXLzNvUUpCTDlnV0EwTWdaSVpmZjJlV0VSUnptb2puTWJQUmV5SzFzZ25SU09EZGlZWmZ0MllrZ3FXaTd1MDVxNWFXVG1oUWtNUXpBMExaQWwyTXRlc2c2TlRkZU1pK2IvWG4xb0NMZXp2ZnRwTDBNa3hpVkdaOHJ5OWNZOERWbHdIZFkyK1EyaWNDWWJHU1p2VzBaSmsrd3JwTHRZM0JGc2ZPUGthOGVkcnpLbXFOZTB0eWJMZ0ZmZEtpY09pVkNCYWRKbmd4em13QWw2VUJNbk5zUVBlaVZiWVlISlFTTTZ0c2Zycko1bU9Sb3A0a0EreURkT21pS2dnVFZvR25XMEFPWVRBV0RERTN2R2tUTmtqU08xb2g0QTN0bE1HTHB3RVJQcU43cDFiQkRscVJTMzdNeXJTeWVDTVF1WXJwUmVPbXdkbmY5MnZqSmMvWksxenBDOTlxRXN1L1U1Z1dnUXhabFdNZU93VCsiLCJtYWMiOiJlZjM3OWU0YWUxMjFlNTMyMWRlZGZhMzBmMjA3NmJjYjFhOThmOTU4NGI1Mjc3MWVhNzNmMTQxM2U3YWNmNDUxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:40:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImY2RTUrZlZuZUc5U3VMM3BNRGRtTlE9PSIsInZhbHVlIjoiRUlucytsNXh1SlRWK0tzeW5HMEdTQjdUQmVsdWtKK3kwS2x1UlFHTGJYNXRFL3dLRC9WSmo3ajJFaTVXd2pjSCtKTStoYyswMU0vT09ZbEJSbTRoM05KcWlESzBuY0hIY2FUdGszaGxNTzY0OXVPQlIvZ284UElpT3hJQlVyQkhac1QvSVlOckFjR294R09hdlRSQUNBeFRqOG0vR3VPKzJIVksxMnJlWkxpa1VzSDVmQUVJVmNJUHp4TDlIYmRCajk0REtYS0lRaW9CQnhpaUMrcU9QMUl0OGtyNjdKT21ZcEpBOXNXeWptUXBGOVU0M2N4ditWN28vZ3VRQmQ5ZXpTNUNIQ2JhWi9XSk5GR2lyOEFBNU5uRTRnS1lTU09CVTF1dVlRWjRvTDNzRzFzNndVMnZLK2pHeXA4OThhT3VnZW5OU2NyYlFZUzJmZW52OE12dEdHbVlJUmdUbW5LdnlqRjdRUG9jOWxUUTVqQUpPMGU4a201dUJLbklpanRoejhkNEtTTVRiSEYzU1ZDWGtwM08ySmoxNmJoVVh3S0ppaWN1MzZKQy9WYlRPQUtBa1NrWjR6ekY4cldpdzNpM2ZjemNyRkRidXVYVmpua1FRZUcxTEFCd2tUQXlIVGExWUhIR0tSakQ2OHFPQ1kwbkxiS3oxcXlDY1JyYWl4NUEiLCJtYWMiOiI3MjRhMzMwOTgyZDA3NTU5NGIyYjVlYTU3ZGMyZGRlOGU2Mjk1ZWI3N2NkMzVjN2YyNmRkNzI4MzMyMzU3NGYyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:40:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1357099736\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-897010711 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-897010711\", {\"maxDepth\":0})</script>\n"}}