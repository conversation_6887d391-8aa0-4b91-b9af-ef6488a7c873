{"__meta": {"id": "X2526993f8884931bbad3bec7c4127462", "datetime": "2025-06-08 00:29:47", "utime": **********.793916, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749342586.75122, "end": **********.793946, "duration": 1.0427260398864746, "duration_str": "1.04s", "measures": [{"label": "Booting", "start": 1749342586.75122, "relative_start": 0, "end": **********.662227, "relative_end": **********.662227, "duration": 0.9110069274902344, "duration_str": "911ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.662251, "relative_start": 0.9110310077667236, "end": **********.793951, "relative_end": 5.0067901611328125e-06, "duration": 0.1317000389099121, "duration_str": "132ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45399144, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00666, "accumulated_duration_str": "6.66ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.740902, "duration": 0.0044, "duration_str": "4.4ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 66.066}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.766288, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 66.066, "width_percent": 14.565}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7740989, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "ty", "start_percent": 80.631, "width_percent": 19.369}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1834 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749341283326%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjRmUWx5eVZSQ2lOOHd6eVhKMkMzU1E9PSIsInZhbHVlIjoiVDZFSjNFalVEOFNmQ2x5N1dpMCtlSStYb0xKVUhCdXdmVDhnUnBiZGZ2cmZaMmpBcWw5cDkxeGcxdXROeThHY2twS2doVUFLNG44bnlxNjlMa1liZFF4bWtCUHBPZjZmUnRCZjhsWkdoVjR2ZDJ2Wi9seWhjSVQ3UDUyc1owYjdpTG9IT3hkbzA2d3hhSHI3MkRLeEFwcjk5bGN4b2Z6U0MrRHVhQURVQzFma0l2Uzhya1c3OXY0WmFTUXFiMHMzRUtrcGRlbEJRQjFwYkt6anM2dERSbllIK1cwdWFBOWM5eWlYQXI4M3FxenM1eHpDSktmcXZ5VDFHK3YraUVnMTl4N1BVU0FUc2liRkpWVjV1OWdLQmVtbDBwQ2hUYURFYlFyUk1yOU1GUW5oVjNXRHg3UFBLUjlUYmo4SXhGYTV3blMycWpwTU1OdVZXSnFsMzJyeVpVM2RKUG5HdVhzN25Vb0paT1kvN05UQVZUT1d5Tlk5T0R6dDNmWXMzVjh0YUVWd2RoYXFJVTBIbDJ1UzNQVmRrZFVTbStNTEpDNVhCR0pkbHN6RVkwcVJyRm1nYmJKdFM4clMvV0ZCdHZGWFQ2MFpkOENQSFdtd1haclBDYkdZOU9GbUhEUmQ4SThpdlp4NEh4RTNBNTBxbzEwbUZyM0RhcE9obFM4cGxCeGYiLCJtYWMiOiI4Y2MyOGVhNjM3YWM1MjA4NGMyN2U5ZGFmMGZhYjRhMTU5ZTJmMjhmNmM2MDJhNTVmNTZjYTBhYzA2OTA3Nzg0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkZrN3I4NnFZU1RYbTZMZUYyaUNXelE9PSIsInZhbHVlIjoiTUN5c2U0M0R3YUpLNE1NOHIyYi8zbVpKM0RIbVNwbjdmQnJWVmdHdUpnWjFOcSswVy9FYXVHTjJQZVdBazBWYjZCQ0hJWjNkbURybUNocExpbXY0YnJqMFVIeXA5dmdyUGNRVG8yOER6U1JMQUVUZGZwemdiSEk5SUdqVnhqRy9GVUowWWhZQ2tWSk5MN1ZXbHY2UWlyS1BtZUdMSTNLNFBHUjJDMTZlK0Q2TjJhM2ZKYjZ1MjJOYUFlTW1kS0U1N3ZwZ0hTcnN2Nk04ZmtDREx5TFk1ekowUTA0U2VBY2tvaWh1dGI3a0tYRzVOMGZjU0dIc0s2eXU4QVVlcHJNSi9qR0lrWUExWG9zckczcSswdVd6Ty8wQ0wxM3hzVVNYdExPL2tsSFc2V3dCWk9rbU0zVmZmekhnOUlMTnYzaGlBK3ZoTGNhOVZSV2dVQ1BoYXloeXdjbTZDeXhSVGNIT20xL05FK0dpVm5pNUZHckxvamFUa3E5TnhKQUpZNW4rbE9iRktveWoxUVB3eFR3SXV6d2lBUEkvRFZCMTcrTmhhbkxJWWh5eEpYOEtKSktUN1FkVUpJWFlZU1B3aTlwaWxRYklnNFZVbkdPRWk4QTRabnNjUjJ5MDRDVTAxeVkwWnlCWWJUQTlKc1NvQ1NCOE1TVTlxcWlKVWZXakJMSE8iLCJtYWMiOiIzZTJmYTNjMjcxNjYwMzJiMmRkM2ExOGEzNDFhOTQyNGI1NWQ2MWQ1YjJhZjRkOTZjMzRiNDg0ZWU0OWMyMDBlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1261939318 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1261939318\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2146436873 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:29:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkdkQ0JTdGdOdmtuaVB2ZENjUFdJQWc9PSIsInZhbHVlIjoiN3ZEcm5yMHdNcFZBVlpRV29wN0l3S3lFVFU3bDBTY3gxTC9ybjlUUmxwempLNUdkQ1lSRlNKNW55REdZL0srVUpwQU5XbzVGZEVYTjhIemo1LzA4UlBtMTNuKy83c1ZxVUVXUlhRQUxHbnlLZlhwWFJ5b3dVVkZoUzJMcXhlZ01rTWFobTRFbjZ4TURrNC9BUmlLZmV3a25hUzF2U3VEeWFGWGd3UmdvQ29VU1RuRHpvQWdBWjdQaHVCQkFJRzlETzhVK0R4N1lXcDZVbXZXUTJLdDc5NVhUVWNrWFRucU1FUTBYWUVBck9jYkxOS21sUENUVFgySmpNR3gvS1h4cjBzTHJTN1pvRUtsdW16NEdoNHVJelluelhqSW9HMXFML3BkVlhKSVF2LzB4VENtbTU5aU9YYmhDZU9WdG5tYld0QWNlbHBLRnlqejlyUktRUUdHRFQ5VHNmY2FESVdCRm1DYi9kUFcrYUhMNnhHLzBncDF2bk15T1h5SzBFdGVDOU9lejNUVjJaQzJHYUFJaDV4eUUweHFXVkVZaEpVQ01BeVBhYXk1Vm85NUFzR0pjQm1nSXgwOTZHU0MxVGYvMXAraThsNW96cTR6RERMaHIrNFZyV1FUbHRFS3doUEN4RHFVV0F3VE5ONHpMMkpwZ0hINEVOOERSWFpBOWlPMWwiLCJtYWMiOiI3NDBjMGQyODNkMmMyYTNiOTFjNzRkYmM4ZDBlN2FhOTkzY2ZlOGM0NTMxZTg5MTIwMmZjYWVhNWFlMDA2ZGU0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:29:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik5oSzlVMzVsaWVSRmxzRkY2Nlh5L1E9PSIsInZhbHVlIjoibEJqUnhwWnFXQUFuVy92c3YwWlkxeFV5Q3hzRzlEdTkzRmVJM3VPRkhMaThUN2g0WWp1cXNwNW1MOWM4eDJLRlhzUmdaRUVzTko3WFZSZGVBRDIrYTYvZ21mSVdyZFVYQXhWcTdiZzg1MVVOdGhQZjM5Z2JCQ3JFNXFhWnZNRHRDd2d4dDk2Y2FpZ1VKWjNhTDlDUklYWW9rYzZwejBCaTBxNVZydTNzYjFSSUVySjVFU2lKNzY3SXpCV0NQUGN6blZKOXJNdUhRSTVsKzFzalBrbXhDSm40K1ZQNzVDS2RhSmdhUGpNUzVyMy9WOEpNTjFGSTc5ZnFRUUsrQmVOcWNTTjFmQnc4dkYra1lrenZtVFdxdzA3WnU1bVR2TGJjN0RmMWVib0l5NDB5aGNCdmNXWkU3QWdNREptbDd6VEduY2E1djlZaDlDUFc2YXBUbjJJU1hYemxHTWdJZjZCL2x4cEdrbnEydjFTd0pIbGkwbitLVlRRd1RiVXh1MFhZeEdBaDRDUWhvZ2w3b3pvQmF4YnBVUnE5QWs0ZlVWM2pGaHJ1MWhpcGZ0aG5YNkhtRkR4bG5Eb2t3aEVWZVlTUWlrYlVLcEF5aHdNRWpROU9RV0Z0cXprQjM3SHREU3YzNHpUWEpyYWpuVVdrdG5kMkNGMjRqRnpJUXZ5d0J5cWciLCJtYWMiOiJiNDM5OTg4MDE5ZmE3ZjU1YTg5MjczMGY0ZGUzOTYxMzkwYjZmZjg2Y2M4YmE0NWNkNjI5MWE0MDM0ODNjYWM3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:29:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkdkQ0JTdGdOdmtuaVB2ZENjUFdJQWc9PSIsInZhbHVlIjoiN3ZEcm5yMHdNcFZBVlpRV29wN0l3S3lFVFU3bDBTY3gxTC9ybjlUUmxwempLNUdkQ1lSRlNKNW55REdZL0srVUpwQU5XbzVGZEVYTjhIemo1LzA4UlBtMTNuKy83c1ZxVUVXUlhRQUxHbnlLZlhwWFJ5b3dVVkZoUzJMcXhlZ01rTWFobTRFbjZ4TURrNC9BUmlLZmV3a25hUzF2U3VEeWFGWGd3UmdvQ29VU1RuRHpvQWdBWjdQaHVCQkFJRzlETzhVK0R4N1lXcDZVbXZXUTJLdDc5NVhUVWNrWFRucU1FUTBYWUVBck9jYkxOS21sUENUVFgySmpNR3gvS1h4cjBzTHJTN1pvRUtsdW16NEdoNHVJelluelhqSW9HMXFML3BkVlhKSVF2LzB4VENtbTU5aU9YYmhDZU9WdG5tYld0QWNlbHBLRnlqejlyUktRUUdHRFQ5VHNmY2FESVdCRm1DYi9kUFcrYUhMNnhHLzBncDF2bk15T1h5SzBFdGVDOU9lejNUVjJaQzJHYUFJaDV4eUUweHFXVkVZaEpVQ01BeVBhYXk1Vm85NUFzR0pjQm1nSXgwOTZHU0MxVGYvMXAraThsNW96cTR6RERMaHIrNFZyV1FUbHRFS3doUEN4RHFVV0F3VE5ONHpMMkpwZ0hINEVOOERSWFpBOWlPMWwiLCJtYWMiOiI3NDBjMGQyODNkMmMyYTNiOTFjNzRkYmM4ZDBlN2FhOTkzY2ZlOGM0NTMxZTg5MTIwMmZjYWVhNWFlMDA2ZGU0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:29:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik5oSzlVMzVsaWVSRmxzRkY2Nlh5L1E9PSIsInZhbHVlIjoibEJqUnhwWnFXQUFuVy92c3YwWlkxeFV5Q3hzRzlEdTkzRmVJM3VPRkhMaThUN2g0WWp1cXNwNW1MOWM4eDJLRlhzUmdaRUVzTko3WFZSZGVBRDIrYTYvZ21mSVdyZFVYQXhWcTdiZzg1MVVOdGhQZjM5Z2JCQ3JFNXFhWnZNRHRDd2d4dDk2Y2FpZ1VKWjNhTDlDUklYWW9rYzZwejBCaTBxNVZydTNzYjFSSUVySjVFU2lKNzY3SXpCV0NQUGN6blZKOXJNdUhRSTVsKzFzalBrbXhDSm40K1ZQNzVDS2RhSmdhUGpNUzVyMy9WOEpNTjFGSTc5ZnFRUUsrQmVOcWNTTjFmQnc4dkYra1lrenZtVFdxdzA3WnU1bVR2TGJjN0RmMWVib0l5NDB5aGNCdmNXWkU3QWdNREptbDd6VEduY2E1djlZaDlDUFc2YXBUbjJJU1hYemxHTWdJZjZCL2x4cEdrbnEydjFTd0pIbGkwbitLVlRRd1RiVXh1MFhZeEdBaDRDUWhvZ2w3b3pvQmF4YnBVUnE5QWs0ZlVWM2pGaHJ1MWhpcGZ0aG5YNkhtRkR4bG5Eb2t3aEVWZVlTUWlrYlVLcEF5aHdNRWpROU9RV0Z0cXprQjM3SHREU3YzNHpUWEpyYWpuVVdrdG5kMkNGMjRqRnpJUXZ5d0J5cWciLCJtYWMiOiJiNDM5OTg4MDE5ZmE3ZjU1YTg5MjczMGY0ZGUzOTYxMzkwYjZmZjg2Y2M4YmE0NWNkNjI5MWE0MDM0ODNjYWM3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:29:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2146436873\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-15******** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-15********\", {\"maxDepth\":0})</script>\n"}}