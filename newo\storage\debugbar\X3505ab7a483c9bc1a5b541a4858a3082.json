{"__meta": {"id": "X3505ab7a483c9bc1a5b541a4858a3082", "datetime": "2025-06-08 00:58:05", "utime": **********.353667, "method": "GET", "uri": "/add-to-cart/5/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749344284.697782, "end": **********.353689, "duration": 0.6559069156646729, "duration_str": "656ms", "measures": [{"label": "Booting", "start": 1749344284.697782, "relative_start": 0, "end": **********.224032, "relative_end": **********.224032, "duration": 0.526249885559082, "duration_str": "526ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.224044, "relative_start": 0.5262620449066162, "end": **********.353691, "relative_end": 2.1457672119140625e-06, "duration": 0.12964701652526855, "duration_str": "130ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53599432, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1322\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1322-1579</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.0081, "accumulated_duration_str": "8.1ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2744691, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 45.309}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.290334, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 45.309, "width_percent": 9.012}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.313105, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 54.321, "width_percent": 12.963}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.317627, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 67.284, "width_percent": 11.235}, {"sql": "select * from `product_services` where `product_services`.`id` = '5' limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1326}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3252509, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1326", "source": "app/Http/Controllers/ProductServiceController.php:1326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1326", "ajax": false, "filename": "ProductServiceController.php", "line": "1326"}, "connection": "ty", "start_percent": 78.519, "width_percent": 11.111}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 5 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["5", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1330}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.332027, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "ty", "start_percent": 89.63, "width_percent": 10.37}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-983737662 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-983737662\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.323929, "xdebug_link": null}]}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:1 [\n  5 => array:9 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"id\" => \"5\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"originalquantity\" => 23\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/5/pos", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1028630299 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1028630299\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1433472449 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1433472449\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-75277165 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i31pha%7C1749344276347%7C8%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImZPMHBTMGFDbXFKV0M3NkRReTBFWEE9PSIsInZhbHVlIjoidjJPTEFPK01xcmhWNXdNcVI5MGs5V0FQS0VXTWRlUFpIRnBhQmRuSllyUG5Fci9YaFJuMjJ3MVJHRHlXSmRqWjdvcUNzMnJJcUg5czAyOWR3U0RFNmZxdjFOelVnbWE1QmhuTXh4eVl2ZU1DUmk2L0dUU2ZwTHhCNXZ0bnJENGtXZ3ZHTEowaEpnWk12czJaWnpITzF4SVNabVRwQWg2RGhPR0Z3d0hhaGpJUE9nS0NKdVE0S0x2cjczQVRqTGxIaGpLOStGMnFwSTc4MGJDTXZoUkVHdUZ6b2c4c25iVGRIV3doZ0phazZHOWtzVXQ4cmZrZzVjYldXTEFzWkFiQzVCQ0lNUm5YM1VhT05GcEJLU0NxS1VobW1JejRYNXJ1SGEydkQxSFNaQStSRTFRd1RzeVQrQ2QreHhNTlNKNWp1SE9VeCtvUy9sRmcxYTJiMjRiWS9GRDhhd00raWlKZlBNR1V0RjdTNEMrQnhSQVh0UllzQzFBbXJBQnF5SDB3K1EvUnBHVURUNkYwaEhoajhHOEpVWUV6Vld3Z3ZrSXJYTUcydXpYOXdyTkNKM0dxNk93NUI3aXE3SlFmb2JOMWQ5SUdFMk9BdklIZDEvNGNQVldEdHJZWmtLc0Z2eDNsOFE2QWhmN2crdlhZdng4SGVLV25ra2xPWXZab0l1ZDkiLCJtYWMiOiJjZjM2M2EzNTQ2ZTM2M2U0NTZjYzcwZGJmN2Y2YzU2MDkxYTE3YmE5MGM0YTg1MDg2ODdhYmRlMDM4N2E3YmExIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkFnSGkzOG45NnBSdHltRGxKbUo0Qmc9PSIsInZhbHVlIjoiMWZmK05HcXdud25Za0ZwaE9mNzZOdE5GUDdQUVBFYmk2eTNWcFRCSjBSbkR5bmluUzE1SGN0a3JCbUNxNUIrS0NucTBDSnhLcnpvWEhtL3FReXlDNnFnMEltc1h0MVgxSlhtenZFSWtqS3B0Q2R3dnpkWDluUDFwblpuVTBhTDhyYUxPbmFQSTlkc1V1YnRiVVdWTEZXQm16QVE1MzdubzZJUkZOTi9vR1JIOFJTTWNyVEVZNnl4cXZERXpHVktsY0UvQytKQXB3NDhid1ZkQzNLK1VwSXVERnIrY3c1c3B3V21EY3JpdTI0VGRJaWFJVUZCUHNOcWZ5NWxLN3FPZkZWN2IvaXhPWGt0Wk9HRi9DaEw4T0p3UkVkWGJYb1VjTHZwUmxZaWplSWdiQWdUbW9UejlQOXRvQzVPTVRIK0hqanF4VVMvdHo5cXpBMVBqc0F2d3BKdWtOWFBwZFQySTdVK2Y2S0FaOWVMT2xiemhsQ29SakNPZzZvUDllSmxnRnc3NUhFMk1aYXNIUWtPeGw1NWM0STNmb3dLUEFoeXBRdllzcUNoK3FmbTJYcVF2Z2pFc0lUT1poWUk3MEtkRlUvTUtjTSt5ekN6ai9Ma0FRdVRxbTVCMkN0TXNXbGtLQlloVE84aThONlFLL2FlT2FhcFNVR0U3Y0l0anNNaTUiLCJtYWMiOiJkZmQ4MzI0N2NmZTM1NGU2ZjIwYThiY2JmYTI1ODA0MGEzNWI4MDUxOTkyNmExNzc4MTMyZmRjMzEzY2QwNDgzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-75277165\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-893484824 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-893484824\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1186099143 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:58:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdaR3NRbHVvenYwcXFRM2dsWnordUE9PSIsInZhbHVlIjoickhuRmpseitCY1kzQmRSazVmR3BPQWRUYldhVjVvbFNJbjhZbXBsNWJCdnFUUDlsNDlycHpueWxuWEozY3grODR0YThFdWVyMFNvTElwd1FsWVVpaFlSYWxDQzNLSWZkRm12YWJibUo1Y2F1WlJVcFRjcVVKK1JXWExwSzhrTWYyUjVaM3FNbmM5RElEVis1d04wR0tITlNWTFVtNUNjaE5xM3RJcGpsL2lvTU9GbGZXMW4vamFnRTl2ZTVQM1M5blpaVXE4U0RkMVRiYXZXZVJud0ZmMlVBSUdheHZ3bWhsZHJrMGt0S3FqMHpFMDgxUEh4K1Z3amZaRVliOE5mbDdmZDUvck5VdzNxNXJDL0g0YkVvZExScnZudDlLdTgwTnk5c0xQQVBkQ0FRUGpYY0FYRTh0a243b1k3c2xGRTFLNC9jUUt5MFpTNkpwQS8xa01vd3ZDcnRQUkxmQWpjaUJUTFlQMVNWUk1sMmhURjM5ZkR0Y1NuU3dCdWRhTkRuaE83eXk3MTdMYk5BSmU0ajl3aFovOXRNSXpqNVBKdUpZdkszYlBHOExEMjRzN0ZTZnB3bmR4YUlqdGhTTnFScjNtNS9oYWVncS9lM2dMMW1wVVg2VWF2N2p1cWowQ3YyTUN1QjlMOEUyMTg5NU0zdkZlQi84bFE2TCtVd2NkRlUiLCJtYWMiOiJmMzEwZWYwZDFjZWRkODA2ZDdlZWMyYzYxZGQxNTc4OTQ4MmYyZDRjMmI0ZDRmZjU3ZmQ5YmI0MmNjMmUzMDBlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:58:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ild6Zmh4Q0UwSkxSM0FXdEVvWjh2cWc9PSIsInZhbHVlIjoiMFhRVTVWeGtXUHhIOWFMMXlCOE0vV1c5Q3M5R3lRU0pNMnVCSWZjSFBhVm9rTmo5WUZvdDhsVGRKd2FhSnU5UUF0WTlESG1yeU9lY0FBQmtxQkRJK1FKTnRNTmw0R1ZRMXpRcTBnVTJ3bm5iajNiVGFlMHNwQndFSmlIVmJocldKdDNTTFhMTnpqTGdzMHd2QmpXSzByKy9jUkZ2dmJwNlFXYytkK0hRT2NsQXhWTExYeXlaRmVLdkh2cHBPeGtyKzZmbnlqNXMva3lML0VpcEplU0hST0ZFQVAyM2I2Z2ZveE13Vk5QeWpXV2pWZ1NuZUxqZVpkeEdoNEZnTGdaOFpyYWN2aWlRVUV0SkV4WWxVdmZ4bnNIM28xTGNDSjA4c0p4RnZIRCtCellCaDAvazc1cUYydVR2YitsV3RNSzlCMTVJd0YyWng4Y2d6SmQ0YUl2SFdBenloQ2R6QU4vd3VrbDU2ZnU3c0ZQVXk4NFNoUUxBTm5meTV4ZGRDVFVpbzVOaXRWWEdTcG5FWGdJOWd6c3EvY2pYY3pVdFRNZit4Y24xMzM3eVRZS0ZaZmVwcW5kR2J6SWRsNUF6cUQ5U0NaZjUySGtPdXRCY09PUE1kRUkyS09MSjdtdUZQdUE4VEV0U3F4eUFtcE5TbExwM3pzNElVT2dPYjhHQitzYUsiLCJtYWMiOiJhOGU5Mzg4M2Y3ZGVjODFmYzU2MTBhMWZmODQ3MzBjMGQxMmU4OWFjMTA0MmY1OTk4NTk2N2RhMjVhZTlkOTYwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:58:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdaR3NRbHVvenYwcXFRM2dsWnordUE9PSIsInZhbHVlIjoickhuRmpseitCY1kzQmRSazVmR3BPQWRUYldhVjVvbFNJbjhZbXBsNWJCdnFUUDlsNDlycHpueWxuWEozY3grODR0YThFdWVyMFNvTElwd1FsWVVpaFlSYWxDQzNLSWZkRm12YWJibUo1Y2F1WlJVcFRjcVVKK1JXWExwSzhrTWYyUjVaM3FNbmM5RElEVis1d04wR0tITlNWTFVtNUNjaE5xM3RJcGpsL2lvTU9GbGZXMW4vamFnRTl2ZTVQM1M5blpaVXE4U0RkMVRiYXZXZVJud0ZmMlVBSUdheHZ3bWhsZHJrMGt0S3FqMHpFMDgxUEh4K1Z3amZaRVliOE5mbDdmZDUvck5VdzNxNXJDL0g0YkVvZExScnZudDlLdTgwTnk5c0xQQVBkQ0FRUGpYY0FYRTh0a243b1k3c2xGRTFLNC9jUUt5MFpTNkpwQS8xa01vd3ZDcnRQUkxmQWpjaUJUTFlQMVNWUk1sMmhURjM5ZkR0Y1NuU3dCdWRhTkRuaE83eXk3MTdMYk5BSmU0ajl3aFovOXRNSXpqNVBKdUpZdkszYlBHOExEMjRzN0ZTZnB3bmR4YUlqdGhTTnFScjNtNS9oYWVncS9lM2dMMW1wVVg2VWF2N2p1cWowQ3YyTUN1QjlMOEUyMTg5NU0zdkZlQi84bFE2TCtVd2NkRlUiLCJtYWMiOiJmMzEwZWYwZDFjZWRkODA2ZDdlZWMyYzYxZGQxNTc4OTQ4MmYyZDRjMmI0ZDRmZjU3ZmQ5YmI0MmNjMmUzMDBlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:58:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ild6Zmh4Q0UwSkxSM0FXdEVvWjh2cWc9PSIsInZhbHVlIjoiMFhRVTVWeGtXUHhIOWFMMXlCOE0vV1c5Q3M5R3lRU0pNMnVCSWZjSFBhVm9rTmo5WUZvdDhsVGRKd2FhSnU5UUF0WTlESG1yeU9lY0FBQmtxQkRJK1FKTnRNTmw0R1ZRMXpRcTBnVTJ3bm5iajNiVGFlMHNwQndFSmlIVmJocldKdDNTTFhMTnpqTGdzMHd2QmpXSzByKy9jUkZ2dmJwNlFXYytkK0hRT2NsQXhWTExYeXlaRmVLdkh2cHBPeGtyKzZmbnlqNXMva3lML0VpcEplU0hST0ZFQVAyM2I2Z2ZveE13Vk5QeWpXV2pWZ1NuZUxqZVpkeEdoNEZnTGdaOFpyYWN2aWlRVUV0SkV4WWxVdmZ4bnNIM28xTGNDSjA4c0p4RnZIRCtCellCaDAvazc1cUYydVR2YitsV3RNSzlCMTVJd0YyWng4Y2d6SmQ0YUl2SFdBenloQ2R6QU4vd3VrbDU2ZnU3c0ZQVXk4NFNoUUxBTm5meTV4ZGRDVFVpbzVOaXRWWEdTcG5FWGdJOWd6c3EvY2pYY3pVdFRNZit4Y24xMzM3eVRZS0ZaZmVwcW5kR2J6SWRsNUF6cUQ5U0NaZjUySGtPdXRCY09PUE1kRUkyS09MSjdtdUZQdUE4VEV0U3F4eUFtcE5TbExwM3pzNElVT2dPYjhHQitzYUsiLCJtYWMiOiJhOGU5Mzg4M2Y3ZGVjODFmYzU2MTBhMWZmODQ3MzBjMGQxMmU4OWFjMTA0MmY1OTk4NTk2N2RhMjVhZTlkOTYwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:58:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1186099143\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2077613079 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>23</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2077613079\", {\"maxDepth\":0})</script>\n"}}