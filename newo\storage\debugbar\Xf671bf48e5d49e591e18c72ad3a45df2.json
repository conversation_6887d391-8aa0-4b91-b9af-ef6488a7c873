{"__meta": {"id": "Xf671bf48e5d49e591e18c72ad3a45df2", "datetime": "2025-06-08 00:40:46", "utime": **********.687141, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749343245.976254, "end": **********.687161, "duration": 0.710906982421875, "duration_str": "711ms", "measures": [{"label": "Booting", "start": 1749343245.976254, "relative_start": 0, "end": **********.577401, "relative_end": **********.577401, "duration": 0.601146936416626, "duration_str": "601ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.577417, "relative_start": 0.6011629104614258, "end": **********.687163, "relative_end": 2.1457672119140625e-06, "duration": 0.10974621772766113, "duration_str": "110ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45037680, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.028510000000000004, "accumulated_duration_str": "28.51ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.622246, "duration": 0.02594, "duration_str": "25.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 90.986}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.663974, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 90.986, "width_percent": 3.157}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.675215, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.142, "width_percent": 5.858}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-635760236 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-635760236\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1255254360 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1255254360\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-549935944 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-549935944\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749343239542%7C40%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImI3TzluaUJQVW1LV3FBRlA4R2Z6L3c9PSIsInZhbHVlIjoiUUw4bCtybDhDMXRWNFV1c3RuZlNoYUtnVGQzSUdURnBVQVNPRFNkelRrTVliOTRMMTlpWTBRY0VGQTdHbEFMdEIxeFphNURTNnZnaUd5bTNEUVN6UDcwb01mVUcvU0E3Wlh2cTZGbE5vLzRvd2tmWjZrdnBrVkdJd2FBQW9NZzZtNWxXNjMzMGNjQXNyQWM0eVFTRkFOTE1uT2dOOThPUzUvaFhuSlJyN1B4MHNTRWNXQldyRTQ5SmxveHpBZ2NCRDMyMHdjeFlqTHRML1Z4UnhsWjE1UDdvZW85QTVmZ3lJakZEamR2RTlFUlZkMkdtN0R6a3RkcnlsR2R3cXNiRzNSNUVNNDR5bG9zN2l0VkhHOCtHUlJlWmc5MUxlcksrblkzTERPTDZjeUJIU0lqdVp1U3I1MlRWazZQODVaeG1IeEdzcTlxdEhoVHdMeWJRZmpmY2grdmUrc0pjdUpLMEgyOVNJU1BTVC9Jb2t1bEs3UlFDa2ZvR2ZHbW4zSXp1U0ExNEp5Tko0L3BYSXpXdVNrRE1meXdsTU1HZCsxVGtZUmFVcVBrQnFHM0tCdUViVzFpUXBrTFZ0bCtJb0dxZjhQWnpQMFhaMVFwSXYveUlyOVNaeFlKN3FKYVF4T09QbDVqK3gvSjB0WlZEcmZwd0YvMkNEaVRRQTVmSHZ0WW0iLCJtYWMiOiJmOGVjZTRiMDk3YzA1OTQ1YzUyZmJiN2U2YzM0MzU3ZmFkYzIzOTg4YzI4MjIzNjk3OGYyNDhmODVjZDlmZDM3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImY4N01ocW1oS3huSDRkZ2ZybXpTNGc9PSIsInZhbHVlIjoiakRQZXFleHd2bEd5cXRqYW1ueCtCY0JyTEdxM0EvSG5NSHpwMlJrQXB6ZUoydXlPU2RYTXhSdkpWVVAvb1ZMY0ROQ3lvSUQydEJmTW5HNFlqTDJpWVRvSnBJQ3BwYjBiOGpOcXR4ZjhCRHlFQ095c2tWa3FmbVBoQWphTFNSY1NuNWszL1lDTXdic0J3RGxLUmQzMjZZM2IxSjQ1TlpYZWpVdStlT1lyMDRBSVAzMzAyTFhKLzRBMmJDNVB5c0JqcjdxY1JpbjNyQUdkUkpRVVNvdlpvVmpVWDlQRnJOb3NZQkR0VVlwcUczbmRMMk5EM1Q1dEVqa2w3bDVURGE5MmJOYzgxSitobm1CYVVCdjB1emFrb25mN1RUOENBdnoyZEg2Z2dWQ1d5UW0vYVowcU5EMjBpM0lQU0oyVTBTVVNPVVpzTzhPWURzN3huTFZoQnJjY3dKNEwxdHptdGl2bFJBc0M0bFI5VkpDTGs3UjhaWDE3MVg0bEdXR2hSOC9hNElWZmV2S1owSnhnbUl4Q2RWMTVBU0JUa0U1NHJ4amtoSXB2L29ZWkdpWTh4bllwN2Q4Vm9oUlNkSWpJQk1qb2oveldOaW9NSk9iZ2VXTERPUHE2Y0dqTUtnczBJRGxyREtEZUg2aUE4ZVhNbEM2M3lCUHZRR0xja2xZSDdjVWoiLCJtYWMiOiJmMjQxNDVmOGZhNjZhZjhkMTFkNDY2ZWM4NWJlNGY3MzYyMTdmY2E1MDhkNjU0YjBkYWIzMzFkNDdlNGEzODFmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1984492595 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1984492595\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2134795715 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:40:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjV6bk9Xdk90TlljY052cFJ1eXlscnc9PSIsInZhbHVlIjoia0E3bGZzTzlzU0xwVTZ0YW85bWdKeU5vd09lT3QwRlFzQ0lKZlZZMDRoK0RpRnVIZXUzVzlDYWtQWFh4d28zZFNCR1JIVFdWeC9LeE5BMDcvSGhiSjdTcTZzbXh1Ny91Sjh5ZC9ZSVVJcStMbTkvWlA1d2xQRlN0NnROVlFVTlVYMmlxcUFhRWF5cjZScW9ITGxCK1prK3UwRmh6TmRJbU9kOHNRbi9uOEZMaU1qdzJLUXdtTVFZQnBwTVBwckZ4VG1KWXEzQVM1eWN0TldJUlBMMHI0N0w1cGt2VjFhV0JxOG9mVThCU1ZQTWhwY0lNKzdmNzFJZ3ZQemVNM0tvT0kyUkNRamtoN3B3SW5kZzVpRUszVlN0SHVJSFZmOTNTWExUeXFnZ0NuSnI2Y3cyNmZiUWdZVUMyMUh5Y09aSnJWZGFCYzkwTEhLaVUwWGltQ2VLdXBrYUR0SyszdUN6eVE0blJuUzJWc0J1NnhrZ0lDS0Njb0s0aEo1clhVUSt4TlRLMlpobTJEa1hsTUo2Yzl0L0dxb3Y0aWlRSjc4ZGhQdWlab2NTeGVZVXgwZ2plVzdXZU0vb2lUcjk3Ylo3akNDVGF4S2N3dlgweDltRVdJaDFibndFZGhSRWFESXB0MDdPOFk4N2J4bFdxYkN5QlRncVBwbWVoNFBCTmJsdFQiLCJtYWMiOiJiZWU2OTU0NjExMDkyZmFlOTYxZDFlODg0MjZiMGFiNDRlOTM2MWEzNmZkYTZiOTNmYjU5YjM5MDM2YTkyZDZjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:40:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im1pUHQvYW91T2VvcUhSTU5QWEVCcFE9PSIsInZhbHVlIjoiVHFqY25OUWd3QjU0UnVuYWxNbkVlZmN0R3FSL3o0dnpZM1JONGF6WjJNWkVnQzlXTlMySWJFeFkvQ3R6RFp3YURBZzJmcGdjbGtGb0VrT2RrS2l0d0JKZTZmaWdyS2l3aFBHRjhoOHYyUlJST21pMktQOEszRGJ6c1cwZlFDa2VOSjFXSkRtNzMrR2Ztc3ZjVEVPRy9YWXg2Y2J0MGJpY3p2RmgrbFFDeTBlNGh2NWpjT1puR3FjZ3BLcDM3b3lKNFphcFoxQkZPT0xDZlhUaFpPOURJS09BK1NLeW5CejkwVktxYWRneTFQZXhDdU9iNDdIZzQ3cGxtZWx6aVQwblY1QXFRYnVodUZLM1VjcEhhTXBtSG9MWnBJRCtDMG1YQTZGY2ZaZ3ZBUnN4WHZoVmo2MEIvekt6RWdBS21nQXFkdWg1ZzRUSlNSTlA3ZzZVZDFvWDlYU1lPZ25BUXd4VHl6MzlmcnJxaUsrZDAvN0h1TXdRL09yQ1hMN1pobHl3OHl2Y2h0d3lMbkk0QVYyYUNRS251bkhxRUVwcGp5aytjbWl6RGExVlYybWI1L1dWYTFZaEVLRUI0d21XU0tpWEh5OGFRN1FJRXBPZmJKcXBDLzNpVFpvbmwyTGoyMGJGOHpDMmlXUnhCTUFGWVFKQ283TXlGWGJGOXY1R1cxL2giLCJtYWMiOiJkZGIxYzAzNDIwMTNiMWE2YmQwMmUzNmY4MWY5MDk3YTMxMTYwMDhiZGNiYTczYWQ3NGM1NDZhYTZkNmU2ZTIwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:40:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjV6bk9Xdk90TlljY052cFJ1eXlscnc9PSIsInZhbHVlIjoia0E3bGZzTzlzU0xwVTZ0YW85bWdKeU5vd09lT3QwRlFzQ0lKZlZZMDRoK0RpRnVIZXUzVzlDYWtQWFh4d28zZFNCR1JIVFdWeC9LeE5BMDcvSGhiSjdTcTZzbXh1Ny91Sjh5ZC9ZSVVJcStMbTkvWlA1d2xQRlN0NnROVlFVTlVYMmlxcUFhRWF5cjZScW9ITGxCK1prK3UwRmh6TmRJbU9kOHNRbi9uOEZMaU1qdzJLUXdtTVFZQnBwTVBwckZ4VG1KWXEzQVM1eWN0TldJUlBMMHI0N0w1cGt2VjFhV0JxOG9mVThCU1ZQTWhwY0lNKzdmNzFJZ3ZQemVNM0tvT0kyUkNRamtoN3B3SW5kZzVpRUszVlN0SHVJSFZmOTNTWExUeXFnZ0NuSnI2Y3cyNmZiUWdZVUMyMUh5Y09aSnJWZGFCYzkwTEhLaVUwWGltQ2VLdXBrYUR0SyszdUN6eVE0blJuUzJWc0J1NnhrZ0lDS0Njb0s0aEo1clhVUSt4TlRLMlpobTJEa1hsTUo2Yzl0L0dxb3Y0aWlRSjc4ZGhQdWlab2NTeGVZVXgwZ2plVzdXZU0vb2lUcjk3Ylo3akNDVGF4S2N3dlgweDltRVdJaDFibndFZGhSRWFESXB0MDdPOFk4N2J4bFdxYkN5QlRncVBwbWVoNFBCTmJsdFQiLCJtYWMiOiJiZWU2OTU0NjExMDkyZmFlOTYxZDFlODg0MjZiMGFiNDRlOTM2MWEzNmZkYTZiOTNmYjU5YjM5MDM2YTkyZDZjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:40:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im1pUHQvYW91T2VvcUhSTU5QWEVCcFE9PSIsInZhbHVlIjoiVHFqY25OUWd3QjU0UnVuYWxNbkVlZmN0R3FSL3o0dnpZM1JONGF6WjJNWkVnQzlXTlMySWJFeFkvQ3R6RFp3YURBZzJmcGdjbGtGb0VrT2RrS2l0d0JKZTZmaWdyS2l3aFBHRjhoOHYyUlJST21pMktQOEszRGJ6c1cwZlFDa2VOSjFXSkRtNzMrR2Ztc3ZjVEVPRy9YWXg2Y2J0MGJpY3p2RmgrbFFDeTBlNGh2NWpjT1puR3FjZ3BLcDM3b3lKNFphcFoxQkZPT0xDZlhUaFpPOURJS09BK1NLeW5CejkwVktxYWRneTFQZXhDdU9iNDdIZzQ3cGxtZWx6aVQwblY1QXFRYnVodUZLM1VjcEhhTXBtSG9MWnBJRCtDMG1YQTZGY2ZaZ3ZBUnN4WHZoVmo2MEIvekt6RWdBS21nQXFkdWg1ZzRUSlNSTlA3ZzZVZDFvWDlYU1lPZ25BUXd4VHl6MzlmcnJxaUsrZDAvN0h1TXdRL09yQ1hMN1pobHl3OHl2Y2h0d3lMbkk0QVYyYUNRS251bkhxRUVwcGp5aytjbWl6RGExVlYybWI1L1dWYTFZaEVLRUI0d21XU0tpWEh5OGFRN1FJRXBPZmJKcXBDLzNpVFpvbmwyTGoyMGJGOHpDMmlXUnhCTUFGWVFKQ283TXlGWGJGOXY1R1cxL2giLCJtYWMiOiJkZGIxYzAzNDIwMTNiMWE2YmQwMmUzNmY4MWY5MDk3YTMxMTYwMDhiZGNiYTczYWQ3NGM1NDZhYTZkNmU2ZTIwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:40:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2134795715\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-455776732 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-455776732\", {\"maxDepth\":0})</script>\n"}}