{"__meta": {"id": "Xc1ad6c958caccebc5cd0168974a70ec9", "datetime": "2025-06-08 01:05:14", "utime": **********.24913, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749344713.368845, "end": **********.249163, "duration": 0.8803179264068604, "duration_str": "880ms", "measures": [{"label": "Booting", "start": 1749344713.368845, "relative_start": 0, "end": **********.12711, "relative_end": **********.12711, "duration": 0.7582650184631348, "duration_str": "758ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.127125, "relative_start": 0.7582800388336182, "end": **********.249167, "relative_end": 4.0531158447265625e-06, "duration": 0.12204194068908691, "duration_str": "122ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45040224, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.019430000000000003, "accumulated_duration_str": "19.43ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.184103, "duration": 0.01802, "duration_str": "18.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.743}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.2212949, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 92.743, "width_percent": 3.86}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 23}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2339032, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.603, "width_percent": 3.397}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/sales-analytics\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-184829345 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-184829345\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-168916754 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-168916754\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1591925847 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1591925847\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-554431851 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749344699171%7C48%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjE0RTlSZDkvU2krZVA1T3lQS0VQK2c9PSIsInZhbHVlIjoiNGl3dmJ2K21MWC9TZjNTVS8zdEpURHRaSWprVitybnlvMGp2RnVPRkQxckZGQ2NpMXkzR0N3V05WQkFBUWxWSXg5a2I2ejlxWmlrdDRTTStNV3JUb2d3dUI2b1RKOTJGLzhHN3FxbkVSR3NuTW1QaExGRUY2MU55Rk5UM3dwMHRrQTNodVZWU1RNcm1HT2hZZnlJaGw3M2Fsd2FXWUVuWUpMakJuVXRKcGFSdEVVSEQ2K1dYWTBJcTVlU3dFbjh3QnMyRXBSYVZpVlRUQkpxVlVjcnQyWjFIT0RGM3F2aVZtQWJGZTZKNmZwb2l0elNIbFpTZTZVNmVMWlJKZ25aTUQzdzQ1Z0crRFRwN0xrWlA3QjM5dWs1TVNtRldvL0NpdzliSk5NOU1VWUV2elpuUmM1clIyeXRWRW4zODA0RVlzUFF1TExWWkU4RWZxQVo4OXA4cU9tanJWVnJoenp2TXNyaVlYSXVOaHRoRnI2UjRKdllYZkMxdStBUUlrckU1K0FoOUg1ai9mQ0hSWGd6Z01seHBzN1N3WmpuSS80eTZ1b2lqak5BSGErRCtpeGR5a2RTYS9vbklHUkovMUpWa0xwcnpoMVErRDZid2lmcVpRWGJ4SVo3Tmc0ekNoSjE0UWFmNS9mYzlYSGZrc2Z5c0J1Z053UVkvZ2QrK000TzkiLCJtYWMiOiI4MTFjOGQyN2VmZGU0ZDVjYWY4ZmNhZGNlYjNiZWIxYjdlMDQxNjMwNDE2YjdkYWM1NTdiNDBjNWNmZDQ5OTBlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InYvYWFFemw0ZlE0UTZtclRqUlZuVkE9PSIsInZhbHVlIjoiSDZaV1FYNkhjZ3g3SGRHL0tEeU9BdXY0U3RwdDViRm42ZWxpZlVuTGNhbHhyc01KVVNJS2ozK1FDMVVZMnl2Sm5EbXVVSHZ4a0JCM3dZOEt6aVFCNzdmbkwvLzFPREc4VUVuTzdtK1JXaDRjN2FsWjBRZzlJSGlBQ0xrajRGdXlnencrODRjS3Y3S3crVUtoSmxxNnFmQkd1dVc1eFdjM04reks5RlZnNjRMalg0SGZCWU5CVVNDcnJpTStnSUMvR3VUSGpEM3BxeEpNRnAzZG00Q2JlMjVUck5jQ3E2SmNOYmxiT1QvRm9KWGJRZk5BeUZtcTQwaU9YSmdUVVRDdUFYenhMMExIUkhwYzRWZTU5NmtKMUpUNnQydWV0elozTlpoV2tMTmFwdkd0ZGx4WmJ1SGc2ZXpqRDBOYytKeXl0RHh5SFZRamZGY3MwNWlaazFkQTFBUnU1RE9CY1JlL3A5VFIzMEhJVTFzMnFpVGhlSWlQOFJYWXA0SXIvSzdSVHlpVzNEQlZ0ZllGb2xWazhyMURHWTlsZ3dHZGxQQUxacUdBVWFvTVV4SmhBTVdaN3EvTGNzNEhhVlFmZ0RSQXhBTDZqeVJqSWUxa2Q3S0tnanovUFZJTzQyQkI0OXYwbVRvK20xay9uMytWRTRPSUhPQ3k3ZWlkRzZ6UEF0M24iLCJtYWMiOiIwNzA5MWEzODVhNmY0MDI5MDdmYjVjOTJiZWE2M2JhNTY5Yzg3ZTViM2Y0NTFjYjY3ZWMxZTA1NjBkMjllMTQxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-554431851\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-174589326 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-174589326\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-341774428 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 01:05:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkRuV0l2em5NU3RZVllaWGhtWXhyZlE9PSIsInZhbHVlIjoiWld0NGxlcjZLY012V253WmxEcGprY25CNmpQM0xhUjdua1pZZTlJMmxLZ0FndnQwUytrVmpIQm1BczVLOSs0N3BXVGdUS0kwRkV4ZEVNRjUrN3NUdyt5S294cnhJbDVrZnNxNDZzaUFjV1ZSRVd0MkVxQWpaUzJVTG1MbDBIckpwSmVLN2RmdTgwWGh2Ym5UaGpnSTVyeHllWTdKREhTemNGN2VhazV3U3kzanBnVHNacExLN1lmQmhzUGpDSlpNb2IvUGNMbDRJZmFOeVF0aUFwVFd3NSs1STNvczl5TnY5MS83T1FMMkl0Rk1IdFpTUXVORy9mOWJUWVg5M0ZJc2dnQSsvYXlvclFoS3NBRzVrdFRWQ2FZUDV3WFd4SFZBbWxTb2k5VzAyRmkzOUV2UlEzL1pWUDhGRmFadzl0RzZ6eHB5SXZjSHE5ZGsxN1lZZ0xqYU1nNjRHWVJLSzhtRkZXbnF3a284VWNQdzJ1TEo3TXJrU2RGL21XUkRnTnk5ZVhyOEU2L05lbHdha3UrRi94S3c0VVBSLzMrTE44K3YvOS9vMEwzZkdsbzRvdjJnaG5QY0sraTRad3lFMW5rZnVTNVU5cDlrOC9MY0VTcEN5amJYV3EwU3lUaEhBREd4SnJtN1AxS3ZYZUIxTXNVY0RFNlEveE5lUHFBWk9vRG0iLCJtYWMiOiI1M2M1ODNhMTY1MjA3Y2NjYTZmMmNjOGVjZTEwZTMwZGJkODRjMWExYTVkMzQ4N2IyNmIzZmQyMDdlM2Y1MGZkIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:05:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IktoSWJoOUV4eGRUdS96WGt4Tncrcmc9PSIsInZhbHVlIjoiSHZ3YTVuUW4zNmg5eHMxOFZIVkkvS2w2bGpzUlNrQURGOVlJRnZuK1BUYXVDYWVtb3RjbVVQWVRPSTlXNkcxNWpYeDJtZGVULzdEMEFKaXBnY2F3VUtvZld4K3RiVitzSnMrWmFTZ3FtcXVUNmhYK0NZT3hJejNXN3BGZGU3bHI3Rm9Ia0FzRHhWNG1hdTI3K2RHUGtGSUJBaXFzYjlIclJQbW9TaEFpa0s3aDhCSCtCWEE2eExWbDd0UjVsdURSa21WWEQ4R0QrNFJ5MTRYTSs5VlZuT29oY1JRVkpNTEtjWlMzSER4RHNrRmtDcTJFaHZxQ1NHR0JQRzNsZTkvU21SQk04Tk8zMFhyaGhSLzBFd093b0lwU0w2WUdMb1VNZzNQa1J3QVlYZHhRU0h2N1JEVTB4enJiOXhiVGIvWmpvd2IxZ3c2MXZKdm5WbUhOdG9BVkgzTlNyTldOZ2s1TlVTemk5YWpJU0FZOUNBcC9DWVpJUFJYdzdMSWQwRHhZS0NXRndHUEcyR2c5SVFZRnk1NS9MTXRGaFppYTdiOGcrZmw5MmFHUU53SnFEUW5vb3JFamxrZzlwMHpCbC9ZTWlTWUg3TDZVMkViRFZMajVvQzRBd1VlSUIzaHg0UnBnZmo5ZXhCcnZyQldvN3JBNCthek9BMmFPTCtva1N2cGkiLCJtYWMiOiIzYzkzYTM4NjA5OTEyYzRhOTcyMzA3ZTNlOWVmMzc0MzJiNmM3N2UzOTBjMWUyN2JmMWQ3MjkxNmVmNTZmMTk2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:05:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkRuV0l2em5NU3RZVllaWGhtWXhyZlE9PSIsInZhbHVlIjoiWld0NGxlcjZLY012V253WmxEcGprY25CNmpQM0xhUjdua1pZZTlJMmxLZ0FndnQwUytrVmpIQm1BczVLOSs0N3BXVGdUS0kwRkV4ZEVNRjUrN3NUdyt5S294cnhJbDVrZnNxNDZzaUFjV1ZSRVd0MkVxQWpaUzJVTG1MbDBIckpwSmVLN2RmdTgwWGh2Ym5UaGpnSTVyeHllWTdKREhTemNGN2VhazV3U3kzanBnVHNacExLN1lmQmhzUGpDSlpNb2IvUGNMbDRJZmFOeVF0aUFwVFd3NSs1STNvczl5TnY5MS83T1FMMkl0Rk1IdFpTUXVORy9mOWJUWVg5M0ZJc2dnQSsvYXlvclFoS3NBRzVrdFRWQ2FZUDV3WFd4SFZBbWxTb2k5VzAyRmkzOUV2UlEzL1pWUDhGRmFadzl0RzZ6eHB5SXZjSHE5ZGsxN1lZZ0xqYU1nNjRHWVJLSzhtRkZXbnF3a284VWNQdzJ1TEo3TXJrU2RGL21XUkRnTnk5ZVhyOEU2L05lbHdha3UrRi94S3c0VVBSLzMrTE44K3YvOS9vMEwzZkdsbzRvdjJnaG5QY0sraTRad3lFMW5rZnVTNVU5cDlrOC9MY0VTcEN5amJYV3EwU3lUaEhBREd4SnJtN1AxS3ZYZUIxTXNVY0RFNlEveE5lUHFBWk9vRG0iLCJtYWMiOiI1M2M1ODNhMTY1MjA3Y2NjYTZmMmNjOGVjZTEwZTMwZGJkODRjMWExYTVkMzQ4N2IyNmIzZmQyMDdlM2Y1MGZkIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:05:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IktoSWJoOUV4eGRUdS96WGt4Tncrcmc9PSIsInZhbHVlIjoiSHZ3YTVuUW4zNmg5eHMxOFZIVkkvS2w2bGpzUlNrQURGOVlJRnZuK1BUYXVDYWVtb3RjbVVQWVRPSTlXNkcxNWpYeDJtZGVULzdEMEFKaXBnY2F3VUtvZld4K3RiVitzSnMrWmFTZ3FtcXVUNmhYK0NZT3hJejNXN3BGZGU3bHI3Rm9Ia0FzRHhWNG1hdTI3K2RHUGtGSUJBaXFzYjlIclJQbW9TaEFpa0s3aDhCSCtCWEE2eExWbDd0UjVsdURSa21WWEQ4R0QrNFJ5MTRYTSs5VlZuT29oY1JRVkpNTEtjWlMzSER4RHNrRmtDcTJFaHZxQ1NHR0JQRzNsZTkvU21SQk04Tk8zMFhyaGhSLzBFd093b0lwU0w2WUdMb1VNZzNQa1J3QVlYZHhRU0h2N1JEVTB4enJiOXhiVGIvWmpvd2IxZ3c2MXZKdm5WbUhOdG9BVkgzTlNyTldOZ2s1TlVTemk5YWpJU0FZOUNBcC9DWVpJUFJYdzdMSWQwRHhZS0NXRndHUEcyR2c5SVFZRnk1NS9MTXRGaFppYTdiOGcrZmw5MmFHUU53SnFEUW5vb3JFamxrZzlwMHpCbC9ZTWlTWUg3TDZVMkViRFZMajVvQzRBd1VlSUIzaHg0UnBnZmo5ZXhCcnZyQldvN3JBNCthek9BMmFPTCtva1N2cGkiLCJtYWMiOiIzYzkzYTM4NjA5OTEyYzRhOTcyMzA3ZTNlOWVmMzc0MzJiNmM3N2UzOTBjMWUyN2JmMWQ3MjkxNmVmNTZmMTk2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:05:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-341774428\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1455821273 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1455821273\", {\"maxDepth\":0})</script>\n"}}