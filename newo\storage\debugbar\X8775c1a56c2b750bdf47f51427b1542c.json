{"__meta": {"id": "X8775c1a56c2b750bdf47f51427b1542c", "datetime": "2025-06-08 01:18:28", "utime": **********.263956, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749345507.569471, "end": **********.263984, "duration": 0.6945130825042725, "duration_str": "695ms", "measures": [{"label": "Booting", "start": 1749345507.569471, "relative_start": 0, "end": **********.168547, "relative_end": **********.168547, "duration": 0.5990760326385498, "duration_str": "599ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.168565, "relative_start": 0.5990941524505615, "end": **********.263989, "relative_end": 5.0067901611328125e-06, "duration": 0.09542393684387207, "duration_str": "95.42ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45053384, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00468, "accumulated_duration_str": "4.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.222852, "duration": 0.00287, "duration_str": "2.87ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 61.325}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.241058, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 61.325, "width_percent": 18.59}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.250203, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 79.915, "width_percent": 20.085}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-682158175 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-682158175\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1824062945 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1824062945\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-28819716 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-28819716\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-876848649 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749345302717%7C55%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjZnMmZkOUR0VlZwak0vcm5zRDVGK3c9PSIsInZhbHVlIjoiWE51UTFxNkpEL3B1aGlvZFB1dGw2MlA1UDFVYzMrU1R1RjFkdFJVT3psRWgwbktVVE5IRkd2NlU0Rmt0amN2TGFCUkZFeG5YaHVjSW5WL2NzWUVJZVVENnlESjVQZnh3MThUdE1PL1dNU1JuMFpuQ3BWQlhWSGYzaGI5dlFmUS9ldCtaUXpNeUVVK3MwaEIwS0MwM014ek9ZaHRtR0JTTDJIdGtZUldZNzNtQUxqc3QvODdPMSt0empjL2ZIbENCakZnbFRUckVPS1FKMUpTL0tVNkJtOE01NTRYOGZUNEFVbTR4dlZFUElUTlpQWnRrNzN3cDVMT0ZqeFNGU2I2YWU5bkE5dnd4NFErandsaTJmdWMzOGhFZjNMY2ova20rVTBLalIvMHRuZkZDNVBjWjJxQWVRQlVHL0tTWW0vYmhzK3ZQTHNrYWtEVkFZS0Z4MWdHS1BiZ0VmcGFydk9Bc1QxWmFsVUR0VUx2NU1taU5JOEFCT1lkRkF2UlFnTUIxY05pUGUwbW5aUjlwbU1kRkhQVzlEcExXQVhCNGVxdFZ0Y3lKMWppWit5MGEwNlp4NW5UQSthT2xMRlgyOHVmZEFUUmhaV1FQWkpKNnN6WUZmaXI5d1ZyOHo1a2dSQlR6VmVnKzNYdjNGT09WellOVm1pOXZKR3hQS1cycHJZU2MiLCJtYWMiOiI5MjI5ZTllNjJiNmEwYTVlN2I5ZGQ1MzlmYjRmOWJkODcxMGJhZmU4YWNhMTE4ZjhmM2RjNDVlZGJlOGFjYzhlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlpnV0VtcU1IZklzdlQ0aWN1TmNvUVE9PSIsInZhbHVlIjoiZDdPZ0V0anQzcUNOT0hsODFwbFAxSFg4amRSVERsQlBUbFlNRU9rdDFtczFZY3lmZXYrNnFPNEFEcXE4SlQ2UldVeXlEMCsxb3pMUk9tZkNEdUk0UFFxaVZCNVRXUUo0RDJ5YUd0aERsd21Yc2Npd0RrT2dIeStoWUY0Q2Y0MEtPNGtWTHRGaWFIMnlwWWdKV1pqTm5mekxkR2dHeXdqQlUzUTAvdmFncGErTEZSYU1lMTFSMTBUbEhtY0tjN0R2c29qVTlRT3dSNERydHlDV0VTeWRXWTUzbXVacGV5MjRKUEwyaS9nMWQ3Vi90WU8zYk5ROS92c0dnV1k2UWltT04yc3p2UU1IZC9YazAxTUpzR0VJcUxkekcvZ2FMd2g3dFk5dUw4S3dvT1BJdkdBOHBQQ2FoS1R2SEh4cVlNOVNuMGMrcWtTOVAyblBzLzNSN2dZSkJxdWtQa1QzdzJmY21QVTRFT2dlK3NnL05IcUJpZjJYajFpc29MY014N2xrbzVTVDB5WVRhQ0hMVUNUdFFlNmFtbHVZWSt3VVo3K1diYkoxQ3d5a21iRlNLTGE1ZHhOUStaNXRKT1NKZExQZ1dSemk2bmhreTZhU3ZHYzlub0c1RjdoTWpCNy9zbmpudG1PS2dpa0RGZTZvbXRvQm4rZWg5ZHFTdDVFMnFoYVUiLCJtYWMiOiI5MDg3NmI5MzgyYmVlMDMyNTZkZTU0ZmFjYTJkNDM3OTgwMWFkYjkxYjgwN2NmNmE1MjNiNGZlZDhlYmRiNjEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-876848649\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1175343752 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1175343752\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-907644432 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 01:18:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1GaUdUVU9SczVFTGhObktVNnBvZmc9PSIsInZhbHVlIjoiMElPRFVWSG1qODYzYWsrOHNIWCtWamZZTzd0bTRrY0JMMU5xUE52YjcvdGR0YmlYSDhmTkZxbUlWNzlZNjBhUHNzK004WUk1M3NlWnVlQWI2OVBPUlplZ0QzVElTc0VWd1JSVENYNkgxOUo1YitqUHFQNWVPZk5vZW11ZjJQdTU0cXNFZ3V4d0hJSGhybkkzcW52bGwvZVRLR1RyM29kUDFZTFo4K2kzTzdFZ3dVdFJjZE9EbFYrakprM3E5VXVDcnloS2hLYTVSckNwdkFleFpBb0g1cWdjQzhsUXViRlYrTW93M2V5TVROK0RKcmMvaXd3cW05VFIwZUdRRHVWbnNMWlp2SGFtOTYvTFF3eVR0SHFYbzRDdElHM29xMnptQUMrZnptbzkzU1hzVjZNd1ZRQnVmdUtIb3p6UTBsQnZwaGhISVVtbkVZNFdIbkpRcDBWSklNOHNnSklUaWtFbm9PWEx3SXBIU3ptSHBTdGk2bG01SHZPdnh1UUJSelNma3hkeGtQaCtQTk1nTzcvSmNMbU5DNGE1UVVaWTJxQUVZL29GdzdBakM2QktoQVMwbUlXWEtzRnVHM0t4TEZ6WnJUSWVyeFRzLzZUY2VocUlDcDYwMCtLUXBWR3AzZGZJQ3doUmFHVEhJNW1rOXdkTHRxWUhEVyt2Y1p1MHhpRVQiLCJtYWMiOiI5MmMxODIyZmZkYmViZWI5MTc0MGE4NGEwODkwNjQ5YThlYWNhMmY3OTI4MGVmNzllNWNjMDczZTEyYWVhYTJjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:18:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik9BRDJVL3FMZkZobk9OVTNVeDQ0dVE9PSIsInZhbHVlIjoiclQyWkVnZU5TNDRQTUFlK0ljUk1GWU1UN21RcDk1Yi9rSVYyTU56YVZmalh4TEp6cHZQT0N0TVFFVkt5VkZKaFVOand5VVJ4YUlCYWxXUUUzODRROXBHejlTWU1KTFdpUUlEZEVZY3V0ZEVPZVRLZWp3UzdMRjhiL1plMWRMTytSNGpuN3RBcGN0STg1RmE0TmphTUZoUmNJZGJBM29XMk5ya0x0R2RWZEVucG5EdjdrUVhWS0xOS1hsdmIrRTV5a3ZrdW0yVzdDMVB4ZHB4UHBXMnJHQWluQnJXRlJPMlFJUzExUmhOTTZMMWtIN3JpRG5xQTZOZ2NWYmV1MzlNb1VNZkowMFhMN0E1bUo4MXBaMDhNZ3dLUUE1dkFEVnBZNHlyamtvaGtSS29wQ3EwY0FhUmdQU0Q2NVRYYTBEZVRyY0c0a0NQVHB1WkkrVjZwY2ZUemhzNFFsUFZMa1dKVlFWbXJtcHJSSEhJZ1JCL2pmVzJMQjRVUjJneHZtK0FWaDJ4Y1duNU9xNklZQWRGT3dZZTF2NGRKUk5jUlRkZHVkeVN0cmU1NXc3eUNjWDFjS3lOQlVBTWJkNzlaVE8vT0dFMW1UUXBUSnNTdU8rUGVPZ1Jva1Y3VHJJZTFpNlcrTWtaTnNPQUhDcEFYSzF5VGQ4dmYreWpkTDVzdDNwdVUiLCJtYWMiOiIyZDkxNDk3NTAwMDFlYWZjZDlkOWMxNTI1NTZkN2UxZmIzNmFlMmM0OGZmMWY4ODUwMDY0YTRkMzA4YzJkOGExIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:18:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1GaUdUVU9SczVFTGhObktVNnBvZmc9PSIsInZhbHVlIjoiMElPRFVWSG1qODYzYWsrOHNIWCtWamZZTzd0bTRrY0JMMU5xUE52YjcvdGR0YmlYSDhmTkZxbUlWNzlZNjBhUHNzK004WUk1M3NlWnVlQWI2OVBPUlplZ0QzVElTc0VWd1JSVENYNkgxOUo1YitqUHFQNWVPZk5vZW11ZjJQdTU0cXNFZ3V4d0hJSGhybkkzcW52bGwvZVRLR1RyM29kUDFZTFo4K2kzTzdFZ3dVdFJjZE9EbFYrakprM3E5VXVDcnloS2hLYTVSckNwdkFleFpBb0g1cWdjQzhsUXViRlYrTW93M2V5TVROK0RKcmMvaXd3cW05VFIwZUdRRHVWbnNMWlp2SGFtOTYvTFF3eVR0SHFYbzRDdElHM29xMnptQUMrZnptbzkzU1hzVjZNd1ZRQnVmdUtIb3p6UTBsQnZwaGhISVVtbkVZNFdIbkpRcDBWSklNOHNnSklUaWtFbm9PWEx3SXBIU3ptSHBTdGk2bG01SHZPdnh1UUJSelNma3hkeGtQaCtQTk1nTzcvSmNMbU5DNGE1UVVaWTJxQUVZL29GdzdBakM2QktoQVMwbUlXWEtzRnVHM0t4TEZ6WnJUSWVyeFRzLzZUY2VocUlDcDYwMCtLUXBWR3AzZGZJQ3doUmFHVEhJNW1rOXdkTHRxWUhEVyt2Y1p1MHhpRVQiLCJtYWMiOiI5MmMxODIyZmZkYmViZWI5MTc0MGE4NGEwODkwNjQ5YThlYWNhMmY3OTI4MGVmNzllNWNjMDczZTEyYWVhYTJjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:18:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik9BRDJVL3FMZkZobk9OVTNVeDQ0dVE9PSIsInZhbHVlIjoiclQyWkVnZU5TNDRQTUFlK0ljUk1GWU1UN21RcDk1Yi9rSVYyTU56YVZmalh4TEp6cHZQT0N0TVFFVkt5VkZKaFVOand5VVJ4YUlCYWxXUUUzODRROXBHejlTWU1KTFdpUUlEZEVZY3V0ZEVPZVRLZWp3UzdMRjhiL1plMWRMTytSNGpuN3RBcGN0STg1RmE0TmphTUZoUmNJZGJBM29XMk5ya0x0R2RWZEVucG5EdjdrUVhWS0xOS1hsdmIrRTV5a3ZrdW0yVzdDMVB4ZHB4UHBXMnJHQWluQnJXRlJPMlFJUzExUmhOTTZMMWtIN3JpRG5xQTZOZ2NWYmV1MzlNb1VNZkowMFhMN0E1bUo4MXBaMDhNZ3dLUUE1dkFEVnBZNHlyamtvaGtSS29wQ3EwY0FhUmdQU0Q2NVRYYTBEZVRyY0c0a0NQVHB1WkkrVjZwY2ZUemhzNFFsUFZMa1dKVlFWbXJtcHJSSEhJZ1JCL2pmVzJMQjRVUjJneHZtK0FWaDJ4Y1duNU9xNklZQWRGT3dZZTF2NGRKUk5jUlRkZHVkeVN0cmU1NXc3eUNjWDFjS3lOQlVBTWJkNzlaVE8vT0dFMW1UUXBUSnNTdU8rUGVPZ1Jva1Y3VHJJZTFpNlcrTWtaTnNPQUhDcEFYSzF5VGQ4dmYreWpkTDVzdDNwdVUiLCJtYWMiOiIyZDkxNDk3NTAwMDFlYWZjZDlkOWMxNTI1NTZkN2UxZmIzNmFlMmM0OGZmMWY4ODUwMDY0YTRkMzA4YzJkOGExIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:18:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-907644432\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1390030139 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1390030139\", {\"maxDepth\":0})</script>\n"}}