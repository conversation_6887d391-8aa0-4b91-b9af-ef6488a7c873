<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use Illuminate\Foundation\Application;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "إصلاح دور مستخدم Delivery...\n";

$user = User::where('email', '<EMAIL>')->first();
if ($user) {
    $user->syncRoles(['Delivery']);
    echo "✅ تم إصلاح دور مستخدم Delivery\n";
    echo "الأدوار الحالية: " . implode(', ', $user->roles->pluck('name')->toArray()) . "\n";
    echo "لديه دور Delivery: " . ($user->hasRole('Delivery') ? 'نعم' : 'لا') . "\n";
} else {
    echo "❌ مستخدم Delivery غير موجود\n";
}

echo "انتهى الإصلاح\n";
