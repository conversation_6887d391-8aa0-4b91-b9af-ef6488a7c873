{"__meta": {"id": "Xc91fe5e6e331b78ed1d0095305bb073e", "datetime": "2025-06-08 01:15:15", "utime": **********.374832, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749345314.657064, "end": **********.374859, "duration": 0.7177951335906982, "duration_str": "718ms", "measures": [{"label": "Booting", "start": 1749345314.657064, "relative_start": 0, "end": **********.295235, "relative_end": **********.295235, "duration": 0.6381709575653076, "duration_str": "638ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.295249, "relative_start": 0.6381850242614746, "end": **********.374862, "relative_end": 2.86102294921875e-06, "duration": 0.07961297035217285, "duration_str": "79.61ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45270200, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1671\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1671-1681</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0038599999999999997, "accumulated_duration_str": "3.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.343473, "duration": 0.00312, "duration_str": "3.12ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 80.829}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.360612, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 80.829, "width_percent": 19.171}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-2135255359 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2135255359\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-531753872 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-531753872\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1305128962 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1305128962\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1939 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i31pha%7C1749345246637%7C11%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im1tZUIxUWw0K2o0d0lRNC94YjRPVnc9PSIsInZhbHVlIjoibHZQck44a2g0cW9HS0dFZnc5RmZtL0VreC93Rll5emVYQjI3bmo0eG1OWkZ6M3dsdVBtUDM0N2RMM3d6T1hkQk1GdmVDbUtkZTFzSVQvRHZWOUtIQmk5MEgweTdYa1VlU0t5MUZ4TWhjd0FFYVhwbjBkaU1FRjdBekV2bGpvb2IwRFNkbnB0K2I5a21FUksxSjdxMnNiSFBSUCtSUkxVdFRsRzYzNTRUaVhOMmdTcDRlNjVsa3dJaUVLS1RqZ2FxTm9USmtkSHhwOUJlMXg0WWl4NWx2Nkppc1puZVV5MnppbzVjSnI4SkRuRmIrQVMyS0NpN3E0UGowRm9TWjJLbTJlMHJOZ2NHL3QrMG9DWmZpVmFCeWZkSDdSYnc0aUtlSEhVV0dLSTFhK1dMdEQ5bUZ4b2lrZDZwZzltKzNDM1hURjhwc1Y5N1U5YldBRTFvQUhENXNqYXZ6dkhaT1RGT0oxUWUyUGtyaUtCdnFEYSttQUZhUzB2UHYxbCtRMkZPYlB6a0pkNDN5ZHdsVkk1YjdLVWhkNW9uU1FoeElvYW84TzYvc1V2MWdFSGRMVjlmanMrSXNCUDhKL1JqVDRTbHdpblhXYTRaUmFNSVl2QUh5MVVMdE95ZDZ1ZStFUzZrd2FZVzByYjhqSXpQaGEwOUJxSmtEaXFuT011UjZZRysiLCJtYWMiOiJhMTkxMjk0N2JjZjg4ODI0ODI4ODk5NTU0YTYzYjdkMTZkNTYxNTI3NGQxNTFhMmFjN2U2Y2I2ZDM1MjJkZWQ0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkNSVDdVVDFHSUpuQmVUMmFIODI4MlE9PSIsInZhbHVlIjoiM3lsVk9rV0VvR0pqTllaUHlCRXhWV215c3RSaU9ncWh5RnVVRDU4czQySWRHVkNqYW5DMFhzUktKd0gzY3BTSlpRdU5hZXJTcFByL281Z0wvVEFYU2N0MVg4RzVQM1l5VGI3ZFJCT0lBSUttRkx3K1JoTTdveDdBcW9tcm42UU1uSTZ6L3MrVlpwbk5yQ1NFSlppNWw0VzRORU1SLzFTN2NYUkg2MGdIdG16TE9mY0NCRGF5bHE4ekRTTk9KL1dSbCtUd296bkZCZFdYYXVrZmhrd2Z6ODBlMGxBK0JYWVh4OXZxSDNKa1pUQXczcWdvUEI3NGpuZzlGN1ZSbCtQOEhnRiszRVBNVkZQN1dCVWJFamNmSFRoQXhaYUNCd0d6SUxwMGpHM2xwbGs4VDVWUVR4TnM3MkliVktrVXg4N1lSbEE4R1ZyOXplWDh6b0NQVjZpbXZmeGxwdDZpQ0NPVHhMVGY3eWo2aG9LM3Noc1BUN3JKVFltelhycWZzK2JVZzNPZ3RBank2dUlZUUlBOHZ0ZDhhbU5OM1hjdkdRaFkzYkJoK0dBTktNamsrQUtpQ0diYVFLaWdMeTg5bHBkbmFYVGo4NzBWbCtocll5Z0RqV1FwdUIwbTVKZzZZMlZseEhsNUJFNEZLUVFvZkhPdzU4TU12M0NjZmFiUTdTK0siLCJtYWMiOiIxZTQ2ZGU5MzQ4OTg4MzUwMmRlYzFiMDkxNjdmN2QxMDFhMGY3MGMxZDQwNGUyZDY2YjFmNjZhY2ExZDlhMmNiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1746535831 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 01:15:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZMNU1wK09OVmJJOU5TaTgxTWIvOGc9PSIsInZhbHVlIjoiOEdqNHgzem1DbnJjODBrb2ZvTEpEVjl1Y2ZGdHNzZkVGeEZ0eFRxZVJURG9PcnVra3gwYWF2RVFmelY1Q0tSMDJIOVhBb2xJOThpbjVndHg3ZmJLTXNDMzM2SW5vRlBrbHB2ZnBNWDZWRjc3QWhrdndrOHk4V3F6M1dKTmo2RithNW9QclByamRWR093dW11L2xFT3NSMlNKSEg0ZWxQRzk3Uk5ob2ZDSldsVU5qOHA2ZU9wRW0xeVZZRFAzSllXMHFqUE0vdFVUMFZ4MkErYVdReFlIczhRVU9ybHMzQ0Q5QzV6UDMwMTB3K3FrZUxscVo0aWtJOUd1ZnhVazI2OTlhTEVKa1BjZ2oxZVV1TWdpb2t1MXJlTk5zbW54TnhwZTZVeC84YTN3dHRGbTFzRldYYWYveXBhRlVhR0J4cFJMaVRrSTEwWVpPU2RscGswVXdic2NLcWlvWFU2RzlvcnR5V0dDRTN0NXpqVkJIVVgzRzIvZUpkSFh1N3ZQTDE2ekYxcDRZNHptZjJhdm9HdzFYRkdSMFBVcHhBOVIxVVFoYlg2WUxSVVF5bzRaMWJuY2ZocW5SQm1IY1JFZW9yUU9aOUQrMWh5YnhQQ3BsQ0N2Q2ViOHlhRWlGaTZzSlR5bm5NVDBndzhPRlJoZ2dWMmJCMEd2Y3hkeFRPVXZmUXYiLCJtYWMiOiIxYTE5MDgxNjM0OGZjMzBiZDQwNDBhMWIyZDBhZGY0Njg5NzAxMTI2NzQ0MWQ5NTQxZTUwMWE1Y2M1YzYzZDA1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:15:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkNMbTROSmJhWkxpL3FmZ20zNUlTdHc9PSIsInZhbHVlIjoiTlJpOVVHRUhwUmJEWmhiSi92YzV6ckJyM2R3eUhZeXNXK2tKVjBQY0pkWnEvMTZlaFJ6VVRGUVU4SENHUFJGdXpiUUlURE5DajcxUzRXT25oTTJFUDdsZ0JpaksvcE92VndXc1dzNWF4ZVBreCtXZVJLZzlOT2k5Q1JDK0FJNkR1aUh5WU9LalVOYmVYUGFXcGVoczh3YzJoVmwyRnRSWG9ZWmZKYmZIL05xNTZwM3N5Ym5LbmFDWmNaMkRJdTVHN2E2UnZaVHBjUk51cVRCWEx2NzdmRG14RUhBU09ZWGRDTGU0QlBOa0xBd0FobkdQWi81NTVCWXEwaGZSeTRGc0k2OTFnVDQrdWZYQ2Y4SDlKRGV6MjN5eXZNY2p1MEhLWi9haW5pRlNhcDFxOWlEaXI3ZmVDdDlKSUhYQkw4emsvQ1FYSVp4cmNJak5PeVdDTWpmN00wbE9QdC9sWkFudXBJS29ER3BvbVkrSTdHZmNLcC90OTA4eE9iSWhFM3oyU1JjRU9CZ3dFVmk3amhiMlpiTUFSSUxYNnErZG9OZzduRC90bTlDdHR5aFhvZ1h6SWVCT3VIUENhOG5NS0sxVGx6M004MDRMVUpEc01YNlpJVlBBbVNYZU8za3NkcUJLNWhaNDBZcFRpZE5YaFk3SU9lbXR1S2xVMG1yUitidlEiLCJtYWMiOiJlMWEzMWI4Zjg0ZDFjZDc4Y2Y5ZTdjMjVjM2FiNmE4OTllMjA0NjI3Y2I1YmJhYjg1MTU1YTRhZTliNTU3MGZhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:15:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZMNU1wK09OVmJJOU5TaTgxTWIvOGc9PSIsInZhbHVlIjoiOEdqNHgzem1DbnJjODBrb2ZvTEpEVjl1Y2ZGdHNzZkVGeEZ0eFRxZVJURG9PcnVra3gwYWF2RVFmelY1Q0tSMDJIOVhBb2xJOThpbjVndHg3ZmJLTXNDMzM2SW5vRlBrbHB2ZnBNWDZWRjc3QWhrdndrOHk4V3F6M1dKTmo2RithNW9QclByamRWR093dW11L2xFT3NSMlNKSEg0ZWxQRzk3Uk5ob2ZDSldsVU5qOHA2ZU9wRW0xeVZZRFAzSllXMHFqUE0vdFVUMFZ4MkErYVdReFlIczhRVU9ybHMzQ0Q5QzV6UDMwMTB3K3FrZUxscVo0aWtJOUd1ZnhVazI2OTlhTEVKa1BjZ2oxZVV1TWdpb2t1MXJlTk5zbW54TnhwZTZVeC84YTN3dHRGbTFzRldYYWYveXBhRlVhR0J4cFJMaVRrSTEwWVpPU2RscGswVXdic2NLcWlvWFU2RzlvcnR5V0dDRTN0NXpqVkJIVVgzRzIvZUpkSFh1N3ZQTDE2ekYxcDRZNHptZjJhdm9HdzFYRkdSMFBVcHhBOVIxVVFoYlg2WUxSVVF5bzRaMWJuY2ZocW5SQm1IY1JFZW9yUU9aOUQrMWh5YnhQQ3BsQ0N2Q2ViOHlhRWlGaTZzSlR5bm5NVDBndzhPRlJoZ2dWMmJCMEd2Y3hkeFRPVXZmUXYiLCJtYWMiOiIxYTE5MDgxNjM0OGZjMzBiZDQwNDBhMWIyZDBhZGY0Njg5NzAxMTI2NzQ0MWQ5NTQxZTUwMWE1Y2M1YzYzZDA1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:15:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkNMbTROSmJhWkxpL3FmZ20zNUlTdHc9PSIsInZhbHVlIjoiTlJpOVVHRUhwUmJEWmhiSi92YzV6ckJyM2R3eUhZeXNXK2tKVjBQY0pkWnEvMTZlaFJ6VVRGUVU4SENHUFJGdXpiUUlURE5DajcxUzRXT25oTTJFUDdsZ0JpaksvcE92VndXc1dzNWF4ZVBreCtXZVJLZzlOT2k5Q1JDK0FJNkR1aUh5WU9LalVOYmVYUGFXcGVoczh3YzJoVmwyRnRSWG9ZWmZKYmZIL05xNTZwM3N5Ym5LbmFDWmNaMkRJdTVHN2E2UnZaVHBjUk51cVRCWEx2NzdmRG14RUhBU09ZWGRDTGU0QlBOa0xBd0FobkdQWi81NTVCWXEwaGZSeTRGc0k2OTFnVDQrdWZYQ2Y4SDlKRGV6MjN5eXZNY2p1MEhLWi9haW5pRlNhcDFxOWlEaXI3ZmVDdDlKSUhYQkw4emsvQ1FYSVp4cmNJak5PeVdDTWpmN00wbE9QdC9sWkFudXBJS29ER3BvbVkrSTdHZmNLcC90OTA4eE9iSWhFM3oyU1JjRU9CZ3dFVmk3amhiMlpiTUFSSUxYNnErZG9OZzduRC90bTlDdHR5aFhvZ1h6SWVCT3VIUENhOG5NS0sxVGx6M004MDRMVUpEc01YNlpJVlBBbVNYZU8za3NkcUJLNWhaNDBZcFRpZE5YaFk3SU9lbXR1S2xVMG1yUitidlEiLCJtYWMiOiJlMWEzMWI4Zjg0ZDFjZDc4Y2Y5ZTdjMjVjM2FiNmE4OTllMjA0NjI3Y2I1YmJhYjg1MTU1YTRhZTliNTU3MGZhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:15:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1746535831\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}