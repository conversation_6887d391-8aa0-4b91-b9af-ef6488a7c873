{"__meta": {"id": "X8e99aed884725d8413dd19cd4d5922d6", "datetime": "2025-06-08 01:05:55", "utime": **********.923366, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.205418, "end": **********.923388, "duration": 0.7179698944091797, "duration_str": "718ms", "measures": [{"label": "Booting", "start": **********.205418, "relative_start": 0, "end": **********.842248, "relative_end": **********.842248, "duration": 0.6368298530578613, "duration_str": "637ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.842261, "relative_start": 0.6368429660797119, "end": **********.923391, "relative_end": 3.0994415283203125e-06, "duration": 0.0811300277709961, "duration_str": "81.13ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45040224, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.004529999999999999, "accumulated_duration_str": "4.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8882592, "duration": 0.00311, "duration_str": "3.11ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 68.653}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.9032478, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 68.653, "width_percent": 12.804}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 23}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.912033, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 81.457, "width_percent": 18.543}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1080605994 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1080605994\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1379630819 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1379630819\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-23944646 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-23944646\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1632092412 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749344713396%7C49%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjV1WmN4ZzdzOU9RU3A5WEp6SVBuTWc9PSIsInZhbHVlIjoiai9mVlpMVnVnMVFlNTVmcGJKZ1RDS043NVlvQWpHb1ZUVlRPbmF5WjdCUGQzMzVSWjRaOXpmOUc3NnFRM1NIcEkyUDBVZUNNTndZbGlZVHJ3ZVk5Ylg1UTdxMzVaQnJLRzc1MDNCaEVub2FBUFQrcEMwWmducllWS3QwSGhRWTdFN3hFZi9GZE1hSTVuOG1hV3F0VWlLRTY4YjNBdXFLdmFRb2NheHRvM1plZWxVemdBU0pkNDAvZlBJMVpQb01semtzVHdJZGZCdkh5dDZMOGd2MnY5QlRORk4yb3hMY1dLYmJINnJ0cjVQNEZud0J5bSsyNnp2SHcybW1sZklCNk96a0tyeDh1MHBCN3hORFBDSFo1VTRvRXBwMy9TYytXY29mZ2RlNzJWTnU1ODExbzQ1QnJiVjRBRFhYLzhsMnRudm9jOUFza2l0Tm51eGMvMllQZ2NXTzhMSkpEa05ha1YrVjZGZzVYUXphaitlSm1vY2YyRDhTQUhUbU04VUdLb3ZRU0FJRG5SN21lKytjMHRDVHZKSDVsMTMwRVBPTGI2ZXBhQ1VxdHNVMCtvc0gyZXVoTm8yaHRPTkc5ZVFJaHdQUXdiRnluWGpCS1JPa0dLdCt3TWs0TDlzNnhmRlhUZktKK3NjbnhpUDN2SzJ4VEpCRUdzYy9zTFRIRWdQak8iLCJtYWMiOiJiYjZmNGEyM2EzYzE5M2QzZTkxZDM1NmE3NmYyZDFmNjVmMjNmM2Y0MDVjYjU3N2M4MTc3OTVkYjU3NTNmMDhmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjdDc0ltUFhMS0tpelE2dTQrMmNhZkE9PSIsInZhbHVlIjoic2F2bzQwR0QySStEem1yRlY5eDBDRXA4eDR1dTRGZ29YZFFXbkd1SzdJeG9IdmlNYVZBSTdnbExLSlR2d003eTZiYWJsanJvRDFGOHV5cWtaWnFIdmsxVzRsLzZRZmRaZm1wVUdpOFYwcVFEakNmZ2JnYWFkdWgxbEtRWEhiUmt1UElEaDIzTjFxcUxlemxPMUlJYmRaWXFrNm5NLzVOZjhHNVhnQ1dra1pWaTdPTm16WTBFNFZuUGRaUXlwTW0wU3Eza05QeDhiUVpvZ0xLYVdPU0hYRTYxQ3R1YVpxa1dJN2dtbHpvbEY3VGQ3Vmk1SkcxYXJHc3dQRzhrRmtZZDRCNnZFb1lBWmhQZ29JNkpzTjI2Q1Z4dUFYYTZsVGxGcVgya0lBTEluL2hMd1AvRVAvVjJEejJ4a2N3Z0hKSUMreERKMCsvMk5sZktCbStTUHhuMEszd2creEdHa0JuT1ZHN2NSUHlhWEFGWXNjRXlSSE1ldzVNdEdWSTlqZ0VBanBTK3dweCtic0VWK1ZKeTdlaitFcUdLVWE3M3pITmVmbmdLK2JBV1dFdnQ1V1NyQ2pvMXh4UEJnYXdEbzh4cDdlNEt0cm5JMEZpVGJXWlVsa2JubkZiSEtCV2dMWElYQ2tsTm9MQmJPaE5MNWw4aXpza3lLTG91bnJZODhldGsiLCJtYWMiOiI1ODUwZTQxYTQ3MzUxMGY5MWExNzc0ZmEyNTBlYTgzZjJmNmQ0OWNlMDQxMjhkYTEyYmE2Y2IyODgyZTE1NGRmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1632092412\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-528802289 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-528802289\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1437749048 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 01:05:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkRBdzcrU3VBc1pjdDRXVmtXaUx1VlE9PSIsInZhbHVlIjoiVzRCTmViVzZrTmJScG1XNWk4VGgrcTdVZkVva3NKN1A1Z1l3UXNlbklIUHJVOUplTTdSNytZNE5qQldVTkdrM2N4TlVRSWFkVGhXL0pFbHBOM2V5RndSaWs5OUxMYkRoRVh5cGFWaGYzeFpxamJZMXJ3L0g2MHNyMkVFbEg2UTJSY0xuL2ZqUDJTa2MwQmVsK2xLY21KalFFZVk1ZlVpaWdBMjh1anNka2xpVjcwS0w2QnRsTWlnZTlXd1BvaGZCcFlGMWxQVTJUVlZ3aG9kcm1WaXl5andUNytlYmxCVmxpRGd5c3BJbWRsL2JvbkZvdDdGbDlrUnA3aEV5Q1BqV0EzVWJobXZTOFV4MHo0bVlvQURzTVhydTR0cjZZMHhXaEplZXUvdVNJMVMxUzFXTDlRL3J6aVVCNnV1UTE3b1BPd0R2NWowdXVtVmUrMkFKOVhzY0V1Unh4b2pPeCtqd0xNZkRvNmtMN0VEQmcydEJ6bFR5R0hVNkRKNUl1NkVNcEx0MWRDNmFsYjErdTNBbnBCbnplUjJuaDVTbzR6bFFyR2xOd0RTSHhycGpjOHpsLzNEN3Brc2RuTFBIVWlEcEtpZmdJZUNiYURrTkVKdzNMYlJia0Z6SGZ1dkY3RG9VMmtIWEk0cTk3YzlDeEZXdkpCSnpVbGFjMlBjZklnMkciLCJtYWMiOiI1MDAyOTYxY2NkODg5NWMxZjFiYTgxOTZjM2QwZWMwOGJjNDA2YTk5NTc3Y2NkM2Y2NGU2M2IzZmYzMDkyNWFmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:05:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImZ1dXRacEN2NzlGR1M3REx5RTdETnc9PSIsInZhbHVlIjoia2IyQW9IcnJ4RXE3WGlPWURTVmdyRDNIZUJxNmhHTkdUMjY0bkV4WnJoZnAyRnFsSFV4c1pySm1Fa1A3YzVpWU9PL3Q2RUdnTy9FVklVU29rMUxLNWx1TTBOcXJKMXJKOGcrdnBiTkdjR1BBbHFwdHBuQk5yUjdhSHgvaUpGWDZKcmk0cHRydjRIeWJNeE4vQ1FDTEw0MFRpNFJyaEt5ZEl6YVpYZXV0WUdnbDlIbHhzbWxMRVBsSk5tbjh0djhEdG9TaHVWc0RUbzQwYmxKV015ZENIQlhyakRPN0w4RlFRSXpESGl0dDA0MTdJa1dBZGdhVFN1WHZ1VXpOanFLdTFVblB4amVmWDIyR0EybU5ITWd6aXhoQXY3dEpaWlRpYklOa0JPNDNoQ2l0T0xUQXk3cENVazh6ODJCa2R1VEdpdXRpRmpVNUJJaFVVRGd1MFVGai8vYzZpYUM0dHB2UHJneVA1RTBORk15b3ZjT3d3UytvcWtKMkZ6YVdLQkZEOHh0WmM1RnVrZVpkamFZSWVOb0duOHRvd3Vvc1RUcnFiV01jdEUyWkxqdTRyemNpWkUzR0lEVDBtSkc3TmJ3M3pBd3ZFRkJSZS9HNnB1UUNnQ0hZK01qYVorS3hVbytoTTl0aU5YU3IvemNMVnJFTWx2U3dTWXFZM3RXN04wY1EiLCJtYWMiOiIxOWU3OTg0YWU1Nzc5M2M4NzM0YTY4OGU4MDRkMWZiODUwNmU2NTkzMzI0ODEwYjg5OWY3NTVjNDY1YTg3YjdmIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:05:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkRBdzcrU3VBc1pjdDRXVmtXaUx1VlE9PSIsInZhbHVlIjoiVzRCTmViVzZrTmJScG1XNWk4VGgrcTdVZkVva3NKN1A1Z1l3UXNlbklIUHJVOUplTTdSNytZNE5qQldVTkdrM2N4TlVRSWFkVGhXL0pFbHBOM2V5RndSaWs5OUxMYkRoRVh5cGFWaGYzeFpxamJZMXJ3L0g2MHNyMkVFbEg2UTJSY0xuL2ZqUDJTa2MwQmVsK2xLY21KalFFZVk1ZlVpaWdBMjh1anNka2xpVjcwS0w2QnRsTWlnZTlXd1BvaGZCcFlGMWxQVTJUVlZ3aG9kcm1WaXl5andUNytlYmxCVmxpRGd5c3BJbWRsL2JvbkZvdDdGbDlrUnA3aEV5Q1BqV0EzVWJobXZTOFV4MHo0bVlvQURzTVhydTR0cjZZMHhXaEplZXUvdVNJMVMxUzFXTDlRL3J6aVVCNnV1UTE3b1BPd0R2NWowdXVtVmUrMkFKOVhzY0V1Unh4b2pPeCtqd0xNZkRvNmtMN0VEQmcydEJ6bFR5R0hVNkRKNUl1NkVNcEx0MWRDNmFsYjErdTNBbnBCbnplUjJuaDVTbzR6bFFyR2xOd0RTSHhycGpjOHpsLzNEN3Brc2RuTFBIVWlEcEtpZmdJZUNiYURrTkVKdzNMYlJia0Z6SGZ1dkY3RG9VMmtIWEk0cTk3YzlDeEZXdkpCSnpVbGFjMlBjZklnMkciLCJtYWMiOiI1MDAyOTYxY2NkODg5NWMxZjFiYTgxOTZjM2QwZWMwOGJjNDA2YTk5NTc3Y2NkM2Y2NGU2M2IzZmYzMDkyNWFmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:05:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImZ1dXRacEN2NzlGR1M3REx5RTdETnc9PSIsInZhbHVlIjoia2IyQW9IcnJ4RXE3WGlPWURTVmdyRDNIZUJxNmhHTkdUMjY0bkV4WnJoZnAyRnFsSFV4c1pySm1Fa1A3YzVpWU9PL3Q2RUdnTy9FVklVU29rMUxLNWx1TTBOcXJKMXJKOGcrdnBiTkdjR1BBbHFwdHBuQk5yUjdhSHgvaUpGWDZKcmk0cHRydjRIeWJNeE4vQ1FDTEw0MFRpNFJyaEt5ZEl6YVpYZXV0WUdnbDlIbHhzbWxMRVBsSk5tbjh0djhEdG9TaHVWc0RUbzQwYmxKV015ZENIQlhyakRPN0w4RlFRSXpESGl0dDA0MTdJa1dBZGdhVFN1WHZ1VXpOanFLdTFVblB4amVmWDIyR0EybU5ITWd6aXhoQXY3dEpaWlRpYklOa0JPNDNoQ2l0T0xUQXk3cENVazh6ODJCa2R1VEdpdXRpRmpVNUJJaFVVRGd1MFVGai8vYzZpYUM0dHB2UHJneVA1RTBORk15b3ZjT3d3UytvcWtKMkZ6YVdLQkZEOHh0WmM1RnVrZVpkamFZSWVOb0duOHRvd3Vvc1RUcnFiV01jdEUyWkxqdTRyemNpWkUzR0lEVDBtSkc3TmJ3M3pBd3ZFRkJSZS9HNnB1UUNnQ0hZK01qYVorS3hVbytoTTl0aU5YU3IvemNMVnJFTWx2U3dTWXFZM3RXN04wY1EiLCJtYWMiOiIxOWU3OTg0YWU1Nzc5M2M4NzM0YTY4OGU4MDRkMWZiODUwNmU2NTkzMzI0ODEwYjg5OWY3NTVjNDY1YTg3YjdmIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:05:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1437749048\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-934935539 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-934935539\", {\"maxDepth\":0})</script>\n"}}