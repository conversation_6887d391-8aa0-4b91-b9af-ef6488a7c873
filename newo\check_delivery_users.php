<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use Illuminate\Foundation\Application;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== فحص مستخدمي الدليفري ===\n";

// البحث عن جميع المستخدمين الذين لديهم دور delivery
$deliveryUsers = User::whereHas('roles', function($roleQuery) {
    $roleQuery->where('name', 'delivery');
})->get();

echo "المستخدمون الذين لديهم دور delivery:\n";
foreach ($deliveryUsers as $user) {
    echo "- {$user->name} (Email: {$user->email}, Warehouse ID: {$user->warehouse_id})\n";
}

// البحث عن جميع المستخدمين الذين لديهم صلاحية manage delevery
$deliveryPermUsers = User::whereHas('permissions', function($permQuery) {
    $permQuery->where('name', 'manage delevery');
})->get();

echo "\nالمستخدمون الذين لديهم صلاحية manage delevery:\n";
foreach ($deliveryPermUsers as $user) {
    echo "- {$user->name} (Email: {$user->email}, Warehouse ID: {$user->warehouse_id})\n";
}

// البحث عن جميع المستخدمين الذين لديهم دور delivery أو صلاحية manage delevery
$allDeliveryUsers = User::where(function($query) {
    $query->whereHas('roles', function($roleQuery) {
        $roleQuery->where('name', 'delivery');
    })->orWhereHas('permissions', function($permQuery) {
        $permQuery->where('name', 'manage delevery');
    });
})->get();

echo "\nجميع مستخدمي الدليفري (دور أو صلاحية):\n";
foreach ($allDeliveryUsers as $user) {
    echo "- {$user->name} (Email: {$user->email}, Warehouse ID: {$user->warehouse_id})\n";
}

echo "\n=== انتهى الفحص ===\n";
