<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class DeliveryRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // البحث عن شركة موجودة لإنشاء دور Delivery تحتها
        $company = User::where('type', 'company')->first();
        
        if (!$company) {
            $this->command->error('No company user found. Please run the main seeder first.');
            return;
        }

        // إنشاء دور Delivery إذا لم يكن موجوداً
        $deliveryRole = Role::firstOrCreate(
            ['name' => 'Delivery', 'created_by' => $company->id],
            [
                'name' => 'Delivery',
                'created_by' => $company->id,
            ]
        );

        // تحديد الصلاحيات المطلوبة لدور Delivery
        $deliveryPermissions = [
            'manage delevery',
            'show delevery',
            'create delevery',
            'edit delevery',
            'manage pos',           // مطلوب للوصول لدالة create في PosController
            'show pos',             // مطلوب لعرض تقارير POS
            'show pos dashboard',   // مطلوب لعرض لوحة تحكم POS
            'manage customer',      // مطلوب للتعامل مع العملاء
            'show customer',        // مطلوب لعرض العملاء
            'manage financial record', // مطلوب لإدارة النقد
            'show financial record',   // مطلوب لعرض السجلات المالية
        ];

        // التأكد من وجود جميع الصلاحيات المطلوبة
        foreach ($deliveryPermissions as $permissionName) {
            Permission::firstOrCreate([
                'name' => $permissionName,
                'guard_name' => 'web',
            ]);
        }

        // إعطاء الصلاحيات لدور Delivery
        $deliveryRole->givePermissionTo($deliveryPermissions);

        // إنشاء مستخدم تجريبي بدور Delivery إذا لم يكن موجوداً
        $deliveryUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Delivery User',
                'email' => '<EMAIL>',
                'password' => Hash::make('1234'),
                'type' => 'Delivery',
                'default_pipeline' => 1,
                'lang' => 'en',
                'avatar' => '',
                'created_by' => $company->id,
                'email_verified_at' => now(),
            ]
        );

        // تعيين الدور للمستخدم
        if (!$deliveryUser->hasRole('Delivery')) {
            $deliveryUser->assignRole($deliveryRole);
        }

        $this->command->info('Delivery role and user created successfully!');
    }
}
