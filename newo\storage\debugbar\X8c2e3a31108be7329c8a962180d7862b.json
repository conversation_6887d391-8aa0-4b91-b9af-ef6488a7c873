{"__meta": {"id": "X8c2e3a31108be7329c8a962180d7862b", "datetime": "2025-06-08 01:05:36", "utime": **********.800421, "method": "GET", "uri": "/financial-operations/sales-analytics/product-performance?warehouse_id=&date_from=2025-06-01&date_to=2025-06-30", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.219369, "end": **********.800453, "duration": 0.5810840129852295, "duration_str": "581ms", "measures": [{"label": "Booting", "start": **********.219369, "relative_start": 0, "end": **********.705877, "relative_end": **********.705877, "duration": 0.4865081310272217, "duration_str": "487ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.705892, "relative_start": 0.4865231513977051, "end": **********.800456, "relative_end": 3.0994415283203125e-06, "duration": 0.09456396102905273, "duration_str": "94.56ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46141472, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET financial-operations/sales-analytics/product-performance", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\SalesAnalyticsController@getProductPerformance", "namespace": null, "prefix": "", "where": [], "as": "financial.sales.analytics.products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=493\" onclick=\"\">app/Http/Controllers/SalesAnalyticsController.php:493-600</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.013229999999999999, "accumulated_duration_str": "13.23ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7575052, "duration": 0.0027400000000000002, "duration_str": "2.74ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 20.711}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.7725399, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 20.711, "width_percent": 6.954}, {"sql": "select `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`expiry_date`, `psc`.`name` as `category_name`, SUM(pp.quantity) as total_quantity, SUM(pp.price * pp.quantity) as total_revenue, COUNT(DISTINCT p.id) as order_count, CASE\nWHEN ps.expiry_date IS NULL THEN \"لا يوجد تاريخ انتهاء\"\nWHEN ps.expiry_date <= CURDATE() THEN \"منتهي الصلاحية\"\nWHEN ps.expiry_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN \"خطر عالي\"\nWHEN ps.expiry_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) THEN \"تحذير\"\nELSE \"صالح\"\nEND as expiry_status, CASE\nWHEN ps.expiry_date IS NULL THEN NULL\nELSE DATEDIFF(ps.expiry_date, CURDATE())\nEND as days_to_expiry from `pos_products` as `pp` inner join `pos` as `p` on `pp`.`pos_id` = `p`.`id` inner join `product_services` as `ps` on `pp`.`product_id` = `ps`.`id` left join `product_service_categories` as `psc` on `ps`.`category_id` = `psc`.`id` where `p`.`created_by` = 15 and `p`.`pos_date` between '2025-06-01' and '2025-06-30' group by `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`expiry_date`, `psc`.`name` order by `total_revenue` desc limit 10", "type": "query", "params": [], "bindings": ["15", "2025-06-01", "2025-06-30"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 536}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.777694, "duration": 0.00853, "duration_str": "8.53ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:536", "source": "app/Http/Controllers/SalesAnalyticsController.php:536", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=536", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "536"}, "connection": "ty", "start_percent": 27.664, "width_percent": 64.475}, {"sql": "select `ps`.`id`, `ps`.`name`, `ps`.`sku`, `ps`.`sale_price`, `ps`.`expiry_date`, `psc`.`name` as `category_name`, `wp`.`quantity` as `current_stock`, DATEDIFF(ps.expiry_date, CURDATE()) as days_to_expiry, CASE\nWHEN ps.expiry_date <= CURDATE() THEN \"منتهي الصلاحية\"\nWHEN ps.expiry_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN \"خطر عالي\"\nWHEN ps.expiry_date <= DATE_ADD(CURDATE(), INTERVAL 15 DAY) THEN \"خطر متوسط\"\nELSE \"تحذير\"\nEND as risk_level from `product_services` as `ps` left join `product_service_categories` as `psc` on `ps`.`category_id` = `psc`.`id` left join `warehouse_products` as `wp` on `ps`.`id` = `wp`.`product_id` where `ps`.`created_by` = 15 and `ps`.`expiry_date` is not null and `ps`.`expiry_date` <= '2025-07-08 01:05:36' order by `days_to_expiry` asc limit 15", "type": "query", "params": [], "bindings": ["15", "2025-07-08 01:05:36"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/SalesAnalyticsController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\SalesAnalyticsController.php", "line": 566}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.789261, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "SalesAnalyticsController.php:566", "source": "app/Http/Controllers/SalesAnalyticsController.php:566", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FSalesAnalyticsController.php&line=566", "ajax": false, "filename": "SalesAnalyticsController.php", "line": "566"}, "connection": "ty", "start_percent": 92.139, "width_percent": 7.861}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/sales-analytics\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/financial-operations/sales-analytics/product-performance", "status_code": "<pre class=sf-dump id=sf-dump-1071016371 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1071016371\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-630792228 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>date_from</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-01</span>\"\n  \"<span class=sf-dump-key>date_to</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-30</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-630792228\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2041840574 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2041840574\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-720395707 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749344713396%7C49%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik5WcHJJKy9sSUJpU2IxMmdHWGFxOXc9PSIsInZhbHVlIjoiaHIrczJGQmlaaDNTeXJySnFlZjBQbzgwaFF5K09wL0ZjR2RVenRzYUNQWXNnR1VaOVhtSHBpdFY5M1loc1kzQnNqL0wrcVcycXA1cEliSVlPNEtvQlVQSmZoS2pYYUlXM0NHZHQ3N0NyN0tFYXd5N0dEbWEzUFd3OTZxdGcxcFhVU0ZNaVhrSmNlVnUxNHU5NkNnVU4rNDlhWnVLRkkwMGF1VEdjRUxIUnh4bmN1NlRXSnBKcnJ3ckR0bjRNZlNkOTY3b0YxcmlGc0RmWnkvdFdOQVpUdzFmbzFEZ0NvTEZpWmN4ZmxuZDYzZmkreVRla0VIMnpNaFlDbkoxeXpjcHNXMHFBRVVyUFk2RTVhNkFNbCtzYmlBMGJZSTdZcXVjelJqTVVhc3M2TSsrdkFOaC93blVTRkhMUjNPN2piN3dRV3gzbkl6WnovNnVrcFBSOEdxWDhkMGxoRUlzVXZvbzZtNENmTHg5MVBSbE80TWZXZlBoWXIvbUc1MnZhZ281RW52RldvQUlUT21JVHE3L0daeDJmdlBnUkQ5UUZweGYxTENDbisrQm9reVAzR054enNXRHg4SnEyNW1lRkZNNHVhMEFRZVA5dEZZYWpqbFhGNmNJMFJUNGx6UnVYbFRZR0dVd015ZlBZcm15SzROWFo0d3ZkOW1lUS84SjJMQzkiLCJtYWMiOiI4OTk3M2IzNzI5OWZlMTE0YzFlMGRjOTU5NDJiY2Y0YTU2ZjMzMWM2YzViYWZjOWRmZmIyOGRmOWQ0NjI3ZTUxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InUvRGJXZWIyWG92NGVMOFBldXpQS2c9PSIsInZhbHVlIjoiWWJST20rZmw4MGNTVStCY2xnQ0dOb0s5YWJXek5GN1NMS1RkdFFIdGhuNFU0ODBwQTUzUlN5VFNiTWpKM1poUnN3L1hMQ2VSbk1qdXVQdDRpbXdCQUdSUnB5TnhaRlR4TEZScWtJSUlJUW8rWE5UN3hCVVJLQ0pxUTcvSnVSY2RHUE5FbXFiQUxWanduYWVGWHV4MjVRU25SODg3Q0k4ZkdXZ1RkRm0vVTg1LzhmcXY2N2l6RE8rNjlWUzVnTzJwTk15SG1scG03dmVkSDFCa0JNeVdJSUdITjVRVEpZVW9vR1gzSWU4NHhRQzVLUEVVaVl1eEVmM0FpTGgzMWxCcEZEWlBMVFVIcU5LZ043U0tJMUpZSzhySFhRZkJiblFaUE10ZE5VK1c5UE9GQ3dMZFovSVJkcW5KZXNVcFBFKytLQlBPblVPNEFPMTdrMmRMNHJKbVd6UWN1L0JIdXlhODEvNHk0ODcxcXp4RCsySDhzRUVnZW1vbXV6M3NEZEs0Y2R2N1BWZUJWNDFPQ2Y4MHU2V1ZIRERUSjVVUS9UMEt0MzRHeWo0ZjJNdE5SRzdWVVJjNGdPVGhQZUpIOFd6bzVpS0M1UG9jcVZwcVJLYk42VWIzVFFRaFR2a3N5YmxpWXY0S3hlTmU2SnhpVTlkTHlha1RRVUNYV2QxRzkzbXAiLCJtYWMiOiI4OWEwNmRhOTU0NDA0MGZlZjlkZmRjNTYzMGI4MDI1Yjk0ZDUxMjdmYWY1ZmY0YTQxY2UwYmEwY2JlOTllMTczIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-720395707\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-101720874 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-101720874\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1229872208 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 01:05:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNHaFZqTnVCbmlvdk1EVWVoRG5PSFE9PSIsInZhbHVlIjoicUQ3NCtiWWFyakFNdVRzUVVFOENuZmZJZUVUakdrVk40eUQzMDk2QUt6R2xrSTNnTjZIdHNpUkhCQnVjVnVpbXRNeHo4NWtYZW9RNUg3SURvdG9wQ216NHIzMFJKRmdNdDFQclUvanVpK2tLWUx4K2ZMY0ZBdTU5UHRnSUZLb1hXVW1Db1FQSVZSVHNkSFU1UUMxZVhOSSsrZnJWRk1EaXVJczFnVkJPQlA4c3J5WHNSb29DcXVsbFZJeHJKaVNUcXpNTTBTSUtOMjVUUHJGVmJHV1JPaXJlQzN6cjJCbTVYQWtST0RxU3VqUWx3TGZhcU5vNUdpVXBGVHBzV1F4dkxtL0llalVqRE94djVVZGpoSlRZVmxsRXlLb055amR5UjdpaDRSTVZPcUxMWHFvS1NjQ2pTVEs2WVdDajBTQ2RxSGcxYkFES3ptQVowdktaZWVjSldzUzBKeXUzSDRySGlwVnFNVkN6cnloYUp5MVZ5QkNVSCswWGNONVNEdXVsSlNOMmpZRmVNQ011VjdpeTJwQllSa3hrNFQwOVp1aDgzTUY1am8yRzg4U0tMN0paaXp3RmJIZXdaY2dmaFJvdm9JZ1gzcHJkL0RXd0JwemNYOHZ2a2R3eTV4YVdRZ0JSSlg5Q25HTHJSR3FQbU9xTjNhdy9IMk5yWTRTNDZXbXkiLCJtYWMiOiI4YWI4YzBiMTM4NTYyZjg4ZTNhOThmNWM0OWNmOWM2ZjA0MzZmMTJhZTQ2ZTc0OTIwNDc0Zjc3NDM2ZTAwNjA2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:05:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InAwK1pBbnMwcklTTy8rY1M2UzJLUUE9PSIsInZhbHVlIjoidWZoSDg5UnBTTWd1eVlzYThxdllVWmNKOFl3T0VPSFdMdFJLR1J5bjN3Y2RvQXI5enNVYnNYSnNWMzVicXordnJnaU96OE1VU1NsV2pleHFmczJaQWxEZkZTWnhHRkdyQVpISzZreFBmZTRPNnBRK3dpcUJ5S3lrRWRnY013OTNCcnM3d3pZclB6VzNoTS9zckIvVVBVRFFmT3pqSE9oM0F6SmZic2gvcWxOQldPSk9ENWJGVFkrR3Rtd200UmNkcmRmMzlVYS9oc1hDT0pQclZZNlRoQ0xCbzRBRnNDNW5lYWZ6L0FTalJnV0tJQ1ZEa0p5SWhrNklWSUpxbzM0ZUp0TXdPT1JzRFlWUVBJWC9iVXFQaHpDbjhVbUgwMGJkZTB5elRqanBSakhlZisxN2pjRXFIV1lDUldPQkk5NXJ3VG5RcDZZcys1eG9EdllrZVlPVG1mdXU5bFhIRFh6MkRKT2J4WnR6YU1JRVZ4S0FCdjgydGx2eGY4VG9xcndpREl4WnphdnpJejY1cHY3bUFaTUU2UCtjWXZiZjMrSnJsdCt2T0dZSG9VSlBxcXAzQUpjbTJWOVBQVnlxL0ZtczcrdXdlQyt5d3ZFVWtycC9ySUlkK2VDSHRVYjFDdXpxM1BNVUY3SnhQNmJMZXF3WGxiLzdyQlBwR3pXK1BzRmsiLCJtYWMiOiI2M2YwNzYyYTQxZDcwZGQ1NzhlODE0ZDM0NzVkZjZiYTZmODk3ODA1N2I2MDAyZDJjMGM1MWE2MTU3MjAwNmIyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:05:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNHaFZqTnVCbmlvdk1EVWVoRG5PSFE9PSIsInZhbHVlIjoicUQ3NCtiWWFyakFNdVRzUVVFOENuZmZJZUVUakdrVk40eUQzMDk2QUt6R2xrSTNnTjZIdHNpUkhCQnVjVnVpbXRNeHo4NWtYZW9RNUg3SURvdG9wQ216NHIzMFJKRmdNdDFQclUvanVpK2tLWUx4K2ZMY0ZBdTU5UHRnSUZLb1hXVW1Db1FQSVZSVHNkSFU1UUMxZVhOSSsrZnJWRk1EaXVJczFnVkJPQlA4c3J5WHNSb29DcXVsbFZJeHJKaVNUcXpNTTBTSUtOMjVUUHJGVmJHV1JPaXJlQzN6cjJCbTVYQWtST0RxU3VqUWx3TGZhcU5vNUdpVXBGVHBzV1F4dkxtL0llalVqRE94djVVZGpoSlRZVmxsRXlLb055amR5UjdpaDRSTVZPcUxMWHFvS1NjQ2pTVEs2WVdDajBTQ2RxSGcxYkFES3ptQVowdktaZWVjSldzUzBKeXUzSDRySGlwVnFNVkN6cnloYUp5MVZ5QkNVSCswWGNONVNEdXVsSlNOMmpZRmVNQ011VjdpeTJwQllSa3hrNFQwOVp1aDgzTUY1am8yRzg4U0tMN0paaXp3RmJIZXdaY2dmaFJvdm9JZ1gzcHJkL0RXd0JwemNYOHZ2a2R3eTV4YVdRZ0JSSlg5Q25HTHJSR3FQbU9xTjNhdy9IMk5yWTRTNDZXbXkiLCJtYWMiOiI4YWI4YzBiMTM4NTYyZjg4ZTNhOThmNWM0OWNmOWM2ZjA0MzZmMTJhZTQ2ZTc0OTIwNDc0Zjc3NDM2ZTAwNjA2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:05:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InAwK1pBbnMwcklTTy8rY1M2UzJLUUE9PSIsInZhbHVlIjoidWZoSDg5UnBTTWd1eVlzYThxdllVWmNKOFl3T0VPSFdMdFJLR1J5bjN3Y2RvQXI5enNVYnNYSnNWMzVicXordnJnaU96OE1VU1NsV2pleHFmczJaQWxEZkZTWnhHRkdyQVpISzZreFBmZTRPNnBRK3dpcUJ5S3lrRWRnY013OTNCcnM3d3pZclB6VzNoTS9zckIvVVBVRFFmT3pqSE9oM0F6SmZic2gvcWxOQldPSk9ENWJGVFkrR3Rtd200UmNkcmRmMzlVYS9oc1hDT0pQclZZNlRoQ0xCbzRBRnNDNW5lYWZ6L0FTalJnV0tJQ1ZEa0p5SWhrNklWSUpxbzM0ZUp0TXdPT1JzRFlWUVBJWC9iVXFQaHpDbjhVbUgwMGJkZTB5elRqanBSakhlZisxN2pjRXFIV1lDUldPQkk5NXJ3VG5RcDZZcys1eG9EdllrZVlPVG1mdXU5bFhIRFh6MkRKT2J4WnR6YU1JRVZ4S0FCdjgydGx2eGY4VG9xcndpREl4WnphdnpJejY1cHY3bUFaTUU2UCtjWXZiZjMrSnJsdCt2T0dZSG9VSlBxcXAzQUpjbTJWOVBQVnlxL0ZtczcrdXdlQyt5d3ZFVWtycC9ySUlkK2VDSHRVYjFDdXpxM1BNVUY3SnhQNmJMZXF3WGxiLzdyQlBwR3pXK1BzRmsiLCJtYWMiOiI2M2YwNzYyYTQxZDcwZGQ1NzhlODE0ZDM0NzVkZjZiYTZmODk3ODA1N2I2MDAyZDJjMGM1MWE2MTU3MjAwNmIyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:05:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1229872208\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-79369509 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost/financial-operations/sales-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-79369509\", {\"maxDepth\":0})</script>\n"}}