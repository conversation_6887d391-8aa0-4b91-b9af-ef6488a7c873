{"__meta": {"id": "X9b73902dcdcb38d6efdcd7881427ccdb", "datetime": "2025-06-08 01:15:13", "utime": **********.935181, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.302194, "end": **********.935204, "duration": 0.6330099105834961, "duration_str": "633ms", "measures": [{"label": "Booting", "start": **********.302194, "relative_start": 0, "end": **********.806239, "relative_end": **********.806239, "duration": 0.504044771194458, "duration_str": "504ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.806265, "relative_start": 0.5040709972381592, "end": **********.935207, "relative_end": 2.86102294921875e-06, "duration": 0.12894177436828613, "duration_str": "129ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48123032, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.0226, "accumulated_duration_str": "22.6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8557441, "duration": 0.01943, "duration_str": "19.43ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 85.973}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.886534, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 85.973, "width_percent": 2.965}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.90989, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 88.938, "width_percent": 2.611}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.9129279, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 91.549, "width_percent": 2.434}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.9197989, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 93.982, "width_percent": 3.451}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.92463, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 97.434, "width_percent": 2.566}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-50431045 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-50431045\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.918523, "xdebug_link": null}]}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-884519587 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-884519587\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1060887977 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1060887977\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-916513765 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-916513765\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1254086292 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1939 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i31pha%7C1749345246637%7C11%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkxhZUFPRFpKendyeFJ4dnBOMXdremc9PSIsInZhbHVlIjoid2puMlJiM3RyN0tKdVhhLzQwY3NCTVhjREc3eEQ0ZHNkL2grU20zdENtQXEwZytqVGxRK0p4bGZSMEM5c3RTM1ZPUk5aMm5xWDltRkc3aXk4dk9hdEJxYkFUUk53Y2kzTWpkOTc4R3NMZFA5V29vRGpDbFQ4cUxneWFFb04yWE52VGVYNTBlbTRpRTMxc3hYWEc2dXNaSitmU0JXV3dFQytXUkR5OG5DZjZsRHhJQU9CVkEyRHdHcUhvbThoTHFQTzFQbFVWdjlMNmV5a1g0VjB2NXNCdnFkQ1JhcUY2MSs2WnE4c3ZiQjMrMVRUT0hHN2NEMk1rV2ZoYnhocjU0cG5jSDB3b2tSSFJhTzF3OUhETU9VVHRqTW5uMVBXbnVmbFB1Z1M1ZWNxRDI5dFM0SFNhL1RBTU02a3RKY2tVeDh5ekJ0ZFE1TmttQys5NDFpUHFnNGFjc0FEZm04eXFmMkhjZ2RDZnRERnBjVW5JRkdGS3VSZ3g2UnBaM2VOd3JpeXJuNU9NaERSU0FjaUJBZTc5eUNFSnE0dzVEWjMvTW1ucTJabE55OFA4ZXhydnN5WFUxR0s2Q0pDWUJhNk5TcVdGU2tNaElST0RpTStPOXA2c0NrRjJ3Nkw0dkswUklmL2tEU1d3aGYwbjA3c0V5NjVEelBiS1ZyaU5HdG50OWgiLCJtYWMiOiJlODY3MWUyOTdiMTc5YWEyYjJlMjExMzZjMTNjZTY4OTU5Y2NkOTE1ZmUyMDExNGEzN2YxYjIzYzA5ZmIwZDljIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImRjTXY1ME10aFRzcFhnRXhUbFFzdHc9PSIsInZhbHVlIjoiOHVUNVRLMU1sTURua0xESk52RDZsTlkvRU5qZTNST3VKbThkZVBzeHZEaFBIRzBEWjdHYkVVQmxJODdqWElDUlBJR1VwMUk5eWhubXhxaTNIakU5VE9DNXdZbm83ZUwzcWlRekNibUF4U0ZuR2s0aS8wczFMUGU4R1VJbllLS3FSZXVCbU9wdzBUWTM1Z0VBV0xSTjU4OFJGQlE4eHVUZ1d2dFpMT3V2YTBjMlNwNXBIc2cvWXYzMFMwL0hWM3NldFN1b1JKaXRoKytXbytMR1d5bGtSNnlndnNkalBVUTc0Q1pML2hQR3pBUXdIZjV4WG1aUTZHdWZtN0lOOUhEOWJKNkFHbXc2QWdvdVlVVXJZRUdlNDRVMjhSOXZrZ2ZZd01pZ2xTQUlEK3ptWjJqc3R1UFBLcnJsS0J0K1RENnA1bkRLcDl1eE1XbTVCTkRWNFg2NUY4ZEVnY3RFdGVaVWNUcDFjRDJ6czFXU2R4RkRkbjR0c1RaUFdjSjVjQzl4OW4zUGhZbDgrMU83V3ROa1pjUVBiNzV1R2doSE5FdGZkeGR6V0tEaktmOWx3c1FTVE85dlo5TVNGNGR5WitjNTdnVWZISkJHY3lEc01Vc05Jb3dVZWZxT25sWlR5SzJHRXJTeUFBZ1hDRmdqQkZkY015VldtQVdTYXhWMlp4RFoiLCJtYWMiOiJjOWMwNTc0ZjRkZGQ1MzkyZWI3OTE2NTQ2NjY2OGY0MzM5MzA3OGJmY2M2ZTc5OTkxMjcwODgwZTNmZjE3MDY3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1254086292\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-72799448 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-72799448\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 01:15:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im1tZUIxUWw0K2o0d0lRNC94YjRPVnc9PSIsInZhbHVlIjoibHZQck44a2g0cW9HS0dFZnc5RmZtL0VreC93Rll5emVYQjI3bmo0eG1OWkZ6M3dsdVBtUDM0N2RMM3d6T1hkQk1GdmVDbUtkZTFzSVQvRHZWOUtIQmk5MEgweTdYa1VlU0t5MUZ4TWhjd0FFYVhwbjBkaU1FRjdBekV2bGpvb2IwRFNkbnB0K2I5a21FUksxSjdxMnNiSFBSUCtSUkxVdFRsRzYzNTRUaVhOMmdTcDRlNjVsa3dJaUVLS1RqZ2FxTm9USmtkSHhwOUJlMXg0WWl4NWx2Nkppc1puZVV5MnppbzVjSnI4SkRuRmIrQVMyS0NpN3E0UGowRm9TWjJLbTJlMHJOZ2NHL3QrMG9DWmZpVmFCeWZkSDdSYnc0aUtlSEhVV0dLSTFhK1dMdEQ5bUZ4b2lrZDZwZzltKzNDM1hURjhwc1Y5N1U5YldBRTFvQUhENXNqYXZ6dkhaT1RGT0oxUWUyUGtyaUtCdnFEYSttQUZhUzB2UHYxbCtRMkZPYlB6a0pkNDN5ZHdsVkk1YjdLVWhkNW9uU1FoeElvYW84TzYvc1V2MWdFSGRMVjlmanMrSXNCUDhKL1JqVDRTbHdpblhXYTRaUmFNSVl2QUh5MVVMdE95ZDZ1ZStFUzZrd2FZVzByYjhqSXpQaGEwOUJxSmtEaXFuT011UjZZRysiLCJtYWMiOiJhMTkxMjk0N2JjZjg4ODI0ODI4ODk5NTU0YTYzYjdkMTZkNTYxNTI3NGQxNTFhMmFjN2U2Y2I2ZDM1MjJkZWQ0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:15:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkNSVDdVVDFHSUpuQmVUMmFIODI4MlE9PSIsInZhbHVlIjoiM3lsVk9rV0VvR0pqTllaUHlCRXhWV215c3RSaU9ncWh5RnVVRDU4czQySWRHVkNqYW5DMFhzUktKd0gzY3BTSlpRdU5hZXJTcFByL281Z0wvVEFYU2N0MVg4RzVQM1l5VGI3ZFJCT0lBSUttRkx3K1JoTTdveDdBcW9tcm42UU1uSTZ6L3MrVlpwbk5yQ1NFSlppNWw0VzRORU1SLzFTN2NYUkg2MGdIdG16TE9mY0NCRGF5bHE4ekRTTk9KL1dSbCtUd296bkZCZFdYYXVrZmhrd2Z6ODBlMGxBK0JYWVh4OXZxSDNKa1pUQXczcWdvUEI3NGpuZzlGN1ZSbCtQOEhnRiszRVBNVkZQN1dCVWJFamNmSFRoQXhaYUNCd0d6SUxwMGpHM2xwbGs4VDVWUVR4TnM3MkliVktrVXg4N1lSbEE4R1ZyOXplWDh6b0NQVjZpbXZmeGxwdDZpQ0NPVHhMVGY3eWo2aG9LM3Noc1BUN3JKVFltelhycWZzK2JVZzNPZ3RBank2dUlZUUlBOHZ0ZDhhbU5OM1hjdkdRaFkzYkJoK0dBTktNamsrQUtpQ0diYVFLaWdMeTg5bHBkbmFYVGo4NzBWbCtocll5Z0RqV1FwdUIwbTVKZzZZMlZseEhsNUJFNEZLUVFvZkhPdzU4TU12M0NjZmFiUTdTK0siLCJtYWMiOiIxZTQ2ZGU5MzQ4OTg4MzUwMmRlYzFiMDkxNjdmN2QxMDFhMGY3MGMxZDQwNGUyZDY2YjFmNjZhY2ExZDlhMmNiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:15:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im1tZUIxUWw0K2o0d0lRNC94YjRPVnc9PSIsInZhbHVlIjoibHZQck44a2g0cW9HS0dFZnc5RmZtL0VreC93Rll5emVYQjI3bmo0eG1OWkZ6M3dsdVBtUDM0N2RMM3d6T1hkQk1GdmVDbUtkZTFzSVQvRHZWOUtIQmk5MEgweTdYa1VlU0t5MUZ4TWhjd0FFYVhwbjBkaU1FRjdBekV2bGpvb2IwRFNkbnB0K2I5a21FUksxSjdxMnNiSFBSUCtSUkxVdFRsRzYzNTRUaVhOMmdTcDRlNjVsa3dJaUVLS1RqZ2FxTm9USmtkSHhwOUJlMXg0WWl4NWx2Nkppc1puZVV5MnppbzVjSnI4SkRuRmIrQVMyS0NpN3E0UGowRm9TWjJLbTJlMHJOZ2NHL3QrMG9DWmZpVmFCeWZkSDdSYnc0aUtlSEhVV0dLSTFhK1dMdEQ5bUZ4b2lrZDZwZzltKzNDM1hURjhwc1Y5N1U5YldBRTFvQUhENXNqYXZ6dkhaT1RGT0oxUWUyUGtyaUtCdnFEYSttQUZhUzB2UHYxbCtRMkZPYlB6a0pkNDN5ZHdsVkk1YjdLVWhkNW9uU1FoeElvYW84TzYvc1V2MWdFSGRMVjlmanMrSXNCUDhKL1JqVDRTbHdpblhXYTRaUmFNSVl2QUh5MVVMdE95ZDZ1ZStFUzZrd2FZVzByYjhqSXpQaGEwOUJxSmtEaXFuT011UjZZRysiLCJtYWMiOiJhMTkxMjk0N2JjZjg4ODI0ODI4ODk5NTU0YTYzYjdkMTZkNTYxNTI3NGQxNTFhMmFjN2U2Y2I2ZDM1MjJkZWQ0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:15:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkNSVDdVVDFHSUpuQmVUMmFIODI4MlE9PSIsInZhbHVlIjoiM3lsVk9rV0VvR0pqTllaUHlCRXhWV215c3RSaU9ncWh5RnVVRDU4czQySWRHVkNqYW5DMFhzUktKd0gzY3BTSlpRdU5hZXJTcFByL281Z0wvVEFYU2N0MVg4RzVQM1l5VGI3ZFJCT0lBSUttRkx3K1JoTTdveDdBcW9tcm42UU1uSTZ6L3MrVlpwbk5yQ1NFSlppNWw0VzRORU1SLzFTN2NYUkg2MGdIdG16TE9mY0NCRGF5bHE4ekRTTk9KL1dSbCtUd296bkZCZFdYYXVrZmhrd2Z6ODBlMGxBK0JYWVh4OXZxSDNKa1pUQXczcWdvUEI3NGpuZzlGN1ZSbCtQOEhnRiszRVBNVkZQN1dCVWJFamNmSFRoQXhaYUNCd0d6SUxwMGpHM2xwbGs4VDVWUVR4TnM3MkliVktrVXg4N1lSbEE4R1ZyOXplWDh6b0NQVjZpbXZmeGxwdDZpQ0NPVHhMVGY3eWo2aG9LM3Noc1BUN3JKVFltelhycWZzK2JVZzNPZ3RBank2dUlZUUlBOHZ0ZDhhbU5OM1hjdkdRaFkzYkJoK0dBTktNamsrQUtpQ0diYVFLaWdMeTg5bHBkbmFYVGo4NzBWbCtocll5Z0RqV1FwdUIwbTVKZzZZMlZseEhsNUJFNEZLUVFvZkhPdzU4TU12M0NjZmFiUTdTK0siLCJtYWMiOiIxZTQ2ZGU5MzQ4OTg4MzUwMmRlYzFiMDkxNjdmN2QxMDFhMGY3MGMxZDQwNGUyZDY2YjFmNjZhY2ExZDlhMmNiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:15:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}