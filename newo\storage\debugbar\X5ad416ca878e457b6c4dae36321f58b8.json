{"__meta": {"id": "X5ad416ca878e457b6c4dae36321f58b8", "datetime": "2025-06-08 00:28:29", "utime": **********.585839, "method": "GET", "uri": "/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749342508.370811, "end": **********.58587, "duration": 1.2150590419769287, "duration_str": "1.22s", "measures": [{"label": "Booting", "start": 1749342508.370811, "relative_start": 0, "end": **********.313815, "relative_end": **********.313815, "duration": 0.9430041313171387, "duration_str": "943ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.313832, "relative_start": 0.9430210590362549, "end": **********.585874, "relative_end": 4.0531158447265625e-06, "duration": 0.27204203605651855, "duration_str": "272ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53266560, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.index", "param_count": null, "params": [], "start": **********.556299, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/pos/index.blade.phppos.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fpos%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.index"}]}, "route": {"uri": "GET pos", "middleware": "web, verified, auth, XSS, revalidate", "as": "pos.index", "controller": "App\\Http\\Controllers\\PosController@index", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=37\" onclick=\"\">app/Http/Controllers/PosController.php:37-109</a>"}, "queries": {"nb_statements": 11, "nb_failed_statements": 0, "accumulated_duration": 0.019620000000000002, "accumulated_duration_str": "19.62ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.402879, "duration": 0.00496, "duration_str": "4.96ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 25.28}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.431066, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 25.28, "width_percent": 5.963}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.47365, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 31.244, "width_percent": 6.932}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.479249, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 38.175, "width_percent": 5.912}, {"sql": "select *, CONCAT(name) AS name from `warehouses` where `created_by` = 15 and `id` = 8", "type": "query", "params": [], "bindings": ["15", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 50}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4909341, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "PosController.php:50", "source": "app/Http/Controllers/PosController.php:50", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=50", "ajax": false, "filename": "PosController.php", "line": "50"}, "connection": "ty", "start_percent": 44.088, "width_percent": 5.046}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'ty' and table_name = 'customers' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.496936, "duration": 0.0045, "duration_str": "4.5ms", "memory": 0, "memory_str": null, "filename": "PosController.php:57", "source": "app/Http/Controllers/PosController.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=57", "ajax": false, "filename": "PosController.php", "line": "57"}, "connection": "ty", "start_percent": 49.134, "width_percent": 22.936}, {"sql": "select * from `customers` where `created_by` = 15 and (`warehouse_id` = 8 or `warehouse_id` is null or `warehouse_id` = '')", "type": "query", "params": [], "bindings": ["15", "8", ""], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 69}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5061119, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "PosController.php:69", "source": "app/Http/Controllers/PosController.php:69", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=69", "ajax": false, "filename": "PosController.php", "line": "69"}, "connection": "ty", "start_percent": 72.069, "width_percent": 10.143}, {"sql": "select * from `users` where `warehouse_id` = 8 and `type` = 'delivery'", "type": "query", "params": [], "bindings": ["8", "delivery"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 72}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.512181, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "PosController.php:72", "source": "app/Http/Controllers/PosController.php:72", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=72", "ajax": false, "filename": "PosController.php", "line": "72"}, "connection": "ty", "start_percent": 82.212, "width_percent": 4.995}, {"sql": "select * from `pos` where `created_by` = 15 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 517}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 74}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.5209231, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "PosController.php:517", "source": "app/Http/Controllers/PosController.php:517", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=517", "ajax": false, "filename": "PosController.php", "line": "517"}, "connection": "ty", "start_percent": 87.207, "width_percent": 5.097}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4716}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4650}, {"index": 15, "namespace": "view", "name": "pos.index", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/pos/index.blade.php", "line": 3}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.558645, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4716", "source": "app/Models/Utility.php:4716", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4716", "ajax": false, "filename": "Utility.php", "line": "4716"}, "connection": "ty", "start_percent": 92.304, "width_percent": 3.568}, {"sql": "select `value`, `name` from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 4078}, {"index": 14, "namespace": "view", "name": "pos.index", "file": "C:\\laragon\\www\\to\\newo\\resources\\views/pos/index.blade.php", "line": 6}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.563393, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4078", "source": "app/Models/Utility.php:4078", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=4078", "ajax": false, "filename": "Utility.php", "line": "4078"}, "connection": "ty", "start_percent": 95.872, "width_percent": 4.128}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}, "App\\Models\\Pos": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}}, "count": 7, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 3, "messages": [{"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2091457732 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2091457732\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.489001, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1253817955 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1253817955\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.519592, "xdebug_link": null}, {"message": "[ability => manage discount, result => null, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-434379147 data-indent-pad=\"  \"><span class=sf-dump-note>manage discount</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage discount</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-434379147\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.575375, "xdebug_link": null}]}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/pos", "status_code": "<pre class=sf-dump id=sf-dump-1767165651 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1767165651\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1777401491 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1777401491\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-487215992 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-487215992\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">text/html, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1834 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749341283326%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InBIRTY3TmRnL202c1hVdXhPS2RLc3c9PSIsInZhbHVlIjoiY3VqQmdyM0F0UktueDR5eGNzMWdWTUhjZVpJWk5RVGY5Tk5Ebll1M3lNek9pQnVNSFY1UUV4dXRCWXh2QlVZSU5scFhqSkpFT3JFaVZySG45ZE10WldnT0RsaFZnbTg2aWRUWmNjOVVNamg2WFdIZWkva0NybGpTUFpuWWR3NzR2NmdRa3g4REprc0hDdkhCRDFhMWNOUkM5alFjLzJ1aXRGWGdVT3o1Q0JkcjFSV2JNTnNqNVlWMTZZT05SQnVveVg2NjNzZWZZT0o4T056dVNFUFZ5dHZDUDJ0N0JZa09DSlM4c0RNVklKODMyc1dVdUsxcStSZk5jMEVvOGE0RVdQZmk0OVRaK3BVWnVUM3FJeUMweDIrOGM5SkkrU2lmazFUYVZPcExUa3VpSkw5aW5ucjJlZ0hVdmg5ejc2S2FBV0RDWFJHemowM0NKczhqTk8yK3V6R2xqU1ZSM2N4WHhWd2hsNHA2YUFHUnFHUmd6OGl0Uzd5K1pockpIQjMxeCsxWGFUM0QrMnJtdzNMcUhkbmUxWHFENzhCa3NiZzYvZnV2MDRDb3JENFJ5NFhCcmc5L1FaRmFYU3NrdTNMNWozZGw4VFRGaHJHeHlLVFY0SGlUanZ6aXFMV1g4NGZpbWtmMHVteHdGenIrTFMyazBoUzdZck0zSXNkNHVRbkkiLCJtYWMiOiIzNDM0MmIxY2NmNzdiYTkxYjdlM2IzNzQ0NTBhZTEwMWQxYTZmZTFhYjlmZDUwNDAwOGQyOGNlZWMzZWM4ZDE1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkVxL05lQ0NmZEhyZnhuVEc3WEZuVFE9PSIsInZhbHVlIjoiQ1Q2MTFUYUVvWWNUcFVBYS9hcU5uRWlEQWVOTmE1b0lDMmk0K0N3MHYxWHl1VGtRWnB3aHY1ZzNqTWRpcHFwdVIvMzk3T3pFczlQakx6cTdlT1BOWVBYU1FoU0VqOVhLRFZ4YVBIMHowdmRXSTAxeHhHTzdZRC9PdDZ0RnBua2FpeDBKRFo1eWU3L1JUd1JpYmxnSzNWbWFCQ3dWTnEzMFBNOUUyNGgxVnk4K0NycHZGa0FNUlNpTXhTaERGMDRQQ0paZFhBMExpbW4yUVRIK21sK1cvbnlwbi8vR3JUamE1andaNWsrSm1oVkxONXhUdm40ZGpKdXoxY0JRYmtXZE8wUGgzUi8xODVrT2JlcGxmMjg0MGFsa0tTR0kvZmZlYittN0R3N0xNc1d5Y2RTTE5jOWxWMzlWTFhPSDNYNFZHYzBzZmh0Mnp6SDlES0xwNjhRRVVPWm5yc043ZmppbXhRRzg2MVZNaGl4dEdmcVoyQU1pTEhrQ29ZRElyNkEzOU1hOWV5NWFBODhOSzVrTWxZeVg3SDZXdmVHdWdkWjFnQW5Qb2JGOFBoTTlxUHlTUmJWM3dIM1BzakdZb3VLMTFURzJtVytFUWlrc3ZKdU8xSlhOZmRmL2RFYlphb3orK2VMUU9VQmlmY0pmbTZzS0d1L29ZejdyNXo3V0JzbnYiLCJtYWMiOiJiMmFhM2MyNGQ1MzU1MTFhZjQ2ODFlMGY0MzdjZTFkZDRkYjE4MzYxN2NhMWYzNDk0NTFiMzVmYjUwZjE1NTUyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1923760109 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1923760109\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1800936356 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:28:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IndEQ3dZak15eHZUTGd5Ti9MY1FpQ3c9PSIsInZhbHVlIjoibnlTVnVWSXFMR2V0OEFJNmsyRFBBRjNwMjhOeG9FTStQSnZTcWg0REoyYndjelhLdzhMRlZselZMbWR4UXUzV3pDQk9uWWVoaFZEdFgwVlZaaTVoTkFNbVpVTXBnVlZ2WmRUUFNPN01Wa3JoTTIwdnFTb1M2cmdFd0hNWVptZEd3S0dSbUR1T09UMWtjb2prMzd3YjdmTlllaVEvdE5VR0lob1VEZDN5QVVyS09mYlVndWhNeGFVWjc3U1Z4ajF4MmZDYytxK01rM3hoSktSMVpOTnRKcW9wMnI2bDZJZHRQL2JTRzh3V3FoVnZ1eDh3dWRzWWpRVVVJWEU1NFlyNlZuTEUrdWY5UFVjM2NrMVFLKytKN0lRZlRneVlid1M2OERpN3VUUERkZ1d6NTgyY0RlNUUrUmlYV3FMSW42ZUYxMSt2b1hwL2JZSGgzTEx4dk9TM3NaT0VLTlR3Zi80TmRqRWo4b0NYaGVKMGRZNC9vV0phNnAvSSszQ1kwZXJQS3h5LzN4QlZMMHFia1RTZ2J4ZEphcnd1QXJiNWFEQzRIcWluemRrTzdWYVFacGd2QVNNT0hPLzVIWUlCRFA5eS9KSU1ybXpKNlpXNVZheG56ZXhqMitWWnJNVnRHRXAxVWxyQlRsL1M5QzRwbjhFT0k3akQxa09Sdm0vemFoaGQiLCJtYWMiOiIyZDJhNjI4ODE1MGYyOTkxOTliZTJkMWNlZDlhY2Q4Y2FlMGNhOWQ2NjU4NmVkNjdlNGUxMTVkOTdjZDliZjZlIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:28:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InZORTNNd0prcnFtejMyVGw3d2hLZFE9PSIsInZhbHVlIjoicmhRL3lUbTVmUkw0dmQrSUQvWHBSbmF4WkZGcWN0VkVUMGYwcEx5MWxUaFRMQ1hwRTgyMXlwT0oxQjlWUEN5QXRCWDNhYmRhUGhIaEk0bHRIdHpTcnZmRisvWXMyM01lR0pvTWlYb1FPMmt1SFV5WHlRdDgxbHIzR29pN0ZKVzRJYnI0b1U1K1YwNENzK1crWnI0Z1grRnBFdDliUGkzMmV3dzZMSkZMMXZnRWtSL01JMVpCTGQrVzM5T1lFRXpBdDBMU3NCL3F0NktBMG53OW5YNFdTSTF5a0NObUxvK29QNHlJQitlNDBzNUxheHVibzhlc1JLZnNZdFdYRzhPTUtLWTB5VFFDVXo2QTZjTlJ1bmEvY05od1lwVDVBTHRhV2xhZmFDOUxzYi9Md0V2L0lwS0dlYXg2VDAzWTdWRHJmL091ZVpaK3JpZXNWS3JKVmxNa25RQUJ6dHRvSVZiVDRRZW5RNXVGVDh2aW1rNVYrb3ZPQzN0UFE5ZDJmYTg2dUd3VTBCQ3lTcWFwanR4Q0VXbVpqajJwK2dwdHdZdHplRkt3bVBUcW02RFZCUFRud2h6OFMvZFlnRUNoUFBZaHlMZCtUck5ETXlZY1pXQUM5em93bFVBcHlmYm1QOFpFU2p1cE1JTHhVRDk2bWFwSGoxb3JDM1oyYjJ1VGNRaFQiLCJtYWMiOiI2NzYyYTkwZGQ3MjFhNjhjM2EzOGUwZTBmZmQ4MDhmYjNkMjEwOThiZWMwOWJjMmQ2NDI5MWViMWQwYzM0ZDYzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:28:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IndEQ3dZak15eHZUTGd5Ti9MY1FpQ3c9PSIsInZhbHVlIjoibnlTVnVWSXFMR2V0OEFJNmsyRFBBRjNwMjhOeG9FTStQSnZTcWg0REoyYndjelhLdzhMRlZselZMbWR4UXUzV3pDQk9uWWVoaFZEdFgwVlZaaTVoTkFNbVpVTXBnVlZ2WmRUUFNPN01Wa3JoTTIwdnFTb1M2cmdFd0hNWVptZEd3S0dSbUR1T09UMWtjb2prMzd3YjdmTlllaVEvdE5VR0lob1VEZDN5QVVyS09mYlVndWhNeGFVWjc3U1Z4ajF4MmZDYytxK01rM3hoSktSMVpOTnRKcW9wMnI2bDZJZHRQL2JTRzh3V3FoVnZ1eDh3dWRzWWpRVVVJWEU1NFlyNlZuTEUrdWY5UFVjM2NrMVFLKytKN0lRZlRneVlid1M2OERpN3VUUERkZ1d6NTgyY0RlNUUrUmlYV3FMSW42ZUYxMSt2b1hwL2JZSGgzTEx4dk9TM3NaT0VLTlR3Zi80TmRqRWo4b0NYaGVKMGRZNC9vV0phNnAvSSszQ1kwZXJQS3h5LzN4QlZMMHFia1RTZ2J4ZEphcnd1QXJiNWFEQzRIcWluemRrTzdWYVFacGd2QVNNT0hPLzVIWUlCRFA5eS9KSU1ybXpKNlpXNVZheG56ZXhqMitWWnJNVnRHRXAxVWxyQlRsL1M5QzRwbjhFT0k3akQxa09Sdm0vemFoaGQiLCJtYWMiOiIyZDJhNjI4ODE1MGYyOTkxOTliZTJkMWNlZDlhY2Q4Y2FlMGNhOWQ2NjU4NmVkNjdlNGUxMTVkOTdjZDliZjZlIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:28:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InZORTNNd0prcnFtejMyVGw3d2hLZFE9PSIsInZhbHVlIjoicmhRL3lUbTVmUkw0dmQrSUQvWHBSbmF4WkZGcWN0VkVUMGYwcEx5MWxUaFRMQ1hwRTgyMXlwT0oxQjlWUEN5QXRCWDNhYmRhUGhIaEk0bHRIdHpTcnZmRisvWXMyM01lR0pvTWlYb1FPMmt1SFV5WHlRdDgxbHIzR29pN0ZKVzRJYnI0b1U1K1YwNENzK1crWnI0Z1grRnBFdDliUGkzMmV3dzZMSkZMMXZnRWtSL01JMVpCTGQrVzM5T1lFRXpBdDBMU3NCL3F0NktBMG53OW5YNFdTSTF5a0NObUxvK29QNHlJQitlNDBzNUxheHVibzhlc1JLZnNZdFdYRzhPTUtLWTB5VFFDVXo2QTZjTlJ1bmEvY05od1lwVDVBTHRhV2xhZmFDOUxzYi9Md0V2L0lwS0dlYXg2VDAzWTdWRHJmL091ZVpaK3JpZXNWS3JKVmxNa25RQUJ6dHRvSVZiVDRRZW5RNXVGVDh2aW1rNVYrb3ZPQzN0UFE5ZDJmYTg2dUd3VTBCQ3lTcWFwanR4Q0VXbVpqajJwK2dwdHdZdHplRkt3bVBUcW02RFZCUFRud2h6OFMvZFlnRUNoUFBZaHlMZCtUck5ETXlZY1pXQUM5em93bFVBcHlmYm1QOFpFU2p1cE1JTHhVRDk2bWFwSGoxb3JDM1oyYjJ1VGNRaFQiLCJtYWMiOiI2NzYyYTkwZGQ3MjFhNjhjM2EzOGUwZTBmZmQ4MDhmYjNkMjEwOThiZWMwOWJjMmQ2NDI5MWViMWQwYzM0ZDYzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:28:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1800936356\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-569015010 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-569015010\", {\"maxDepth\":0})</script>\n"}}