{"__meta": {"id": "Xf6ad3060bdf3fe9903efa059c01ceab9", "datetime": "2025-06-08 01:15:01", "utime": **********.221748, "method": "PUT", "uri": "/customer/7", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749345300.600899, "end": **********.221774, "duration": 0.6208751201629639, "duration_str": "621ms", "measures": [{"label": "Booting", "start": 1749345300.600899, "relative_start": 0, "end": **********.105014, "relative_end": **********.105014, "duration": 0.504115104675293, "duration_str": "504ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.105027, "relative_start": 0.5041279792785645, "end": **********.221777, "relative_end": 2.86102294921875e-06, "duration": 0.11675000190734863, "duration_str": "117ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51664912, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PUT customer/{customer}", "middleware": "web, verified, auth, XSS, revalidate", "as": "customer.update", "controller": "App\\Http\\Controllers\\CustomerController@update", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=194\" onclick=\"\">app/Http/Controllers/CustomerController.php:194-248</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.0052899999999999996, "accumulated_duration_str": "5.29ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.150655, "duration": 0.00278, "duration_str": "2.78ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 52.552}, {"sql": "select * from `customers` where `id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 961}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 42}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 64}], "start": **********.159489, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:61", "source": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FImplicitRouteBinding.php&line=61", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "61"}, "connection": "ty", "start_percent": 52.552, "width_percent": 13.611}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.170456, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 66.163, "width_percent": 10.964}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.1939402, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 77.127, "width_percent": 12.098}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.1971378, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 89.225, "width_percent": 10.775}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => edit customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1568570284 data-indent-pad=\"  \"><span class=sf-dump-note>edit customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">edit customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1568570284\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.20281, "xdebug_link": null}]}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/customer\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "success": "Customer successfully updated.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/customer/7", "status_code": "<pre class=sf-dump id=sf-dump-434342873 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-434342873\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-418145754 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-418145754\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-963148557 data-indent-pad=\"  \"><span class=sf-dump-note>array:22</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">delevery-&#1593;&#1605;&#1610;&#1604; &#1578;&#1608;&#1589;&#1610;&#1604;</span>\"\n  \"<span class=sf-dump-key>contact</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>email</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>tax_number</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>is_delivery</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>billing_name</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>billing_phone</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>billing_address</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>billing_city</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>billing_state</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>billing_country</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>billing_zip</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shipping_name</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shipping_phone</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shipping_address</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shipping_city</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shipping_state</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shipping_country</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shipping_zip</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-963148557\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1529262359 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">404</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749345295884%7C54%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkVjZit0cjZ3aW1uNzRpbEFndFcxVWc9PSIsInZhbHVlIjoiMGprcmMyRUJjU1R0aS9SVm5na1Fud2J1M0kzZ0xSUDhobFpwK2xtWmptRFF4cEpyOHlsV0RmTVVjM2Vka2VCN1FvUXJ5UU02am92Tm9HL215VFR3aVlBYmkyaFUwdlEvSFJGMWtoSDZNU2EwaEhXSGU4RjFEZXRSWEQxSHpUYkJ1TGx1UmJxUzg2b0VsVDFOVjJTUG9HUkFRVXRsS1hJMy96NVNUNWo1VGVxQUE4QjdqSUVraUE2QUFlU2JJM2tLRDc1TjA2MUxqOWhFekRyemUyeWZvNEJtWkpQcEtHUCtpUlVCMXVsbjVtbS9QeHdOUVB5TXpzU3BhRlpoaG9MaU1IeVpTTjlqTTFuTjNFS1FQYnpQR2htc3gxV0ZZeWwzS2lyRk5nYkNFWXdQZWN0NEZmdlNXU2srNzQrVVhqZEMrQWhrY2piMXR0RGwxYWRmdVBxajdjTndDbzZmemZ0Zzh4cnNjSElDb3BFbDV3NC9ycUQ2TENvdHBvYk4xN3N3QXJKUXVlTFBFUUVHVHFndm45MUFVeU1uU1BGVmQ4VFV5Ylh4Y0wzb25zWU1aVXJ5M1NWYUNOR09NOFdwVWNvSVE2emRxbzRralYxcWlnRVI5YnFFUkZ3STNPOG1rT1d5NGs0b0dLTCtDYVBSMmV1Uyt5WVFMNmVUd0t3VHJsbHkiLCJtYWMiOiI3NjUxYjA0YzM0MmY2MjY3ODRlMGRlZTcyNDllODRlNDUzM2M2MGJhYTA0YzQyNmQ0Yjc4YWRhZmVjZmQ2NGUxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlUzdi95ejlWbEJ4dDVZM01IV0lVbkE9PSIsInZhbHVlIjoiRjVzdGQxMzM0OTVyMjhIY09KaTlSMllIcldUTHhQeklsUFdneUl4SHVmc2VjUmI3REI2em9vLzZOMjU2aEdQZHhXUVpRalA3Nlc2Q2ZKdGNEdlpmOWFxQW0yL2cxTGo1d0RnWll0cGFhOUNRMjNSaHlJL0pvOVZ6SjdIL3ZjWGttc2tEVDAvY0ZDZ1prWnhGNDYxRTNFYkJCanJHU1BPcitYZUpQTy9wQmhoRVZoWWdFejBRdWtVdE9Fc2JwSUczZTRxbUMydE9JUWFNSHdjejhyWlRLNnA3QjFnSXVtUFdMOGxZbC9KcHU5VDNuc1g1MTI1anFvWVc0TTRncWQvQ1FlVWJUd0paZmcxRzAwQ1NuNUppOWxldnhkOVdjTlV2VldRVE5lVUw5SnR6ZVJtYkc4MWlUa09pYy94THJSWlZzdDhZY0ZkR3ZrT08xckNmdHUyS2pKbjhxWmJTNWYyTUgzcXJBN0loM1JadndDRFY3TGl6d0ZsT3ZIS3NOcFgxREdLS1Y1MTFHMVIzSHNmYkhPUHM5dU1oZzk0Vmh0cHh6T1h2QTJOZTF4Y283VXJMOGhkbTYxK1NkWlZiV0FFMTliWFF3RGRoMjBuWWxJelBWZ3pvRkR5K096VlZSL01lZlllYS82WkVjcm94SklyZjlOays4MXhGb2owSnNZTWIiLCJtYWMiOiJkMzc4ZjYzN2VmODRiZWViMTk2YmI2MjlhNzM1YjNiNDFkOTQzN2NkM2FkODA3ZjFkYTZjYjkwYWE0MjQxZDRkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1529262359\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1195244630 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1195244630\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-105538504 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 01:15:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZkaldTOWF4SFFWNEM5enNnV0o2enc9PSIsInZhbHVlIjoib0tKdmxOMGh3QWo1a1ovNFF6aGlQMHJqcGlmNWlTZERLSXVFcVlSSFRiVlIrRFUxd3dXMFdIVVhJL2tBRFNMd3hERlozSEVINkVqSSsxVEsweVVvcmkvOFdVVDVZcGtpQVRVMTVkSVErbDM1YTBTUkRwSEs3dXA5U1ZuSHZpMkZWUnJEUm9JNjc3SXRCN0VRQjZRcXY2Q0JUVmMzb1V3cUdoMncvZEg4R0UvZXhnRVBKZndkYklSaVRjcHpLWFNMVlZWbGFOWlBQdEcraEJLUkF4b1ZvNytOZWRrUnpFWGw1K0V6ZE1DSy9LS3N3RlV6SVpMSmtkSEpyZUt6VStVNUdSek0zRzg1WkpHNk1iRUhGR0pJL2MwZFZ4SDVsL2xWbkJNazhwbkVOSTU4MU9SWFEzZDFMY01PNkpXVmVDK1lsakpRQVZ5cnRDSE45RHVVTGVFNXgrbXZVbjk2YVN4ZDN3VDMvaEcyNHZNZG5oWkhvMlNuNHpUZkJiUjhsck5FeHBtbkhxelplOER6RVRUelVkcndkd0lUcXhMc3RzdG04bmxiV2Fxd0oxbUNmeFNpUTRUazhYRVlWMHhiQTlCRW41anJBR0xMOEFNenhYQ2tjTHZaM3FJNDZvLy9WZDZWVzU3cmZ4WGNEVHFrVFdFcXRFTEhhOU9TdmNlcVRLWVQiLCJtYWMiOiJhN2Y3NTExNWU0YmE5M2RkYzBkY2VmNmZjNDY2NmEzNmU3ZWQ2MTZiNDc3MmNiZjlmM2U2YjdkMDgzYWNhOWNjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:15:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InhiZkxFaXZhR0djb3Z4NGlUZ2VQTVE9PSIsInZhbHVlIjoiTVVjOWhDQzNuRUYvc1JmaEk3WDJYT0dwQURKYW5jRDRlc1ZGZU02amlhN0hxakRZSUJpeXZmL3VRc0V5aXFoNURySXN6NGFTWHg0T2gwOEJkQnhoT1RHbjdwVmMrZS9JK3RuU3grd0QyYmltb0JCOHNjUU5tVWRaUkJxaEQyWU9aRWI2SUdGOEtldUxmNGlsYTlMSVZrWE5lQjBpTDJvMFZTUjFzOEV4T0NuV1NiM3dTQnJLS1FmUWdlT2t0MkZ3ZWJDVFRxb08vbzZycjhRYkFTNzh4ZjNNUXkrWWR1TWhkQlRPTk92c1p1QklTUlZsK2p3cHBkK01wbFJ5STB4QTN3YlJvRWJKaStYdVJNRFdWSXBOR0xlTHpmVzY2SEdRRkNrY1BKTlV0Z0NsaUgyRFVURzJuS1BOVnovWU5XZXlwanhGTWdlWTJVSkNxM3VKMnNrNFlJb0RsVmtlN2xBVkhSam1UellKdjlCeithcDRqaThmODFkU2pveXVjOVR2MVRCcndVOElyd1N2cGt4N1k2c3J0VTlrTE9OY2xEYk1RMnJhQmM0L3crNDRJcllIUGRwbVNJcFlyODQ3S0E3Y05HTUZZWnR6bSs3N0tsNkIyOEF2dVBkeGZCK2J3VVNrZUVUR1dRczNZQUtEZjc2NGhzaTY0OXNPdXdUc3YxUGciLCJtYWMiOiJhNjVjZGZkZjg4OGVhMjQ1MDUwNWEwNGU2Nzc1NmRmZjI1NDFjZjdhNWJlN2M4ZTZkNjI0OTVlNGY1NDNmZTdhIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:15:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZkaldTOWF4SFFWNEM5enNnV0o2enc9PSIsInZhbHVlIjoib0tKdmxOMGh3QWo1a1ovNFF6aGlQMHJqcGlmNWlTZERLSXVFcVlSSFRiVlIrRFUxd3dXMFdIVVhJL2tBRFNMd3hERlozSEVINkVqSSsxVEsweVVvcmkvOFdVVDVZcGtpQVRVMTVkSVErbDM1YTBTUkRwSEs3dXA5U1ZuSHZpMkZWUnJEUm9JNjc3SXRCN0VRQjZRcXY2Q0JUVmMzb1V3cUdoMncvZEg4R0UvZXhnRVBKZndkYklSaVRjcHpLWFNMVlZWbGFOWlBQdEcraEJLUkF4b1ZvNytOZWRrUnpFWGw1K0V6ZE1DSy9LS3N3RlV6SVpMSmtkSEpyZUt6VStVNUdSek0zRzg1WkpHNk1iRUhGR0pJL2MwZFZ4SDVsL2xWbkJNazhwbkVOSTU4MU9SWFEzZDFMY01PNkpXVmVDK1lsakpRQVZ5cnRDSE45RHVVTGVFNXgrbXZVbjk2YVN4ZDN3VDMvaEcyNHZNZG5oWkhvMlNuNHpUZkJiUjhsck5FeHBtbkhxelplOER6RVRUelVkcndkd0lUcXhMc3RzdG04bmxiV2Fxd0oxbUNmeFNpUTRUazhYRVlWMHhiQTlCRW41anJBR0xMOEFNenhYQ2tjTHZaM3FJNDZvLy9WZDZWVzU3cmZ4WGNEVHFrVFdFcXRFTEhhOU9TdmNlcVRLWVQiLCJtYWMiOiJhN2Y3NTExNWU0YmE5M2RkYzBkY2VmNmZjNDY2NmEzNmU3ZWQ2MTZiNDc3MmNiZjlmM2U2YjdkMDgzYWNhOWNjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:15:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InhiZkxFaXZhR0djb3Z4NGlUZ2VQTVE9PSIsInZhbHVlIjoiTVVjOWhDQzNuRUYvc1JmaEk3WDJYT0dwQURKYW5jRDRlc1ZGZU02amlhN0hxakRZSUJpeXZmL3VRc0V5aXFoNURySXN6NGFTWHg0T2gwOEJkQnhoT1RHbjdwVmMrZS9JK3RuU3grd0QyYmltb0JCOHNjUU5tVWRaUkJxaEQyWU9aRWI2SUdGOEtldUxmNGlsYTlMSVZrWE5lQjBpTDJvMFZTUjFzOEV4T0NuV1NiM3dTQnJLS1FmUWdlT2t0MkZ3ZWJDVFRxb08vbzZycjhRYkFTNzh4ZjNNUXkrWWR1TWhkQlRPTk92c1p1QklTUlZsK2p3cHBkK01wbFJ5STB4QTN3YlJvRWJKaStYdVJNRFdWSXBOR0xlTHpmVzY2SEdRRkNrY1BKTlV0Z0NsaUgyRFVURzJuS1BOVnovWU5XZXlwanhGTWdlWTJVSkNxM3VKMnNrNFlJb0RsVmtlN2xBVkhSam1UellKdjlCeithcDRqaThmODFkU2pveXVjOVR2MVRCcndVOElyd1N2cGt4N1k2c3J0VTlrTE9OY2xEYk1RMnJhQmM0L3crNDRJcllIUGRwbVNJcFlyODQ3S0E3Y05HTUZZWnR6bSs3N0tsNkIyOEF2dVBkeGZCK2J3VVNrZUVUR1dRczNZQUtEZjc2NGhzaTY0OXNPdXdUc3YxUGciLCJtYWMiOiJhNjVjZGZkZjg4OGVhMjQ1MDUwNWEwNGU2Nzc1NmRmZjI1NDFjZjdhNWJlN2M4ZTZkNjI0OTVlNGY1NDNmZTdhIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:15:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-105538504\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2131937414 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Customer successfully updated.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2131937414\", {\"maxDepth\":0})</script>\n"}}