{"__meta": {"id": "X364676ffcc1fe2ce58e583ca70c314c1", "datetime": "2025-06-08 00:57:28", "utime": **********.944752, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.266494, "end": **********.944775, "duration": 0.6782810688018799, "duration_str": "678ms", "measures": [{"label": "Booting", "start": **********.266494, "relative_start": 0, "end": **********.846363, "relative_end": **********.846363, "duration": 0.5798690319061279, "duration_str": "580ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.846381, "relative_start": 0.5798869132995605, "end": **********.944778, "relative_end": 2.86102294921875e-06, "duration": 0.09839701652526855, "duration_str": "98.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45052568, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00459, "accumulated_duration_str": "4.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9066129, "duration": 0.00302, "duration_str": "3.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 65.795}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.921848, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 65.795, "width_percent": 13.943}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.930295, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 79.739, "width_percent": 20.261}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/customer\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1903852920 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1903852920\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-934731792 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-934731792\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2024329840 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2024329840\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-20629024 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749344246297%7C44%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InV4VFJ6RSswakh4azJVSmFQWjdtT2c9PSIsInZhbHVlIjoiRkFSYTczK2JZTTBTM2JkTUVwOVN5R3lxMEpjbWJvbElIUUE0WUwrYytIdzM4N3lCTUFmQytYQkRONHlMS25JY2xweHVSK2VvRDlEMjVmZkpRekhWQ1BINFVkeU5MdldXSnl2ZkRUV2VVOWtPM0hodVZMeHlKZ3FjMFB1QkdYbHVIaFpIck5oTTl3YTcwTldlSTJTNWpvdUYrVDd6NjdtSXZGVWszaGd1SEZadXNQWXcrTHZyb3JSWG1yN2wyekd0Qk4yd1NMY1R4ZVlQMUVtdDhpV0Q3SStSZFdVazBMdGdqVkptVnd0UHdaa21SbDlYZEM5cEJEUWVPWXNsc2x5L2owOUwwdFM3em1IL1hiTEhFV0dVMkRLQnBrZDdFUVpLYnNKWFhzYnQvZVFpcFRCZDZyTGZNRytRYUxGZHAwNXdPV0FiRURmMFlqV3h5T0pVQzFaS1B4Ky81SDBMNkdVVUx2N1RGdGlvZkdzT3RWTkRUVVRsVCthNEZBLzhMNzJNbkV5enFUQ0pNWk51RWk1cXhwazRWcThSYlVSZjZqNVZ0SHNKWUhrNDlsZ1E4dTdXUkU2TnJ5cndZQThycmpDeEx5RW1TSS9yUkFucWI0MFNqREptNmc3MjVNUTVXRzZuYVJtdUpFVE1WVEE0YmU1b3c3VUZyc2wxNHBFZ0tDWkEiLCJtYWMiOiIyOTI5MjUzYTEyYmI4MGQ1Mzg3ZmVhYTg5OTQyNTUxYjljZTU4YjBlMTg0ZjY4Mzg1MDM0MGJjNzA2OTk1NmZhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ii9ZSkhkYnVoemMvak12Z21YVmw1K0E9PSIsInZhbHVlIjoieWFjMmhMa0RabnA0MmZ2YzVyS01NSktUbGp5VFllaThLUEwzN1hpeXRXTkkzc3o4bXU0RTdUeC9pZnZ3aFBvbUllSUNKQ0pWMHJrRkkrUFZhQ3BDKzUxcGpxOGQyZmYzM3BkaHQzcUhQTlNES29oUWdneTM4WHBjMlNMZlFVTzNPdjJxZ2x0d1Izb1hMRDYzM1JuZmZKdEhPbXpIL1ZOYVZBQm9rV0lITUJWS3dPQjN5T3MvSmU0b1c0a3pJTjZFUm5TemNFdFlibS9vcDdCRC9QUU1rSEFkK3VlSGJoVjFJc3h3UTVBYWJ3QzZReHlydlFDU3FTaHNVd3YxOWo0YzE4c2pnMkZjOXgrQWpIeWpoTURkSlhjeDZKeXdwaGJkUGhwZW9aVHRZaWRQeU9iYnlDdW1OYzBUQXdxTW5adnZsUWVrTCs4TnZOQmE3bVdFbVJGRm5BK0QrQjd1bUo3VzFMTVRxbWxhc1F4blJURzNMZktiTDJ1OG9UTXgzUnNyQkR0cExqQllKYWF2MVVMWVljdWJ0dko5MEd4bU0yRlRtQi9IQlZMcWpqK095WUlyRUJ2R1BHRGc1VE1aaWxPd0lBZVRucEVGMWZ2bFdvNkxSTm1BVDAwOEhNL0ErMHM1bDRFK1FDRURPZ2o2aTFWbFFNbzUrT3hUd1l5cDN6MFAiLCJtYWMiOiJhMzVlZGJkMDJmNzYzMmMzNjcyYjc5ODA4NWExYWRjMGE3YjY5NDRkNTMyMzQ3MzFjYThjMjMzY2M0NGU2MTVlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-20629024\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-146454553 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-146454553\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1944349394 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:57:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZ4NmY1MGhwaGo4S2E4Y0tFV1E5OWc9PSIsInZhbHVlIjoiZ01kWFhoZmxPc0hCbkd4YjZjTzI3blU3bmxpRXdMMnZUMWdMWkZEZldoRHhhNGt2eXd3QU1aWk5KaFNZUVNFU2V5eXNuZWFUbkJRNXFaZVY0cm5sRnp6OVFTaytVbW9ZeFpPeFRtU09mTDBuM0djSUhyV09pbU5QNjdnMTM2WmRzV1AzdTJHOWM0MWo0Z3ZEQ0VNRWtqbFdYRGFhZEJ1T3FaY1FYemkxWG5ZWWl2cVdzaTZPd2hyOHRib0QwYnJKNml4bWg1YzVvUkgzMXkvVEtEZ1dGNEVwWE1vQncyUjdMc2JKSEE5bE5NcFNwU1NNVS9ZZFlNT2s4UVFHVXF6VUVCMnozRGlqcTNMZHozOExyTjFBdVd0MXdPWk9WWU1xMmxuTTBQdVdKWFZxcXhFNVdwY0RTUFZzM3U4M2loOTM1c2NFcE50ekZaWGZQWStpendLQTF2VkFkcW9ERjhPRVlnUHh2YXZQMlZ0L0JlWENJZzJLcVJ3Nm1GaHVRMTRMaE0weDA4WFViWks3eEdyY3Q0eTBrc3RPSnlGZjJpRytwZGhrSE1lRitPSGExU2xCSVZYQlhaR3EveTRPQ25sb2xCVWpzV1hzcms5M2QxMWNHZXRoVWUyWnUyYU5BWGZFQUpNOHNPdlNSa2Rwd1N3WTNiSDZDNTBCS2VIZ2dvUDUiLCJtYWMiOiJhYzNiMzQ3ZDlmZjZmZGJlMWVhMmRhODg0ZWE2ZmNiNTQ5NDc1ZmQ5ZDFmYzM0MzU0ZTUyMWFjODk0MjRmZjI5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:57:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkFDalJ4RzVxYThzMkZxNjhlMTl0a3c9PSIsInZhbHVlIjoiaXpxZ3MvRXhodm5VU2txbTVhSzdxOW52QWo3azlyd3RDclhwM1FoYlpabTUyMHJTeCtmQks3endDdTdaSmluRlNJNVlXb1V4Q1dJSFVFaGRZNkdGSXpjU3h4MXFodU5lVUIydUxDa3d4dkVEWklOV2lVSVA3TEYzalYwckk5aDMxSkR2bUpDTC9LUUJrYjZwa2NORXhpTEdXSUp3dU9qNmZldHhTUERySEp5NnFiMVIyTEl2ZUMwUDJaMldnaXRsbXhYbjdjZmJLNHFiWnoyOG1BenZiZC9waEhaQytRMUhQYys4YmpoeDI2N2ZDN0d2eUpKVnJUb0ZkSEdyVEJjMlNrVHppQ0ZmTk1JbWUwUGNlQ1JJWWFjRkhuV3BtM0crcWxzNm5nZzB0Z3FIVy9MaEEwRTcvcENOR0FNQWxJNGErMnBvdFpHQktXd0ZwVTJHTlgwSEdhblBZc2pEa20yQ0pocWVaRUdjWFI5WDFFUVpTVXJkUWpYZW9EMGs1ZzhqOGpYVXhQdFR0K2x5ODZFNnVFeHJpdzhTc3hncTRHUGVUb2ZUSDI4Q2xaOXNCTkprNE5Xek5Qc2tseFd3cTloYSsvRFk5ZDZaUDYxbk14dENja2FNYkk4RTZ2bUlHQjlLbWtpMmtMSkNvUXBWdmhpZXZOMkJaODZhRFMvc1NNZkwiLCJtYWMiOiIxYmE5YmY4ZjgzZjc3OTMwMjU3ZWQwNDNhYzFiZDNlMTBiN2NlYTJiMGU1YjBmZTg0ZTA5NTkxNTNkZGQ0NTYyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:57:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZ4NmY1MGhwaGo4S2E4Y0tFV1E5OWc9PSIsInZhbHVlIjoiZ01kWFhoZmxPc0hCbkd4YjZjTzI3blU3bmxpRXdMMnZUMWdMWkZEZldoRHhhNGt2eXd3QU1aWk5KaFNZUVNFU2V5eXNuZWFUbkJRNXFaZVY0cm5sRnp6OVFTaytVbW9ZeFpPeFRtU09mTDBuM0djSUhyV09pbU5QNjdnMTM2WmRzV1AzdTJHOWM0MWo0Z3ZEQ0VNRWtqbFdYRGFhZEJ1T3FaY1FYemkxWG5ZWWl2cVdzaTZPd2hyOHRib0QwYnJKNml4bWg1YzVvUkgzMXkvVEtEZ1dGNEVwWE1vQncyUjdMc2JKSEE5bE5NcFNwU1NNVS9ZZFlNT2s4UVFHVXF6VUVCMnozRGlqcTNMZHozOExyTjFBdVd0MXdPWk9WWU1xMmxuTTBQdVdKWFZxcXhFNVdwY0RTUFZzM3U4M2loOTM1c2NFcE50ekZaWGZQWStpendLQTF2VkFkcW9ERjhPRVlnUHh2YXZQMlZ0L0JlWENJZzJLcVJ3Nm1GaHVRMTRMaE0weDA4WFViWks3eEdyY3Q0eTBrc3RPSnlGZjJpRytwZGhrSE1lRitPSGExU2xCSVZYQlhaR3EveTRPQ25sb2xCVWpzV1hzcms5M2QxMWNHZXRoVWUyWnUyYU5BWGZFQUpNOHNPdlNSa2Rwd1N3WTNiSDZDNTBCS2VIZ2dvUDUiLCJtYWMiOiJhYzNiMzQ3ZDlmZjZmZGJlMWVhMmRhODg0ZWE2ZmNiNTQ5NDc1ZmQ5ZDFmYzM0MzU0ZTUyMWFjODk0MjRmZjI5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:57:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkFDalJ4RzVxYThzMkZxNjhlMTl0a3c9PSIsInZhbHVlIjoiaXpxZ3MvRXhodm5VU2txbTVhSzdxOW52QWo3azlyd3RDclhwM1FoYlpabTUyMHJTeCtmQks3endDdTdaSmluRlNJNVlXb1V4Q1dJSFVFaGRZNkdGSXpjU3h4MXFodU5lVUIydUxDa3d4dkVEWklOV2lVSVA3TEYzalYwckk5aDMxSkR2bUpDTC9LUUJrYjZwa2NORXhpTEdXSUp3dU9qNmZldHhTUERySEp5NnFiMVIyTEl2ZUMwUDJaMldnaXRsbXhYbjdjZmJLNHFiWnoyOG1BenZiZC9waEhaQytRMUhQYys4YmpoeDI2N2ZDN0d2eUpKVnJUb0ZkSEdyVEJjMlNrVHppQ0ZmTk1JbWUwUGNlQ1JJWWFjRkhuV3BtM0crcWxzNm5nZzB0Z3FIVy9MaEEwRTcvcENOR0FNQWxJNGErMnBvdFpHQktXd0ZwVTJHTlgwSEdhblBZc2pEa20yQ0pocWVaRUdjWFI5WDFFUVpTVXJkUWpYZW9EMGs1ZzhqOGpYVXhQdFR0K2x5ODZFNnVFeHJpdzhTc3hncTRHUGVUb2ZUSDI4Q2xaOXNCTkprNE5Xek5Qc2tseFd3cTloYSsvRFk5ZDZaUDYxbk14dENja2FNYkk4RTZ2bUlHQjlLbWtpMmtMSkNvUXBWdmhpZXZOMkJaODZhRFMvc1NNZkwiLCJtYWMiOiIxYmE5YmY4ZjgzZjc3OTMwMjU3ZWQwNDNhYzFiZDNlMTBiN2NlYTJiMGU1YjBmZTg0ZTA5NTkxNTNkZGQ0NTYyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:57:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1944349394\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1618174955 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1618174955\", {\"maxDepth\":0})</script>\n"}}