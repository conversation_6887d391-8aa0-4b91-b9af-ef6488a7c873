<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class CashierRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // البحث عن شركة موجودة لإنشاء دور Cashier تحتها
        $company = User::where('type', 'company')->first();
        
        if (!$company) {
            $this->command->error('No company user found. Please run the main seeder first.');
            return;
        }

        // إنشاء دور Cashier إذا لم يكن موجوداً
        $cashierRole = Role::firstOrCreate(
            ['name' => 'Cashier', 'created_by' => $company->id],
            [
                'name' => 'Cashier',
                'created_by' => $company->id,
            ]
        );

        // تحديد الصلاحيات المطلوبة لدور Cashier
        $cashierPermissions = [
            'manage pos',           // مطلوب للوصول لدالة create في PosController
            'show pos',             // مطلوب لعرض تقارير POS
            'show pos dashboard',   // مطلوب لعرض لوحة تحكم POS
            'manage customer',      // مطلوب للتعامل مع العملاء
            'show customer',        // مطلوب لعرض العملاء
            'manage financial record', // مطلوب لإدارة النقد
            'show financial record',   // مطلوب لعرض السجلات المالية
        ];

        // التأكد من وجود جميع الصلاحيات المطلوبة
        foreach ($cashierPermissions as $permissionName) {
            Permission::firstOrCreate([
                'name' => $permissionName,
                'guard_name' => 'web',
            ]);
        }

        // إعطاء الصلاحيات لدور Cashier
        $cashierRole->givePermissionTo($cashierPermissions);

        // إنشاء مستخدم تجريبي بدور Cashier إذا لم يكن موجوداً
        $cashierUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Cashier User',
                'email' => '<EMAIL>',
                'password' => Hash::make('1234'),
                'type' => 'Cashier',
                'default_pipeline' => 1,
                'lang' => 'en',
                'avatar' => '',
                'created_by' => $company->id,
                'email_verified_at' => now(),
            ]
        );

        // تعيين الدور للمستخدم
        if (!$cashierUser->hasRole('Cashier')) {
            $cashierUser->assignRole($cashierRole);
        }

        $this->command->info('Cashier role and user created successfully!');
    }
}
