{"__meta": {"id": "Xd47a65a00d2c8fb43500f8a1d73e4a5e", "datetime": "2025-06-08 00:57:31", "utime": **********.310941, "method": "GET", "uri": "/customer/7/edit", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749344250.559844, "end": **********.310976, "duration": 0.7511320114135742, "duration_str": "751ms", "measures": [{"label": "Booting", "start": 1749344250.559844, "relative_start": 0, "end": **********.131972, "relative_end": **********.131972, "duration": 0.5721280574798584, "duration_str": "572ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.131987, "relative_start": 0.5721430778503418, "end": **********.310979, "relative_end": 2.86102294921875e-06, "duration": 0.17899179458618164, "duration_str": "179ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52060736, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 3, "templates": [{"name": "1x customer.edit", "param_count": null, "params": [], "start": **********.276833, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/customer/edit.blade.phpcustomer.edit", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fcustomer%2Fedit.blade.php&line=1", "ajax": false, "filename": "edit.blade.php", "line": "?"}, "render_count": 1, "name_original": "customer.edit"}, {"name": "1x components.required", "param_count": null, "params": [], "start": **********.296801, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/components/required.blade.phpcomponents.required", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fcomponents%2Frequired.blade.php&line=1", "ajax": false, "filename": "required.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.required"}, {"name": "1x components.mobile", "param_count": null, "params": [], "start": **********.300997, "type": "blade", "hash": "bladeC:\\laragon\\www\\to\\newo\\resources\\views/components/mobile.blade.phpcomponents.mobile", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fresources%2Fviews%2Fcomponents%2Fmobile.blade.php&line=1", "ajax": false, "filename": "mobile.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.mobile"}]}, "route": {"uri": "GET customer/{customer}/edit", "middleware": "web, verified, auth, XSS, revalidate", "as": "customer.edit", "controller": "App\\Http\\Controllers\\CustomerController@edit", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=175\" onclick=\"\">app/Http/Controllers/CustomerController.php:175-191</a>"}, "queries": {"nb_statements": 8, "nb_failed_statements": 0, "accumulated_duration": 0.02352, "accumulated_duration_str": "23.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1754189, "duration": 0.01728, "duration_str": "17.28ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 73.469}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.206818, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 73.469, "width_percent": 2.976}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.2312918, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 76.446, "width_percent": 3.359}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.234792, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 79.804, "width_percent": 3.359}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 179}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.244542, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:179", "source": "app/Http/Controllers/CustomerController.php:179", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=179", "ajax": false, "filename": "CustomerController.php", "line": "179"}, "connection": "ty", "start_percent": 83.163, "width_percent": 3.869}, {"sql": "select `custom_field_values`.`value`, `custom_fields`.`id` from `custom_field_values` inner join `custom_fields` on `custom_field_values`.`field_id` = `custom_fields`.`id` where `custom_fields`.`module` = 'customer' and `record_id` = 7", "type": "query", "params": [], "bindings": ["customer", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/CustomField.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\CustomField.php", "line": 63}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 180}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.250057, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "CustomField.php:63", "source": "app/Models/CustomField.php:63", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomField.php&line=63", "ajax": false, "filename": "CustomField.php", "line": "63"}, "connection": "ty", "start_percent": 87.032, "width_percent": 6.803}, {"sql": "select * from `custom_fields` where `created_by` = 15 and `module` = 'customer'", "type": "query", "params": [], "bindings": ["15", "customer"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 182}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.254926, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:182", "source": "app/Http/Controllers/CustomerController.php:182", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=182", "ajax": false, "filename": "CustomerController.php", "line": "182"}, "connection": "ty", "start_percent": 93.835, "width_percent": 2.041}, {"sql": "select `name`, `id` from `warehouses` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 183}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.259886, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:183", "source": "app/Http/Controllers/CustomerController.php:183", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=183", "ajax": false, "filename": "CustomerController.php", "line": "183"}, "connection": "ty", "start_percent": 95.876, "width_percent": 4.124}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => edit customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-967021045 data-indent-pad=\"  \"><span class=sf-dump-note>edit customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">edit customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-967021045\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.242655, "xdebug_link": null}]}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/customer\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/customer/7/edit", "status_code": "<pre class=sf-dump id=sf-dump-1528255333 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1528255333\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1434043903 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1434043903\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2116778455 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2116778455\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1018688376 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749344248720%7C45%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlRpYTVkSEhyb0JhQnB4Ly80elM5SGc9PSIsInZhbHVlIjoiVWFZKy85dEhYSGxLRE1pRFV0WUhHazNMRFdFNHRsWkVWY1BPM0NnL0lKdjBUNEhiSEFMQVIzSnE1L1ZIN2tjYncrZGtLU3FITEM5aWxqZ3RzWnFQbWV0ZW45YThlK3BNQkpEOHYwb1NrZU81Y09KZ20xbkZPdEZpcVlQYkxEeU51aWtDL29QVGtQY1lSNHhiTTdGcGpseVl0Rm43VVZUUGZFcEExS2wvSUw3M1ZLSmhwRjBHRUtUNTFFbGdXUGJ3Q0ZjbTA2c2swRG5oRVM2Mk1UU0pRMUVkMk5Xek5GVHJ5blVBNGJrNElxTTZ6WC9waTF3NzZvTENqZ0wzVGFmcTArd09Tb2JqbXl3VmNDb1F3TVpIanBBM0pVUDcyUE9sRUZBQ05wMnFmd3lkbXRpM1FIU1RESXU0RDdxTTNTZUIyTUFEaUxrN3ViUXE0MHpCeFBaQWx0aTl4UmZQY1pwWWQ3OWpibkdQUlUxK1NEYmRaZUJBQkNybmY2RjFYVlRnNU1aSFRKNCthK2lpQVVaL0Jpc09oTlhXWTEyaEx0a2xTeGJneUwzdEYzRU5aYjhiY1YzVm92T2grbWlrbXNNeXVydEUwdEp5Q1dueGM3Sm9sUWVTUEl4S2ZjUG0zUDhPQysrbFlMQ1pGTlBvVHIvSjZDZmY2L21xQWpScHJKWjciLCJtYWMiOiI0Njk3N2I1YjYxYTcwY2I3ZDg2Mzc1YzcxNjM3MzQ0YzY0YzBjZDRjYjEwMzg5OTczNTFkMzJmNzBiZDIwYjZhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InNqNnUrNVlya1lRc3BpK3BTc2pmWHc9PSIsInZhbHVlIjoiRzFDMjFPZ0FZVG5wb05ucngvU2I2S3ZiMU14bjZxVHYzZ0tuM2NTT2NSdkRGTERFanlhajFDV2dGZzR0ZVQxcjAwUDJBNitzM0VWV3hVSVVWbDN5dWtvak5lR2lkOUg1eWhxeWtmNjNzeGo4WTJ0Mk5OTEhxcDYyU25ya2NZWWhTb2tJQzdIYWFPYjE1T2NxUUIvYjFBSmFOSU9ZdUxxYWw2OW0rRVJDSXp6VlJRS2FUb3NHZ01JN0RCbHRRbzkwZFYzZkUrSjVsMFZqUWQ4aEM1ZFFWSElwOEgwbnRDcTFENk9YUGZpZXVDK0pYUnZjZW10Zm9MNGY5L3NTdmFCaG5uQzAvbmZwTW9PeVZUTFBYa2dCSENTMU1WTGY2Z08rMXJ6KzFBSEhCOWdNR1JZSisxYzJRMGNiZ1BFSmdwZ2ZVOVVSNGRQMTN5SlBBRG9QcXU4WFU1aXRRQnlFVDdUNkhjZXI0Y0hwYTE4MWVQWG1LWUZJWDV0aUgrOTYyWnluTnZqM3E1dWdrendsUG9WSHZGd3RTNVB1REk2NzRwcjJ2Tlp2RzBoaFUxaUVzVE5MUVFJQjlGNDY1b0RDdk1YazI1SzdiaE02SzBoamVSLy9ZWVVXMlRwQVV4V2xpZFQ5SlRnaVZPQ0hLaG54NW9JMXA0b01YVXlaUUFsMDNEN1AiLCJtYWMiOiI1YmYzNGVkMGNjYThiZWZlMjBmZGI3MGE3OTEyNDFkNDQ1NzE1ODdiODE5NzllYzZkMDAzMDk4MWI3MzFkMzViIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1018688376\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1048715240 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1048715240\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1937008342 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:57:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNDVENZWWZYL2FpaVkyalRsOVNTaVE9PSIsInZhbHVlIjoiLzFZdHRmTTlHUFpTbWorMk9RYmJjWS9na1Vtd2NOV2tWOEZTQklhelEyNzR1VFlyOVZURm5HWkdVSVhxdUxtcTFVajlxaGM2cTZkekRXdENGckNvQkVQYnhtTXprdnhsVWhGdlVPU0p6S2hZeW1KRFNCTHhDa2NRdW5nN3NXZXg1MXFoWThpYmt2MXZtckJvTExaTHJkQkZpUlhDa0VGektGaHhGMjlhdUFkcUtWaGRqVFZQNGNiS3pPemRBakNmOWhSNXRHM1hlZm5hS3R2SDkwVUJqRTNQTVptSTdnbFZpdTFBOWFYT0I0M2J5SkxOS2FGVmFzNk1DcmhSNTluUEdsa3A4UlZkTGMxMUhxWUY1azFGQS9GaExvOUV4NGgzOUU2TDFaNnJCeUF2SDJoRnNuQ1BBR3FEbjlMVDV1OEtWU0xVVFFXdklPcDMvcDVIT0hpcFhETDkvRHl2QTgrYzMwczMzRzFqOWxJc3ZyMU04QTM5aXpZMVV5M29kU1VYT3BPaEJROHFuRlRGSVBrZ2VGV3RHK25VNzRqaVIyTnBGajl3Mm1Ca29zUGtURWhyYktGOXRMRlBCL1g2OGw4TkphYUh6OUhyZ0R1RGNaRVRpWlpNNWFwRnBFS0VOUlJxV3hxYlk1dFNFcGFVb3BpYkdGRFpMbEJGdGRNakt2eEwiLCJtYWMiOiI4MTI4ZTA0Y2M3ODhmNmI1MjA4NDg0MjI2MjMxNDk3N2VhZTJjNzVlMDYzNmQ2ZTRiNzNmYmQzMzdlYjMxNjg3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:57:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjNidWh5UEFFc2R6UjAvQUhXWVFtelE9PSIsInZhbHVlIjoic1hzYTVpbkN2d3YzNHBPSzJPY3RyNjJyQTRDQWREN1hib0hobC9kMzY0S3dEZ1lmVGw4cGw4ZE5aNjdDSTRBaElGYmRzaVRPRk52VzJ4STQyMHdoUE5taTdpZWE4amNhWWZVYU1INncybklmWEkwQ05IR1c3L1hZa1pPWGhuTHh1YTJKaUpJQ1NkcTdTc0RSMkE3eVFJOStqeHUvWXRBN2VuL2t1c09xSzJXZm9ZV1U2aU1GMVlodmc2OC9ubitQWVNNQXB0S2UyK3VrTzdMdjJVRHJkaG9lOGFXWVgzTXlvc2tyWGtWb3ZQWVZpaExkSERGRUkrdytVTjBad00zR0hxYTc2cDM2TWsvczlJQTZDaWlFd3U5NTlXWjRLdkNsaDZnV1RLMDdjdW1Cb21nclpWWXhWTkRTbUI4RlF1aUVDWUM5MjN4d3ZHWGFaQmlMVWRlaUx6NC9LMktHd2NZUkJSaEZPeHlEUjdlWURONVM4YkhPeTRnam1RSjRleXpTZGJ2aHJ0d1V0WlB5Y1IveWlDbFlONTdYVTNhRWtQTEQrR2psc1oxWE9zYU8wZWFGT2x5QXNYKzVLbzNER2dLOHV1YmswQ05hcXdMYjl6aVpsbUVFQ21PbHJjWVJUektPR1J5U0FZZkZmb09yeEtTa0VReWcxdjhDS2lSVUZYamQiLCJtYWMiOiJlNWYxNTVlNjc1NTdjZTA0ZDM2N2JmMGEyZTJjMTM2NzYwZWYzMTAyMWMxMTg0ZTEzZTYzOWQ0MzZiYjgxZjg0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:57:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNDVENZWWZYL2FpaVkyalRsOVNTaVE9PSIsInZhbHVlIjoiLzFZdHRmTTlHUFpTbWorMk9RYmJjWS9na1Vtd2NOV2tWOEZTQklhelEyNzR1VFlyOVZURm5HWkdVSVhxdUxtcTFVajlxaGM2cTZkekRXdENGckNvQkVQYnhtTXprdnhsVWhGdlVPU0p6S2hZeW1KRFNCTHhDa2NRdW5nN3NXZXg1MXFoWThpYmt2MXZtckJvTExaTHJkQkZpUlhDa0VGektGaHhGMjlhdUFkcUtWaGRqVFZQNGNiS3pPemRBakNmOWhSNXRHM1hlZm5hS3R2SDkwVUJqRTNQTVptSTdnbFZpdTFBOWFYT0I0M2J5SkxOS2FGVmFzNk1DcmhSNTluUEdsa3A4UlZkTGMxMUhxWUY1azFGQS9GaExvOUV4NGgzOUU2TDFaNnJCeUF2SDJoRnNuQ1BBR3FEbjlMVDV1OEtWU0xVVFFXdklPcDMvcDVIT0hpcFhETDkvRHl2QTgrYzMwczMzRzFqOWxJc3ZyMU04QTM5aXpZMVV5M29kU1VYT3BPaEJROHFuRlRGSVBrZ2VGV3RHK25VNzRqaVIyTnBGajl3Mm1Ca29zUGtURWhyYktGOXRMRlBCL1g2OGw4TkphYUh6OUhyZ0R1RGNaRVRpWlpNNWFwRnBFS0VOUlJxV3hxYlk1dFNFcGFVb3BpYkdGRFpMbEJGdGRNakt2eEwiLCJtYWMiOiI4MTI4ZTA0Y2M3ODhmNmI1MjA4NDg0MjI2MjMxNDk3N2VhZTJjNzVlMDYzNmQ2ZTRiNzNmYmQzMzdlYjMxNjg3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:57:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjNidWh5UEFFc2R6UjAvQUhXWVFtelE9PSIsInZhbHVlIjoic1hzYTVpbkN2d3YzNHBPSzJPY3RyNjJyQTRDQWREN1hib0hobC9kMzY0S3dEZ1lmVGw4cGw4ZE5aNjdDSTRBaElGYmRzaVRPRk52VzJ4STQyMHdoUE5taTdpZWE4amNhWWZVYU1INncybklmWEkwQ05IR1c3L1hZa1pPWGhuTHh1YTJKaUpJQ1NkcTdTc0RSMkE3eVFJOStqeHUvWXRBN2VuL2t1c09xSzJXZm9ZV1U2aU1GMVlodmc2OC9ubitQWVNNQXB0S2UyK3VrTzdMdjJVRHJkaG9lOGFXWVgzTXlvc2tyWGtWb3ZQWVZpaExkSERGRUkrdytVTjBad00zR0hxYTc2cDM2TWsvczlJQTZDaWlFd3U5NTlXWjRLdkNsaDZnV1RLMDdjdW1Cb21nclpWWXhWTkRTbUI4RlF1aUVDWUM5MjN4d3ZHWGFaQmlMVWRlaUx6NC9LMktHd2NZUkJSaEZPeHlEUjdlWURONVM4YkhPeTRnam1RSjRleXpTZGJ2aHJ0d1V0WlB5Y1IveWlDbFlONTdYVTNhRWtQTEQrR2psc1oxWE9zYU8wZWFGT2x5QXNYKzVLbzNER2dLOHV1YmswQ05hcXdMYjl6aVpsbUVFQ21PbHJjWVJUektPR1J5U0FZZkZmb09yeEtTa0VReWcxdjhDS2lSVUZYamQiLCJtYWMiOiJlNWYxNTVlNjc1NTdjZTA0ZDM2N2JmMGEyZTJjMTM2NzYwZWYzMTAyMWMxMTg0ZTEzZTYzOWQ0MzZiYjgxZjg0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:57:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1937008342\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-720945016 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-720945016\", {\"maxDepth\":0})</script>\n"}}