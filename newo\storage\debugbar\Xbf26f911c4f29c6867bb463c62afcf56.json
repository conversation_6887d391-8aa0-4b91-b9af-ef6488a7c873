{"__meta": {"id": "Xbf26f911c4f29c6867bb463c62afcf56", "datetime": "2025-06-08 00:57:40", "utime": **********.24154, "method": "PUT", "uri": "/customer/7", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749344259.608122, "end": **********.241563, "duration": 0.6334409713745117, "duration_str": "633ms", "measures": [{"label": "Booting", "start": 1749344259.608122, "relative_start": 0, "end": **********.094822, "relative_end": **********.094822, "duration": 0.48669981956481934, "duration_str": "487ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.094834, "relative_start": 0.4867119789123535, "end": **********.241565, "relative_end": 1.9073486328125e-06, "duration": 0.14673089981079102, "duration_str": "147ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51664328, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PUT customer/{customer}", "middleware": "web, verified, auth, XSS, revalidate", "as": "customer.update", "controller": "App\\Http\\Controllers\\CustomerController@update", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=194\" onclick=\"\">app/Http/Controllers/CustomerController.php:194-248</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.03198, "accumulated_duration_str": "31.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.134939, "duration": 0.02212, "duration_str": "22.12ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 69.168}, {"sql": "select * from `customers` where `id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 961}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 42}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 64}], "start": **********.16333, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:61", "source": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FImplicitRouteBinding.php&line=61", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "61"}, "connection": "ty", "start_percent": 69.168, "width_percent": 3.127}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.174602, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 72.295, "width_percent": 1.845}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.197757, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 74.14, "width_percent": 4.847}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.2020838, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 78.987, "width_percent": 1.845}, {"sql": "update `customers` set `name` = 'delevery-عميل توصيل', `customers`.`updated_at` = '2025-06-08 00:57:40' where `id` = 7", "type": "query", "params": [], "bindings": ["delevery-عميل توصيل", "2025-06-08 00:57:40", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 238}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.221193, "duration": 0.00613, "duration_str": "6.13ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:238", "source": "app/Http/Controllers/CustomerController.php:238", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=238", "ajax": false, "filename": "CustomerController.php", "line": "238"}, "connection": "ty", "start_percent": 80.832, "width_percent": 19.168}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => edit customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1675957360 data-indent-pad=\"  \"><span class=sf-dump-note>edit customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">edit customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1675957360\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.208015, "xdebug_link": null}]}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/customer\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "success": "Customer successfully updated.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/customer/7", "status_code": "<pre class=sf-dump id=sf-dump-463285656 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-463285656\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1529265957 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1529265957\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-265975801 data-indent-pad=\"  \"><span class=sf-dump-note>array:22</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"19 characters\">delevery-&#1593;&#1605;&#1610;&#1604; &#1578;&#1608;&#1589;&#1610;&#1604;</span>\"\n  \"<span class=sf-dump-key>contact</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>email</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>tax_number</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>is_delivery</span>\" => \"<span class=sf-dump-str title=\"2 characters\">on</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>billing_name</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>billing_phone</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>billing_address</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>billing_city</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>billing_state</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>billing_country</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>billing_zip</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shipping_name</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shipping_phone</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shipping_address</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shipping_city</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shipping_state</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shipping_country</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>shipping_zip</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-265975801\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1043463713 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">404</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749344251538%7C46%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InNRSmVuZnpvSzk3SUFrMllESy9KZWc9PSIsInZhbHVlIjoiQnljRGZoVzhxVThwUWNMOWpjYzJOSFBjejA3UlFzY2tUL08waUFGdTFqQldUbmVBaGV3cUxwdWtmZEJ2NXRjQ1ZVMzhHL1BvRFR6VU9YSXNoZkhaYUZxcDZQUHZtcWE2Z1dySkFjMDBZUFJkVzRxbVFnZmJZRFlXUmM5enVIQ1AyL1NNK1hUenMrajFEczk4N1hpNnNxQ05FS2lsRnErYUtlTUh0VklvYjZESnQ3R0pMcFllNUxvT2s5QTNyQjRGK0FPdDUwaDZlZEVIeUd3Sytwc0xRK1NPNmlMdWRNMkI2eTdGckYrRjMvUlYzZnRUWm82SzFQOWl2czRBRng5Zy9IUU9DK3ppQ3cyZjlJS3BjRUJIQkFwdktiWXNMTlNwNG1wc1ZqSk1BNDFWZHhaMDh1ajkxR0phNWdnWnAvcENZUC9VeTNxczd3NE1oNlJoamJ6SXp3WE1VQXBFR21WSkNHRWoxRE9RZWdLOEhJWUVGN2hQTUcvNC9uZHgwQ1QzRVlIWmtxQ3k1M1Z1dGJHRW1KVDhNTXNkQnZsbG1sL1kyUGgzVmRoMW5JTFpQRWQwSHAvTmJEdWU3UXYxV1dFWW11UUNDR0g1aVQ3eVpOc25CbTRUREY2MmNVSGJiamNwajJUV3d4ZDBsVi9MZGJkV1ltUjVVVGNnbWg5OEtWTS8iLCJtYWMiOiIwNDRlNDMwMzJhZWNkNDI3YzA5MWViMTI5Mzg2YTVjNTIzNTIxOWQyNDQwZGQ4MWIwMTYxMzk0YmViZmU1MWQ3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjNWUzdCTE9yYTZtdCtDd3BvTjZ2K3c9PSIsInZhbHVlIjoiaVk3TTFSb2JobDJmY3pPZUh0eU81SkhmblVxMHJwdHErYUR1OUJzcEJ0N2FRYzV1TzdjTWlEN0lOWTI0b0U3alpzM1pRQ1BtYjRPK2t5MHVoVTdEdk5CcllmL0ZQekxXMTMxOExIclRoUHdUVFczRjV0S2ZUNXJuN2hRVmtXazFNOE0vV0Z5WmdiZ1N1aDJTT3ZtYjc1RjZBVVVhU2U0cjdCQjFQVEduZk05ell5MDlEL3UrNjV3OXZyZWoxcUN3RFB1RlpYcy9CdVVlQmtmQWNJN1cwTktCL25aandMd0RqOVRPK2VPcHo4TmFlamJVYTZMaFlkTVQ4TUpBdnZHekxselBTRmw3WGpTa3pnUEJlRmNGNjkvVmkxTWJCVEt3anJGTWx6Y2NHQVFqaC9BODBuNDVFR2dxeEE4NGw3UXlUSURPS2Z0emFKWjRWcVZ0dy9KWG83VEpldkQ2Z2dsTlViODdhRUlPRWV4TnVxcElHbndNbmZOM2hncnBUM212bDl4U1BPdk9Ib3JwcjRtWVQzK1ZvczZCdXZ4bXJYcC8xWnJ1aVladnA3VE11cW1zbjgzenlDN2R5K1VDdzlTR0NsSk51VExua3pML1RDYzRuMXZDYmtTZGo4eGc1UGxCUDB5cm9Ga2lMb3hMTlNsYW50QndrR1VwZ1kyWnRCRGgiLCJtYWMiOiJjNzc0NjU4NDlhYjc3MzBhOTFkNDJmOGI2Y2RiMzQzYmEwZjcyNjAxMzZkMDBiYTBmOTIzMTg0YTg2N2M5NWY2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1043463713\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1185167370 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1185167370\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-45003629 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:57:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InNUU2d5NldmamRTUEpQNWxzd01uWmc9PSIsInZhbHVlIjoiS1NxN0pldGxrZVVYTGM3elBaalEvMnlWMFd4K2lYZUIvTUFvSVFDa0tZdFp5endXQk9sbkh5SXBrZHRYUWlEckhhUWx0WXBDMGJVSVdpY2dRRE1uNUtQaHN6WTJ1Ly9oelNuYzUwa295TXV6c0RCRmlKOVV4eXRTeXliWjkrQUgrd2VodEZzU0U3THBIZTlmMU12TVNBcVZ6V2hkUzhWcDQvSWFNKzV2R1Z0ck4ybVdKeVNacXNGUXM4czdkSnNOMmtlZXRDTjhMSkM1WGVhSExUcWg0WktTUU1CUktwRnM5c25FMzNGSCt3UGhPR0FCUEtOUWpDRlpXVWN0VC9ybDlBYnFVZ2ovZURFclN2cVN0RThTYnVMSVJuN1hzc3hKVXh6QVluUksrazkvSmozazAwZnhvbVlWeXMybHMxcVZFS0hMaGc4RU9laThRclhWb2VRMFZlUmRVckwzVEsreldscFVOSkZNSmF1RER5U0ZzcXdGYUljU3lXK2ZzbEV0S21id3V1MVZkV2FNd3NtK0JXT2pJOVNXeDBMeDVpU3FDM1o3L1dhVmloZVVUY2RTRzgwT1pBbjZscnZjbUlGZ2taZFJCQ0hwZFhtR3lZZVpzMVRuaTVyRStydUcrTFl3MkwxYkU0eUhyV2hlbXVIYWJUd3RZdExvZ1hTRDNkdlIiLCJtYWMiOiIyZDI0ZmUzZTQ1YjMxZjdmMGEyZjdhM2JmYjMxMThlNjQ2MjI5NDIzNGE1YmQzMDljOWM5NjhiOGM0NTA5NWUyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:57:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjhFWHlPaENsU0NXVk1MVUpHaFdqY3c9PSIsInZhbHVlIjoiQVhwRld6SXhDUFBFU29wczVIMHBlbEllZy9GanFkZ0pxNmFFUlVGVVNPeWRiMlMzTFhiVU9rV0NTUC93blNpTjJ0WDJjZ2F1Q25LRTNRWDBHZEhEV3lkVzRGOUJOS1RRamRnVGVkM2FOTG5OVXB0UzVxMnZGczVhVFM1M2tGYkZMbkMvWllERDRONVpRU0xPKzdDNUZ3cnBaaDArWUJrK0JlTTBMUC96VkdZOXFGeWUwN2pEdU1uako4Uk1VTFk1RlRYYVUzMWhPVWwzZGQxU1BFVjNhcWhMWjJ3RW02OWZ3dHJYeFEyTkxGZHp2dmVLVFZOME1FUktSN2E3eUltUmxyb3RPak5HZ2RaWXFManRBOXk1Y1JJMk0zSzUvUGdWVVVWVDB5QWlQMmtuOXNsVHVnS3BzUXpweks0RkExN0ZrbUl4L1phc1N3bzkvZjVQeWpCcy9lWEcyTGdXN2l3aGp3SWVyTm8zdzJsYkE2MHRjNXhaeVlDbzkzMS91cGVBVXlnbDY2VkdXYi9LUTA2NWtUZVpQdmFSTnRyZ1BNeHhkTlpxYTFRV1lybXRJdnNLYmpDQ3ErVWtOT0pVakNYMDlXQTIxRlZQVGtwZzVFSk5reWMyK3dOenl4YmUrQ2lZQ1p6NHF3VzJMT0lsZ3R2Yjh0OXZPUDZOckhka254aTIiLCJtYWMiOiI2ZTY5MTgwZjY3MGU0OWUwY2RiM2IwNzZlMWQ4MzZkNWE4MTk3MzNiNTU4MmU4MzMwODA0MTgyYTRmZDczMWIxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:57:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InNUU2d5NldmamRTUEpQNWxzd01uWmc9PSIsInZhbHVlIjoiS1NxN0pldGxrZVVYTGM3elBaalEvMnlWMFd4K2lYZUIvTUFvSVFDa0tZdFp5endXQk9sbkh5SXBrZHRYUWlEckhhUWx0WXBDMGJVSVdpY2dRRE1uNUtQaHN6WTJ1Ly9oelNuYzUwa295TXV6c0RCRmlKOVV4eXRTeXliWjkrQUgrd2VodEZzU0U3THBIZTlmMU12TVNBcVZ6V2hkUzhWcDQvSWFNKzV2R1Z0ck4ybVdKeVNacXNGUXM4czdkSnNOMmtlZXRDTjhMSkM1WGVhSExUcWg0WktTUU1CUktwRnM5c25FMzNGSCt3UGhPR0FCUEtOUWpDRlpXVWN0VC9ybDlBYnFVZ2ovZURFclN2cVN0RThTYnVMSVJuN1hzc3hKVXh6QVluUksrazkvSmozazAwZnhvbVlWeXMybHMxcVZFS0hMaGc4RU9laThRclhWb2VRMFZlUmRVckwzVEsreldscFVOSkZNSmF1RER5U0ZzcXdGYUljU3lXK2ZzbEV0S21id3V1MVZkV2FNd3NtK0JXT2pJOVNXeDBMeDVpU3FDM1o3L1dhVmloZVVUY2RTRzgwT1pBbjZscnZjbUlGZ2taZFJCQ0hwZFhtR3lZZVpzMVRuaTVyRStydUcrTFl3MkwxYkU0eUhyV2hlbXVIYWJUd3RZdExvZ1hTRDNkdlIiLCJtYWMiOiIyZDI0ZmUzZTQ1YjMxZjdmMGEyZjdhM2JmYjMxMThlNjQ2MjI5NDIzNGE1YmQzMDljOWM5NjhiOGM0NTA5NWUyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:57:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjhFWHlPaENsU0NXVk1MVUpHaFdqY3c9PSIsInZhbHVlIjoiQVhwRld6SXhDUFBFU29wczVIMHBlbEllZy9GanFkZ0pxNmFFUlVGVVNPeWRiMlMzTFhiVU9rV0NTUC93blNpTjJ0WDJjZ2F1Q25LRTNRWDBHZEhEV3lkVzRGOUJOS1RRamRnVGVkM2FOTG5OVXB0UzVxMnZGczVhVFM1M2tGYkZMbkMvWllERDRONVpRU0xPKzdDNUZ3cnBaaDArWUJrK0JlTTBMUC96VkdZOXFGeWUwN2pEdU1uako4Uk1VTFk1RlRYYVUzMWhPVWwzZGQxU1BFVjNhcWhMWjJ3RW02OWZ3dHJYeFEyTkxGZHp2dmVLVFZOME1FUktSN2E3eUltUmxyb3RPak5HZ2RaWXFManRBOXk1Y1JJMk0zSzUvUGdWVVVWVDB5QWlQMmtuOXNsVHVnS3BzUXpweks0RkExN0ZrbUl4L1phc1N3bzkvZjVQeWpCcy9lWEcyTGdXN2l3aGp3SWVyTm8zdzJsYkE2MHRjNXhaeVlDbzkzMS91cGVBVXlnbDY2VkdXYi9LUTA2NWtUZVpQdmFSTnRyZ1BNeHhkTlpxYTFRV1lybXRJdnNLYmpDQ3ErVWtOT0pVakNYMDlXQTIxRlZQVGtwZzVFSk5reWMyK3dOenl4YmUrQ2lZQ1p6NHF3VzJMT0lsZ3R2Yjh0OXZPUDZOckhka254aTIiLCJtYWMiOiI2ZTY5MTgwZjY3MGU0OWUwY2RiM2IwNzZlMWQ4MzZkNWE4MTk3MzNiNTU4MmU4MzMwODA0MTgyYTRmZDczMWIxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:57:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-45003629\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1894949596 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"30 characters\">Customer successfully updated.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1894949596\", {\"maxDepth\":0})</script>\n"}}