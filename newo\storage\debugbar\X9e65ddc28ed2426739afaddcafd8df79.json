{"__meta": {"id": "X9e65ddc28ed2426739afaddcafd8df79", "datetime": "2025-06-08 01:15:17", "utime": **********.710831, "method": "GET", "uri": "/customer/check/delivery?customer_id=7", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.080915, "end": **********.710851, "duration": 0.6299359798431396, "duration_str": "630ms", "measures": [{"label": "Booting", "start": **********.080915, "relative_start": 0, "end": **********.645813, "relative_end": **********.645813, "duration": 0.5648980140686035, "duration_str": "565ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.645826, "relative_start": 0.5649111270904541, "end": **********.710853, "relative_end": 2.1457672119140625e-06, "duration": 0.06502699851989746, "duration_str": "65.03ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43925424, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00409, "accumulated_duration_str": "4.09ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.691361, "duration": 0.00299, "duration_str": "2.99ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 73.105}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.699347, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 73.105, "width_percent": 26.895}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-148127950 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-148127950\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-800977357 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-800977357\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1076149512 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1076149512\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1751236546 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1939 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i31pha%7C1749345246637%7C11%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkpOMzJoK2pNcXAzVi9yUHVFbXVYYVE9PSIsInZhbHVlIjoieGxkc2ltb1d0WjI3dkN6THA2S1ljYjhzYWZiU0tmM1dMaCs3RTh5dUJGelUvN0NrbW1yWVBoMjBWTVYxS1RaM3VJdjlmSExYMDF6TGJIeGpSQXNVUlhwNmF6eE82ODZnU2p6WjZRa25vdzhXSTNyZkp5bFJtc3FFWlFxeUdkb1h1bmlTZzdRbTdwS2Qza3pJUXM1Szcxb2ZXUnhnZjkyOW9PYWh6S0tvM2VnNTRZY1lETjMrb1hYTllJaWhNREI2SHl3RncwdlZQMVlLYzE0MFEyZ1JtcnNvdUVKS0FWenkzZ0xTUWRadElXdjE5U3Nmbk1RNEhUVnRrL2NWWXg0Um9JYWl5Q3lSaksvZkRqbXZLRjNqV2NmS1NqeDVxckVrQUxOOWhsQmJ4QVk2ZGN1ZGVMU0tFV2ZlVm5UZGM4Vm5UcnlNaFB5YUVHc0UvVzdGQzROOXVFVXprcWQzM20yQ0JLN1UzVENySVBLcTRNV3NxeFB4QU13c1RqcUsxT09vL3JzV1pucWY1SGw1NVNNdVM0MjJEMWh5U1Z6Smw1dnJEdS9SYXFhVjh2MEpoWDhNbG5ZK2dHUldWa3VlL2lXOEFKNlpZQjlSUlZsRG1tenlHdnd3NlFVV2ozWjNpR1h6eE4rcVR2b1VCdVRWTnN5NFlZZ2paNnpRV3ZtZnVaSGEiLCJtYWMiOiJkOWUyMjZiYjZlMWY4MTFiYTM0NWQyODdmYmY4MDI5MGYxNDBkYjQ5NTQyNTg1ZjUwMzAxYWY3MjM5NjgyYWRlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Iko2QytCbDNMeStFbTVaWDNuSmRRbWc9PSIsInZhbHVlIjoiSUdKMVNqd1drcjBodFh6TGlxYms2ZUxBdTZMODlMeCs1VWoyU2NTQ3ExUjBzYk5URzY4Y3djOXVXZGpCNjM3dHplV1pYU2lsei9EZkVOMU9rc0h1QjdzRFZPb0FRUG1NUVEySkVXeGdXVFRvOTNSbnUzSjVGVS9RM20raWtxQUVEL3ZOYkZTY0Q4OFhCL2psdFpNMTVRQVVPeTkrdkc2TlA1M0I4U2dFUFJJYjFSYzc0NWw1ek0yWndxVVRFUU1RQWVvYkZRbEJjVmdqT0VLM2U4WCtMM2lZWVJJVE1wZUx6SzVVODBBWE4xKzFDY25RZDBSVHM4eUlHcGkzTzBRbmcxdjhZKzYwQTZmYzJPcUs1OTBadStkVDNCQW5qNitQYkpRLzViNCs0WjRpRHdoTEMwVjZRaHdna2ZNb2s1bTJGZkFUV2p1UUFWUDZTZU4wYnNtM3JQVzVlM0puM1VVMXhGMWxoZ3BUNE5xRHZERkNiRmFKSnJZZDM5NjJVWkh4SFJsVXpwTFk2Q1NUbUZPTldrckJBMFFxNWZKQ09UcHluVmFkVVdaVmUvVzV0ME5oL0kxcjJEQW9zcEM2eERxV0F0VDNqY3k1RDhNNmZMNTZoMFZhY1dJb2lQYVNkdmx0bFdtWEVWQlUzUVpQWDEzMlNFRTBqR0dTUlM0ZDc5R2siLCJtYWMiOiJiYTAxMDlhYzg0MDhjNGJlZmNiMTZjMTZhZWUzMmRmNjdmOWJhNGRkMjgzMTlmNjRkMjUwMTZkYzZkYzkwNmYzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1751236546\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1977549083 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1977549083\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-951034986 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 01:15:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFqU0RtZWUrd2Rpc0FyWFR6UnJ0eFE9PSIsInZhbHVlIjoieENwTW9UT1NCTzlNMCtlOUhmeXhtaTE2d3RFdlV4SkVZSUF4M09mVG1Zbkl5NEx3VWlBOWNmWW1MZ1ZxbWtmV3h0aXE5UklqYkcvaElJN2RqT2swQ3BQYjJHalJ4N2JRRFF1TnRoSmMrMjB6QTgwbnJWalo5TW83cXE2MVlPbGV5SXdwQnlCdTlMSDl3RHAwazhac0VPVFBla1hJS2hOV1l5ZW5Ccms1WGZlSDArZnNxV0o4MmZPN00wOWlUeGErY0RXS2lxOXppVmdqcC9ULzVsUFd1ZzBXUHJ3blFsWDlUQ2QvU3BFVHF2N2NYVGhrVTN1TlY4eEl5UkVXdUQ4Y2MrTW9KMmFWaHZPWEZ4WW16RHFhUW9LNE5Zak9YZVVFdWVGdG5VcW1BNXVkSEZMTE1JZmxiV21YMHUwNThzc1FKWEdrd1FBTUdsL2FKYU4rZ0JYcVRIRGtaSzF4amdHVDB2d3JrV3lLTWQ0STVsY2R4TWEzbTBGc2I4ajd2T3VSQkR3V3hITkVWak1LSm8rS3NNWmpKZm1SdlZJdkYxdEpoaFB4RHZEOFFuNTNGZk15UFF2QndGMlJKWnRJUzFGUm9oM3FhV3RsMFU1RFQzZjVjdDVOTDN1eUQyeHVXc3NqNk11VkZibUZYbytoblpVRkRva0NVWVkySmpoU2RHb3kiLCJtYWMiOiI5NjAxNTc1ZGY2YWZjY2RhOTE2OTU5MjU5ZTQ2ZDUyZGZjOTMwNTBiY2IzMTQ4YWVjNjQ1NjAxN2IyMDY5M2Y4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:15:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ik5nVm5qcUdVNFBmeUV2dzBubTB5ZkE9PSIsInZhbHVlIjoieGJFTXRpaWkwZkRhcVdzd0RSQVQ4TmpUZjJFZm5nSzhVSW5NYlhhaStqaEFOdkFxKy9CTUlYUUZUcEJ2d0I0R1l1R1ZZTWNIRXI4dFFUN28vcW4rWXpiYmtLNkcva2MyZFpPd05JeVlmQTA2R0lwTXkyNGtxelphQzZSaGdkazBWdFVKRUN5SitWcU1KS3NCeWsrZHpXRG9SclgxL2crUTQrTCswcTVmMlVSVDVwcHNOU004RmIycEhUTG1NTG81ZUFOeG1BYlJlTmVwditUeEd6V2ZEYzNLQi9sK0R1TkFtZDVQTDdkN3RrMzdvL01STmdwRlM4aFBlZVQvUXlPcnlKd3NwVlpLbTgzeEczSjZwRTYzTVZNc0lKV3g2VC9qUDdVdGRpdVlBalliMi9TdFc4QlY2NCtiTDdOQzFHRGtFT0RvSm9Tck1Ka2lyZ0s2NUFDVi9MNVE0YlJ6N2FPR0QrWFl6SFVQUGtUNnlDWktYazFTaTdwTVlIOExOd1RSekhDZUlZK3F2d0ZhL2gvbzgyOXpENWZWUU5ockVnZkRRbDljODU4QTNkTlE3T284SWhTeUZ4UDluRmd2aU5CK0IzbnM5SzBZT0lvZUlrRHdZbk14UUN0b0R6a3M5R2s1bHBJdWYweFY0dlpMVjNHT2IwTzA1Yno4TVBQRDdmamwiLCJtYWMiOiIzNzQ1YmQ1OGU3YjExNTk3MWVkNWMxMDcwNTEzOTRmYWM1YzM1ZTk3OWJkZTFhY2EzM2VjYmYwYzlmNGUzZDE2IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:15:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFqU0RtZWUrd2Rpc0FyWFR6UnJ0eFE9PSIsInZhbHVlIjoieENwTW9UT1NCTzlNMCtlOUhmeXhtaTE2d3RFdlV4SkVZSUF4M09mVG1Zbkl5NEx3VWlBOWNmWW1MZ1ZxbWtmV3h0aXE5UklqYkcvaElJN2RqT2swQ3BQYjJHalJ4N2JRRFF1TnRoSmMrMjB6QTgwbnJWalo5TW83cXE2MVlPbGV5SXdwQnlCdTlMSDl3RHAwazhac0VPVFBla1hJS2hOV1l5ZW5Ccms1WGZlSDArZnNxV0o4MmZPN00wOWlUeGErY0RXS2lxOXppVmdqcC9ULzVsUFd1ZzBXUHJ3blFsWDlUQ2QvU3BFVHF2N2NYVGhrVTN1TlY4eEl5UkVXdUQ4Y2MrTW9KMmFWaHZPWEZ4WW16RHFhUW9LNE5Zak9YZVVFdWVGdG5VcW1BNXVkSEZMTE1JZmxiV21YMHUwNThzc1FKWEdrd1FBTUdsL2FKYU4rZ0JYcVRIRGtaSzF4amdHVDB2d3JrV3lLTWQ0STVsY2R4TWEzbTBGc2I4ajd2T3VSQkR3V3hITkVWak1LSm8rS3NNWmpKZm1SdlZJdkYxdEpoaFB4RHZEOFFuNTNGZk15UFF2QndGMlJKWnRJUzFGUm9oM3FhV3RsMFU1RFQzZjVjdDVOTDN1eUQyeHVXc3NqNk11VkZibUZYbytoblpVRkRva0NVWVkySmpoU2RHb3kiLCJtYWMiOiI5NjAxNTc1ZGY2YWZjY2RhOTE2OTU5MjU5ZTQ2ZDUyZGZjOTMwNTBiY2IzMTQ4YWVjNjQ1NjAxN2IyMDY5M2Y4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:15:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ik5nVm5qcUdVNFBmeUV2dzBubTB5ZkE9PSIsInZhbHVlIjoieGJFTXRpaWkwZkRhcVdzd0RSQVQ4TmpUZjJFZm5nSzhVSW5NYlhhaStqaEFOdkFxKy9CTUlYUUZUcEJ2d0I0R1l1R1ZZTWNIRXI4dFFUN28vcW4rWXpiYmtLNkcva2MyZFpPd05JeVlmQTA2R0lwTXkyNGtxelphQzZSaGdkazBWdFVKRUN5SitWcU1KS3NCeWsrZHpXRG9SclgxL2crUTQrTCswcTVmMlVSVDVwcHNOU004RmIycEhUTG1NTG81ZUFOeG1BYlJlTmVwditUeEd6V2ZEYzNLQi9sK0R1TkFtZDVQTDdkN3RrMzdvL01STmdwRlM4aFBlZVQvUXlPcnlKd3NwVlpLbTgzeEczSjZwRTYzTVZNc0lKV3g2VC9qUDdVdGRpdVlBalliMi9TdFc4QlY2NCtiTDdOQzFHRGtFT0RvSm9Tck1Ka2lyZ0s2NUFDVi9MNVE0YlJ6N2FPR0QrWFl6SFVQUGtUNnlDWktYazFTaTdwTVlIOExOd1RSekhDZUlZK3F2d0ZhL2gvbzgyOXpENWZWUU5ockVnZkRRbDljODU4QTNkTlE3T284SWhTeUZ4UDluRmd2aU5CK0IzbnM5SzBZT0lvZUlrRHdZbk14UUN0b0R6a3M5R2s1bHBJdWYweFY0dlpMVjNHT2IwTzA1Yno4TVBQRDdmamwiLCJtYWMiOiIzNzQ1YmQ1OGU3YjExNTk3MWVkNWMxMDcwNTEzOTRmYWM1YzM1ZTk3OWJkZTFhY2EzM2VjYmYwYzlmNGUzZDE2IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:15:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-951034986\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-737099259 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-737099259\", {\"maxDepth\":0})</script>\n"}}