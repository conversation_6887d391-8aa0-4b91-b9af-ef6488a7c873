{"__meta": {"id": "Xf9b53e85d885120bdc659adaeda46ef9", "datetime": "2025-06-08 00:57:41", "utime": **********.951202, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.256891, "end": **********.951225, "duration": 0.6943340301513672, "duration_str": "694ms", "measures": [{"label": "Booting", "start": **********.256891, "relative_start": 0, "end": **********.839817, "relative_end": **********.839817, "duration": 0.5829260349273682, "duration_str": "583ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.839834, "relative_start": 0.5829429626464844, "end": **********.951228, "relative_end": 2.86102294921875e-06, "duration": 0.11139392852783203, "duration_str": "111ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45037704, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.025990000000000003, "accumulated_duration_str": "25.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8890562, "duration": 0.02414, "duration_str": "24.14ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.882}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.9284248, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 92.882, "width_percent": 4.309}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.93941, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 97.191, "width_percent": 2.809}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/customer\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-529808145 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-529808145\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1777282123 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1777282123\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1956480575 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1956480575\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-808606738 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749344251538%7C46%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InZ0MVF6dTFaZzR3b1lTdXV1OC9Xemc9PSIsInZhbHVlIjoiOTJxQXlGWURaS2E1eVVzTjZVNHlKY0JlY1JlbzU4K3RzRzRRbE1XU0hkbU5ES3NiaWhlV2xwc1hwdlhsZEZhd2M3ZHpmcm1qMFJmN1hNR2FyL3p2b2ZPZVRWMTg1OEM0QVJuMml3MlM3QzVXeWlaT2t6dTlBckRldVp4emZCOWpjVmt0Rit1QjdJUDRxaXJWN25udTFzTHZOcVM5RCtzUnMxMTlyNUU2MUZxTUd1UjR2TU9nMS8yeFRRTDBBOXZrdGFKaUlIeXZreVF1M0JIMjFCVGVnUG9LTDhtQ2tzS1Y2bWp4R1FjejJGaXcyNGVFR2RTZUFCRnR5aVZ2REt4dDlhY3pwVGtBVXdtWFVzVTBVODh0eFk3bHZ1bHlBTk5lNGFoSUFMdzdob1lJaDU4MlRMWTNrSmN2ZU5mc016dWZyWDNQT3Eyd2VFbHFvZTVHN2JrVmRsa0FoWWVHRFZFMUtSVW10N2pqS2RxRVoxdk1HaWFNTEpRYTRINjJVdjYvTkRLdjJHWCt0SE5RR1RKV0RJZUNPaThIeUo0dVp3Z0NYQ05MQVZmL3lXc0lGdW0rTXJldXNQRGNiNUdmSzJQbWlWRFZrR0xTcWEzWGlnaTQ3UkZkc09ZZ1drZzd5YkNzR1BsTG5PWndVTHVvSWRhSy9jVTdkenk1RnhQWUR2UFkiLCJtYWMiOiIyMTcxYjI4NjM2NjZjYTYyMTc2NjA4MTE4NGZjYzA2N2YxOWFhNDRhZGFmMDI1NjMwNzU1MWRiN2VmYWEyNDg0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkhpVGIxTWRmZ0xRMHF5UjAzMUU3S0E9PSIsInZhbHVlIjoiZVFwaFBUa3dIQ2pZU1JrYXlIQit5TjZNS3h5QmlSQ1FVcldHQ04zdytTZTIrQ2lqclpIZWRTZEQzZk04ak9ScC9oZll3b25XR1NzUVIwUW1yeGlzUDZvSTZCb3lDRWdpb0RIU0JHZnBpRERBM3VMZVcrTFVuT3Z2L2hVZ1dEczJjL0NLSzhFS1lYN3NPT0hNeHlCWjZWL1EyV3JFVzBSYXpJWXArQWgxZFlkL3BpWHNyOVZLMmp0SVhRNzdWRnZnSFZkV2VFS0hhOGxtVHovTFI3OW12RXpzY3FuL3JXeDBrWXdvT2ZJOWZMRjlFR3A4U3BMamFDYlZJSkdJWEJvSHkwSGR2b0QrNFFWbmR4T1dJd1pIQ1FMQXR5K3k3YUlxVldmNXMvL3ZMUngwMnZkSjhnZ1pZNXlQb1ZVdjUzSTFxRHExcXRrdG1nbDJYd3hWZmFvNUxFOGFwcWF4OUtObDA5SXlzUXdXOE5XOXZMbWdocDhqVkQ3WGhGeHkrNjNMQ0p1aE1oWUVZZ3Yrc3U0bnc4dTBnaVdmM1hYNG4xYmJ6a043cmNlU1BPekVXT1RZSGZUb0ZuL2l1aFMvOGI5bjhYT0o5ODVlRCswZ2VOd2l5QXgyZWR6TjdpK1BMdXhNNTl1Nlg1dm1HNldoOVVJeldOeHl3K3MvUStjL3o1a1YiLCJtYWMiOiJjNzYxODc4MjcwOTAwNmQ2M2FkNmJjM2U5NDdmODE4NjMyY2I2ZWQ1YTA4NDNiYTFjMzNjMmY2NmU3M2Q0ZTM1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-808606738\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1816137025 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1816137025\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1558425190 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:57:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImlUS0ZVb2Rpa2svK1oyZ3R1NElnK0E9PSIsInZhbHVlIjoibkp6NHBxVzZPKzY3MlhhU3BsMmtCOVRheTROY0hIYkxaRjk0T0lZNVdLN2lDdWN5K2ZNTE5KVE9EQW0wL0NITS9BRHVxSGhaNnp4dmFLcTZkNS92ZTBMcGwvVVliZWFqZExLUDAvbTJFZjhhYTdjNTNxcW1JMlZlVWoyRzRpd2xiQ2pubmE1cW9NeUg4N3luNjNzYmsyL3o1Q3FtZjJGd2NadlFLTER1QVkvMlZJRlEyRlNTbmtmWEs1Nm9oVytQWjVGMXZNR0l5Z3p1V3l1R1ZOZWE0L080a0FVcXpEVkFyQUplR3grdlkxUGpmS28zOGFtcFFoOGR0emNQMFJSVnNpYXl4bmlhWFVjaThEak9CSnVLOUtUVkdNNW1zV2VZNUhkcnhKdVJNZmtnWHN0UDdRdkJlWi9Jc1V3K1BhZWpkMGJ3R1J1bDhMSmtwL1A2OHF0eTc2U0p2UUJHVGFjSWNyUEhtR1VuY1ZPY0pmZFpDOEl2cWZacnJoRFllaDhsS3JSalFpWExhRkdLVEFTbFh2NlNQQTJvNkRUUTVYcnU5M0sxYTJhck0ySzZDS2E0MUxhQU8xQ0ZNa1pDRlo4Q2U4bG5ZY1JOdStBSld0RFZvd0RQMkYwR3ZkY2F6elNGajUvaVhLaHdYbU0vV01XbGZBNkhVVDI0Nzd4dExNYmoiLCJtYWMiOiJkMWI4YWIxZmU1ZWFhYjZkNTg0YWU3NDA5MGZhYWUzODY4MWJjZmZjODU1YzI3YzlhNGQzZjBmOWE0ZmQ1OGQ1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:57:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkNRb0ZJbSs4QW1sT1dUTFZWTlN4WHc9PSIsInZhbHVlIjoiOXlsYlNncTJMQTJ2TmFWSUMxRWJ0Nkt6Qkc3bmYwZGV1R24rbGcramhlTUpjVmpBbzNGeXNVZzlZVTlPRENOQXNMMzNaVklETDYwUllGMG00eEhMSUd2NjR0VGZjeDJIUnpyaHp6d0NNS1d3em1xWmdqaEpPWFFVb0ZDREdrMmszSlkrenVvVWhOMVhTVVRVSGprQlpCa1ZuTFBPUUZLTDR0WXZQTFB0ci8wOUJwZ0lTNStodjFYdXVTR0FTc0FIMjNJZVJQTE4rN2htL0lNMld5Y1hLVjhZdCtKU2JzYVdDNEtrc1orSE5zYUdwYWxYdXhKZ0FscHo0T0xoTFkzVTJyVEVWYk4xYXdDYnkzS1NDMFNRUnd2WWp4eWRUbDJUbisxeXRaYXpuM2ZtQW1nY3FhcFVCSGt1dmxVTnF1WFhDMzR1VytXcDM0VVpqSW5DTkF6NjREYTl0TGZ3K0F1TTBzSDMzK1E5eU5pK2VxZW1MZzZiNDVwdXMvcGx2YzcwV1BMOXNlbjc1T01QaFdFc2ozTGJna3c4S0tMb21sanFCcHhBMUJjbFlxOENMeFFhZEZBOEl6UnVTTWtTZk9OdVN1MEtMY3pQM1RHeTNNSlBWNDV1Vk44Sm9VOFZ0WFBCRVd3NlZvSHJha25sdGJKd1NDcEJFRjZJS0RsSXdCK1EiLCJtYWMiOiIwYzMwOGMxZWZlNDNjMGRmYmViNDZlY2NjNTllMjg2Y2Q3Y2M3NGQxOTNlMGVmMGY0ZGFmYjYwMmY4ZWIwYmZiIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:57:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImlUS0ZVb2Rpa2svK1oyZ3R1NElnK0E9PSIsInZhbHVlIjoibkp6NHBxVzZPKzY3MlhhU3BsMmtCOVRheTROY0hIYkxaRjk0T0lZNVdLN2lDdWN5K2ZNTE5KVE9EQW0wL0NITS9BRHVxSGhaNnp4dmFLcTZkNS92ZTBMcGwvVVliZWFqZExLUDAvbTJFZjhhYTdjNTNxcW1JMlZlVWoyRzRpd2xiQ2pubmE1cW9NeUg4N3luNjNzYmsyL3o1Q3FtZjJGd2NadlFLTER1QVkvMlZJRlEyRlNTbmtmWEs1Nm9oVytQWjVGMXZNR0l5Z3p1V3l1R1ZOZWE0L080a0FVcXpEVkFyQUplR3grdlkxUGpmS28zOGFtcFFoOGR0emNQMFJSVnNpYXl4bmlhWFVjaThEak9CSnVLOUtUVkdNNW1zV2VZNUhkcnhKdVJNZmtnWHN0UDdRdkJlWi9Jc1V3K1BhZWpkMGJ3R1J1bDhMSmtwL1A2OHF0eTc2U0p2UUJHVGFjSWNyUEhtR1VuY1ZPY0pmZFpDOEl2cWZacnJoRFllaDhsS3JSalFpWExhRkdLVEFTbFh2NlNQQTJvNkRUUTVYcnU5M0sxYTJhck0ySzZDS2E0MUxhQU8xQ0ZNa1pDRlo4Q2U4bG5ZY1JOdStBSld0RFZvd0RQMkYwR3ZkY2F6elNGajUvaVhLaHdYbU0vV01XbGZBNkhVVDI0Nzd4dExNYmoiLCJtYWMiOiJkMWI4YWIxZmU1ZWFhYjZkNTg0YWU3NDA5MGZhYWUzODY4MWJjZmZjODU1YzI3YzlhNGQzZjBmOWE0ZmQ1OGQ1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:57:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkNRb0ZJbSs4QW1sT1dUTFZWTlN4WHc9PSIsInZhbHVlIjoiOXlsYlNncTJMQTJ2TmFWSUMxRWJ0Nkt6Qkc3bmYwZGV1R24rbGcramhlTUpjVmpBbzNGeXNVZzlZVTlPRENOQXNMMzNaVklETDYwUllGMG00eEhMSUd2NjR0VGZjeDJIUnpyaHp6d0NNS1d3em1xWmdqaEpPWFFVb0ZDREdrMmszSlkrenVvVWhOMVhTVVRVSGprQlpCa1ZuTFBPUUZLTDR0WXZQTFB0ci8wOUJwZ0lTNStodjFYdXVTR0FTc0FIMjNJZVJQTE4rN2htL0lNMld5Y1hLVjhZdCtKU2JzYVdDNEtrc1orSE5zYUdwYWxYdXhKZ0FscHo0T0xoTFkzVTJyVEVWYk4xYXdDYnkzS1NDMFNRUnd2WWp4eWRUbDJUbisxeXRaYXpuM2ZtQW1nY3FhcFVCSGt1dmxVTnF1WFhDMzR1VytXcDM0VVpqSW5DTkF6NjREYTl0TGZ3K0F1TTBzSDMzK1E5eU5pK2VxZW1MZzZiNDVwdXMvcGx2YzcwV1BMOXNlbjc1T01QaFdFc2ozTGJna3c4S0tMb21sanFCcHhBMUJjbFlxOENMeFFhZEZBOEl6UnVTTWtTZk9OdVN1MEtMY3pQM1RHeTNNSlBWNDV1Vk44Sm9VOFZ0WFBCRVd3NlZvSHJha25sdGJKd1NDcEJFRjZJS0RsSXdCK1EiLCJtYWMiOiIwYzMwOGMxZWZlNDNjMGRmYmViNDZlY2NjNTllMjg2Y2Q3Y2M3NGQxOTNlMGVmMGY0ZGFmYjYwMmY4ZWIwYmZiIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:57:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1558425190\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1716315240 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"25 characters\">http://localhost/customer</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1716315240\", {\"maxDepth\":0})</script>\n"}}