{"__meta": {"id": "X0584131c97f22e5818d5a3f6171726ff", "datetime": "2025-06-08 01:05:55", "utime": **********.85844, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.203614, "end": **********.858467, "duration": 0.654853105545044, "duration_str": "655ms", "measures": [{"label": "Booting", "start": **********.203614, "relative_start": 0, "end": **********.757262, "relative_end": **********.757262, "duration": 0.5536479949951172, "duration_str": "554ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.757277, "relative_start": 0.5536630153656006, "end": **********.858471, "relative_end": 3.814697265625e-06, "duration": 0.10119390487670898, "duration_str": "101ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45052928, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.016130000000000002, "accumulated_duration_str": "16.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.810004, "duration": 0.01439, "duration_str": "14.39ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.213}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.835417, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.213, "width_percent": 4.96}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 23}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.844285, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.172, "width_percent": 5.828}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/financial-operations/product-analytics\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1727718156 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1727718156\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-575296862 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-575296862\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1153127635 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1153127635\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1126334290 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749344713396%7C49%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjV1WmN4ZzdzOU9RU3A5WEp6SVBuTWc9PSIsInZhbHVlIjoiai9mVlpMVnVnMVFlNTVmcGJKZ1RDS043NVlvQWpHb1ZUVlRPbmF5WjdCUGQzMzVSWjRaOXpmOUc3NnFRM1NIcEkyUDBVZUNNTndZbGlZVHJ3ZVk5Ylg1UTdxMzVaQnJLRzc1MDNCaEVub2FBUFQrcEMwWmducllWS3QwSGhRWTdFN3hFZi9GZE1hSTVuOG1hV3F0VWlLRTY4YjNBdXFLdmFRb2NheHRvM1plZWxVemdBU0pkNDAvZlBJMVpQb01semtzVHdJZGZCdkh5dDZMOGd2MnY5QlRORk4yb3hMY1dLYmJINnJ0cjVQNEZud0J5bSsyNnp2SHcybW1sZklCNk96a0tyeDh1MHBCN3hORFBDSFo1VTRvRXBwMy9TYytXY29mZ2RlNzJWTnU1ODExbzQ1QnJiVjRBRFhYLzhsMnRudm9jOUFza2l0Tm51eGMvMllQZ2NXTzhMSkpEa05ha1YrVjZGZzVYUXphaitlSm1vY2YyRDhTQUhUbU04VUdLb3ZRU0FJRG5SN21lKytjMHRDVHZKSDVsMTMwRVBPTGI2ZXBhQ1VxdHNVMCtvc0gyZXVoTm8yaHRPTkc5ZVFJaHdQUXdiRnluWGpCS1JPa0dLdCt3TWs0TDlzNnhmRlhUZktKK3NjbnhpUDN2SzJ4VEpCRUdzYy9zTFRIRWdQak8iLCJtYWMiOiJiYjZmNGEyM2EzYzE5M2QzZTkxZDM1NmE3NmYyZDFmNjVmMjNmM2Y0MDVjYjU3N2M4MTc3OTVkYjU3NTNmMDhmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjdDc0ltUFhMS0tpelE2dTQrMmNhZkE9PSIsInZhbHVlIjoic2F2bzQwR0QySStEem1yRlY5eDBDRXA4eDR1dTRGZ29YZFFXbkd1SzdJeG9IdmlNYVZBSTdnbExLSlR2d003eTZiYWJsanJvRDFGOHV5cWtaWnFIdmsxVzRsLzZRZmRaZm1wVUdpOFYwcVFEakNmZ2JnYWFkdWgxbEtRWEhiUmt1UElEaDIzTjFxcUxlemxPMUlJYmRaWXFrNm5NLzVOZjhHNVhnQ1dra1pWaTdPTm16WTBFNFZuUGRaUXlwTW0wU3Eza05QeDhiUVpvZ0xLYVdPU0hYRTYxQ3R1YVpxa1dJN2dtbHpvbEY3VGQ3Vmk1SkcxYXJHc3dQRzhrRmtZZDRCNnZFb1lBWmhQZ29JNkpzTjI2Q1Z4dUFYYTZsVGxGcVgya0lBTEluL2hMd1AvRVAvVjJEejJ4a2N3Z0hKSUMreERKMCsvMk5sZktCbStTUHhuMEszd2creEdHa0JuT1ZHN2NSUHlhWEFGWXNjRXlSSE1ldzVNdEdWSTlqZ0VBanBTK3dweCtic0VWK1ZKeTdlaitFcUdLVWE3M3pITmVmbmdLK2JBV1dFdnQ1V1NyQ2pvMXh4UEJnYXdEbzh4cDdlNEt0cm5JMEZpVGJXWlVsa2JubkZiSEtCV2dMWElYQ2tsTm9MQmJPaE5MNWw4aXpza3lLTG91bnJZODhldGsiLCJtYWMiOiI1ODUwZTQxYTQ3MzUxMGY5MWExNzc0ZmEyNTBlYTgzZjJmNmQ0OWNlMDQxMjhkYTEyYmE2Y2IyODgyZTE1NGRmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1126334290\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1297743377 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1297743377\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-471589069 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 01:05:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtRV0NteUJ4RGJMTUZTSDBtVHpBMEE9PSIsInZhbHVlIjoieWgwcW0rUVpFZzVJSG9ZMmhnWnZjbkpDNkNmV3RSMENwUjgybFlRcnFLVDR3TnNvR2gzZFBMdVJvZ0x6aUVzRXppVm9SVjg5ckVHaGtSYmxTdGlQdHR2ZU9IQ2pUMTF3UmJsMlVURGFsbVRlMHl6b1Byc3lxNFlCSk9rb3pVZTM0Y2s2OUhveGdxRHRvU1JsSmZQMEN5b1IxUWkvaTZza2E3TEw0QXM2dzVqanA5c2lQazhGTllhald0MFBIdkQvNHVlUlRzekcvZ2dLZWQ2WWltZjR2NG14T1Z6OEhmWmRUWHNmN0lZMTlubUFQaEJiQXloL3lNZ3FCZWxkZnVWVEk0TVVGWTJnREJsSlVhb2p2c2tFN29VU2xCK05RQnZ6Sk5TYWtWQnpKQVJweHVIK2tpT1F6R1JQRXgwMW1ibGExL2xSdXRmM0xpOW0rR3NIRk9ldERXa2ZaMC9OZjV3UEdzc0xMeFVoOXVMblNmVlcvUG9MZG9PS05ld3BadEEwdGlVc3NiUW11NTEvOU1ubENLb05IM3l4RGZQZEhuTXBKS2tOdjVyLzFzTFMyeWxneUFCeUtaWXZCMmlHSFlaYjRwUEY2V21JcEJWNkxFSTdTekFOZXJWRXkxbXliYlFIVXkrblR4V3ByaTdiM3NBQjd5WnhBTEtDRWZrdzFQamkiLCJtYWMiOiI2NzMxZWNkMzU1OTAwMDg2N2ZmMzIxOTc5MTBmODNmYTkwZTExMGNiMWJiOGY0NWFjYWY3YWI2YzNkZTc4OTIxIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:05:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InV6b2U2WlpINVRtdVdKalFsZ3djRUE9PSIsInZhbHVlIjoiNHNOdWtpNGZ0cjc3cTliekk2VjY1eStKbG43S04rc2FDRU5mbXI2eWRYWTFPQTRSbVZ0aWtVVXpmZHh1WXNjNHd2SnlXZFdPMGNoMEZLMGpCWmVIc2I3aFB5YnRGbVZla3pucnE2YW5pSHZlL1kvQ2hVbzFzNkZ2NDFHSzRBSHM2bjA1V0gvcjkzaGUybTVpeDI0RCtWcnRodFo0dysvZEs4V0lmY1IrMER6b0ZWYnFkTkY2ZXlaaURKWitsK3htdjV1U25DUndKd09UaWtLenljNGQ3eDJscXZqVDQwWlA5ZnRJMGc2NEd3ZXVIRkpadC90V29OdnlVd1VvS1lza0VPamJKbTlPMjZyL01SdFRIaEsrbWR1RXlPditmWXROQk9WSXg4cjU3V0ZLK0JHQmpHSHR0Y2J0NjhaTVNXYjAxaWU3NHdSb2Y1ejlRbWw2WExhQUxFYnJCZkVhVmdsUytSQlUrWC9YTXk1YUJXcm1mVFFmS3ZQN042NGNQcnhSdDhtTlF4OGJoYUxINml4R0NURnhINm1GeTV1ZWx3bHgrcmR5QS9JMVA1bjlBRWExRFlxYkttSVFuS0t0ZldXY3BnS3NzemZDeklsQWVvZGF3amJEQnFkNHMzYmdra3huRHc4US9PRkM1SEVXUWx2VkszSDFtY1RmMlVoTzhVbVAiLCJtYWMiOiI1NGQ2NjQ3YzQ1ZTQwMzc2YzI4ZGZkZDg4YmMwMWUyYWEzNDJkNmZjZmZiNzFkYjg1YmY1NzQyOThkMDM3NzM0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:05:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtRV0NteUJ4RGJMTUZTSDBtVHpBMEE9PSIsInZhbHVlIjoieWgwcW0rUVpFZzVJSG9ZMmhnWnZjbkpDNkNmV3RSMENwUjgybFlRcnFLVDR3TnNvR2gzZFBMdVJvZ0x6aUVzRXppVm9SVjg5ckVHaGtSYmxTdGlQdHR2ZU9IQ2pUMTF3UmJsMlVURGFsbVRlMHl6b1Byc3lxNFlCSk9rb3pVZTM0Y2s2OUhveGdxRHRvU1JsSmZQMEN5b1IxUWkvaTZza2E3TEw0QXM2dzVqanA5c2lQazhGTllhald0MFBIdkQvNHVlUlRzekcvZ2dLZWQ2WWltZjR2NG14T1Z6OEhmWmRUWHNmN0lZMTlubUFQaEJiQXloL3lNZ3FCZWxkZnVWVEk0TVVGWTJnREJsSlVhb2p2c2tFN29VU2xCK05RQnZ6Sk5TYWtWQnpKQVJweHVIK2tpT1F6R1JQRXgwMW1ibGExL2xSdXRmM0xpOW0rR3NIRk9ldERXa2ZaMC9OZjV3UEdzc0xMeFVoOXVMblNmVlcvUG9MZG9PS05ld3BadEEwdGlVc3NiUW11NTEvOU1ubENLb05IM3l4RGZQZEhuTXBKS2tOdjVyLzFzTFMyeWxneUFCeUtaWXZCMmlHSFlaYjRwUEY2V21JcEJWNkxFSTdTekFOZXJWRXkxbXliYlFIVXkrblR4V3ByaTdiM3NBQjd5WnhBTEtDRWZrdzFQamkiLCJtYWMiOiI2NzMxZWNkMzU1OTAwMDg2N2ZmMzIxOTc5MTBmODNmYTkwZTExMGNiMWJiOGY0NWFjYWY3YWI2YzNkZTc4OTIxIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:05:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InV6b2U2WlpINVRtdVdKalFsZ3djRUE9PSIsInZhbHVlIjoiNHNOdWtpNGZ0cjc3cTliekk2VjY1eStKbG43S04rc2FDRU5mbXI2eWRYWTFPQTRSbVZ0aWtVVXpmZHh1WXNjNHd2SnlXZFdPMGNoMEZLMGpCWmVIc2I3aFB5YnRGbVZla3pucnE2YW5pSHZlL1kvQ2hVbzFzNkZ2NDFHSzRBSHM2bjA1V0gvcjkzaGUybTVpeDI0RCtWcnRodFo0dysvZEs4V0lmY1IrMER6b0ZWYnFkTkY2ZXlaaURKWitsK3htdjV1U25DUndKd09UaWtLenljNGQ3eDJscXZqVDQwWlA5ZnRJMGc2NEd3ZXVIRkpadC90V29OdnlVd1VvS1lza0VPamJKbTlPMjZyL01SdFRIaEsrbWR1RXlPditmWXROQk9WSXg4cjU3V0ZLK0JHQmpHSHR0Y2J0NjhaTVNXYjAxaWU3NHdSb2Y1ejlRbWw2WExhQUxFYnJCZkVhVmdsUytSQlUrWC9YTXk1YUJXcm1mVFFmS3ZQN042NGNQcnhSdDhtTlF4OGJoYUxINml4R0NURnhINm1GeTV1ZWx3bHgrcmR5QS9JMVA1bjlBRWExRFlxYkttSVFuS0t0ZldXY3BnS3NzemZDeklsQWVvZGF3amJEQnFkNHMzYmdra3huRHc4US9PRkM1SEVXUWx2VkszSDFtY1RmMlVoTzhVbVAiLCJtYWMiOiI1NGQ2NjQ3YzQ1ZTQwMzc2YzI4ZGZkZDg4YmMwMWUyYWEzNDJkNmZjZmZiNzFkYjg1YmY1NzQyOThkMDM3NzM0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:05:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-471589069\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-171350498 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost/financial-operations/product-analytics</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-171350498\", {\"maxDepth\":0})</script>\n"}}