{"__meta": {"id": "X82b6d5fd77d09dfd5578effd2c31c80d", "datetime": "2025-06-08 01:14:10", "utime": **********.754612, "method": "GET", "uri": "/pos?warehouse_id=8&ajax=1", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749345249.817931, "end": **********.754635, "duration": 0.936704158782959, "duration_str": "937ms", "measures": [{"label": "Booting", "start": 1749345249.817931, "relative_start": 0, "end": **********.54942, "relative_end": **********.54942, "duration": 0.7314891815185547, "duration_str": "731ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.549442, "relative_start": 0.731511116027832, "end": **********.754638, "relative_end": 2.86102294921875e-06, "duration": 0.20519590377807617, "duration_str": "205ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52907840, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET pos", "middleware": "web, verified, auth, XSS, revalidate", "as": "pos.index", "controller": "App\\Http\\Controllers\\PosController@index", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=37\" onclick=\"\">app/Http/Controllers/PosController.php:37-109</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.013800000000000002, "accumulated_duration_str": "13.8ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.622611, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 26.449}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.644224, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 26.449, "width_percent": 4.928}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.679045, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 31.377, "width_percent": 8.188}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.684159, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 39.565, "width_percent": 5.797}, {"sql": "select *, CONCAT(name) AS name from `warehouses` where `created_by` = 15 and `id` = 8", "type": "query", "params": [], "bindings": ["15", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 50}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.695702, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "PosController.php:50", "source": "app/Http/Controllers/PosController.php:50", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=50", "ajax": false, "filename": "PosController.php", "line": "50"}, "connection": "ty", "start_percent": 45.362, "width_percent": 8.406}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'ty' and table_name = 'customers' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7033808, "duration": 0.00269, "duration_str": "2.69ms", "memory": 0, "memory_str": null, "filename": "PosController.php:57", "source": "app/Http/Controllers/PosController.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=57", "ajax": false, "filename": "PosController.php", "line": "57"}, "connection": "ty", "start_percent": 53.768, "width_percent": 19.493}, {"sql": "select * from `customers` where `created_by` = 15 and (`warehouse_id` = '8' or `warehouse_id` is null or `warehouse_id` = '')", "type": "query", "params": [], "bindings": ["15", "8", ""], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 69}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7093852, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "PosController.php:69", "source": "app/Http/Controllers/PosController.php:69", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=69", "ajax": false, "filename": "PosController.php", "line": "69"}, "connection": "ty", "start_percent": 73.261, "width_percent": 8.478}, {"sql": "select * from `users` where `warehouse_id` = 8 and `type` = 'delivery'", "type": "query", "params": [], "bindings": ["8", "delivery"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 72}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.714529, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "PosController.php:72", "source": "app/Http/Controllers/PosController.php:72", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=72", "ajax": false, "filename": "PosController.php", "line": "72"}, "connection": "ty", "start_percent": 81.739, "width_percent": 11.812}, {"sql": "select * from `pos` where `created_by` = 15 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 517}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\PosController.php", "line": 74}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.723913, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "PosController.php:517", "source": "app/Http/Controllers/PosController.php:517", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FPosController.php&line=517", "ajax": false, "filename": "PosController.php", "line": "517"}, "connection": "ty", "start_percent": 93.551, "width_percent": 6.449}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}, "App\\Models\\Pos": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}}, "count": 7, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1734937192 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1734937192\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.693273, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1138298997 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1138298997\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.722327, "xdebug_link": null}]}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/pos", "status_code": "<pre class=sf-dump id=sf-dump-998280828 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-998280828\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1349383236 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>ajax</span>\" => \"<span class=sf-dump-str>1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1349383236\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1285879933 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1285879933\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1812573301 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1939 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i31pha%7C1749345246637%7C11%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InFzdklqVTBab3RlRkNTMUxEWjBKcnc9PSIsInZhbHVlIjoicFZrdnlpUG16NVFEeEZGbEMyemRaRFUxNkdnbG0rbGMvTG1tS3kvMlc0STVtYllNYjJEdnR3QVZ1S1RrOUNmZHFrdVhtYVZYTlovVEI4b0RaMHVjZjh4clM5UEFhSXdKdW5YSEE4N2Flcm1VSm1BVnVKZi9uN1B1YmpDODVySGpHeFQzVDFiam8rVXRjZ0poNXhFZGg2YVBxazJKL3ZHWE5VSFBETzBCQlc0a2J5dnd1VmZsSXZqblp6QjdDZDFINFpYcHZjOW5Sdm5qTnBwNzBLMUVDRlNvaWZRNG8zR2QvQWxYekNMVjZYY0pCUVRjM2M0VHk4UjhSZ0tzcGNpUWdHeTMyZFRlVHhHUS9wQjRRRnRURHdFeDgyVnlWa2Ewc01GcVk5VEY3VEdvZml1aGdxYWdvKzhjam1LWlZiTGE2MFNteUIrWjJGRG9pa2dkZ0VEdkNVUlRBQjRhTytPYVM1VUU3VVl2Qm0xTDR0NEt4Lzh4c0hKTmw1bmxjV2d2S29zTUJYTkNyY2NKWXYyK2FBMVZKWUU2QUg1bmh0MU85bmF3M3BKWjcwbzdZdTBhTk5KR0FZMUdoSEpSUnpSRWlaYmpnNVpWWlo4TEttNGJhc3h1cDRPakFWempES05BSW9KeW9DV1k3QU1Ma2hmdldVTEcvUFVhbHJnenVZVVkiLCJtYWMiOiJhOTJjZDMxMjIzOTRiMGI2NGYxMDgyYTMxNGY3OTZhMGJhNzI4YTU3MGFjYjY0NTFmMTJkMmIyNzZmZDUwZWY4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImlFQnBhazhvQkR4RUlIcGxJOEU0VEE9PSIsInZhbHVlIjoieVFWdDBLVWJFd0FFSDNXV1JiR2N0NERyWWNZNmpld2VUaGZEQXMyY2VidWlNNVc1VERPMUc5ZUpLaVk5STJUZVY5dVlVVGZyTTBtR0E2THdKUnFwNjdENkM0S3VYTmdjYXY0OEVDUDk0aU1hVkUwYTlPRkp6L1F4VEpuWW05YzNUV3dIUVZsL29UalFSOHlSTnZrbGtXeHY5ZXlpbU5KOGpmWHYreGkxbkFEZWtIK3RwNHpIRktBZEc4QW9JTTdvV2dUcjc5STNhVFZyYTNxVTF4MmdQVGdCcUJ0UWtOTWcxS0dIM1lWNi9acDErUjlPc0gzMlowVmJJN2c3N1ZNNlNZLzBNL2J6dGdPbWhoNS9XVkdEYnR6anZDVHV6QUJQUkkxWG56aFltaWdQUXZxWC9YTUszbmNkS0JPVlpWeFhLZ2RJZEtZSjdWMy9CYmkvVWFaWGNKWWdnN3UrcFBxOFUybUtNQlZ2cFpRaG56SjN6aVVZMEdNQUsxd3Nnb0s0UjRtanJxeTM0bTJaS1UyWHAzaXBzaXBueHowM1dUSlJTUFUySnZ3alhHdHp3Um04WW53UnNZaWxJaVRlZndrSHRYMHR1NzAwcTZiZlBkcyt3QUhRcXVZZVh5M1RnMlk4TFljUGt5VjZhcHcrbVQrSStaQ1Y1bm9qeTVGVUl1ZngiLCJtYWMiOiI3NDdmYTc1NjdiYTE0MTJjMDNjNjZiYTkwNDk3Mzk1MTRlZmVjZjliNTMzODgxY2VkZDIxZGIxYTFhNzVkZTRkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1812573301\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2067268914 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2067268914\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-87221128 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 01:14:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Inl5MzcxbXJLaFJneGVETnB5eFFTSUE9PSIsInZhbHVlIjoiZU9HczNpbE5lSUp0dU1HSmR3V1ozeGpuTzF6eitJOWo5SHl4dnRPcElSU0Y1OHhjTVlhTVJ1MlhsdGw1R3BuUnMvVTlRaGFvcjNHREplb1g5K3NhRjlVaW9udDUrby82OEI2RFh6dkhqMkNYOUtvcnlCdzlIWEoyTHdiYjE4M0Jvc3p4SUdqd2ZQN25FM3JyOVBiWHZ0eFBvbEtMQW02MjNuV3NkbkZVc3RBVTQxQU5vanRqeUk3WkJ1dWhsb2tORVRXZEJBREs5WWxuc3R4Q0Z3VDVlbGlFaWxhUE1INFpPODFSQUxHWkU1QWExdFh3Nm1ncEFJdlZMMHgyOUs0elkrcDhVTnZDVnJ0WFRFUmZQL09wUWRpN2NGVE1SMkl5Rzc0Q0xDcnpLczZSU0cxR1poQWtLZy9jOWo2UWdWckhXOWppRXZ3UUxrVFRER0wzRGlySFpBUjExQkRhV1Y4cW95WVNmZWhRRjh0aGpCTCtZK0Fha3libWxqOXRKS1BaY2JubkVCVTg2ZnNIZkt0bTM4YzgyTzZ3azJMbjB0Qm5RRmR3aTlBZlJqblhkUXlFdVFqK2o0NloxdllLSmtMdytyZS93ZzRvVVRFVkc5M2g0UW9XaGt2TzRpTHpZNVpOZUFTQ2xGSXdQRWkzSHc2cWZDNHd1OVZ0Rk1HK3VmY1giLCJtYWMiOiI5ZDc3ZWE4ZDcxYTZiMDc5NGI1OGU0MTg3NzFjNzU2YmMxNTYzYTg4ZWE1NjA2YTJjMjc4ZGVjY2E3OTIzYzM3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:14:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InNnbzdnenJlZ2dOaDdiTUVuWXhRdXc9PSIsInZhbHVlIjoiR3dpUG9CSlkwMWR3UFpMRFREVTdUMmRnOWVMbWs2T1RYU0RSMTBRYjZ5eHc5K3lDbm92Q3c2c0ZOZlFrbURNZXY4VVVYUDJGd3ppR2h3azlBT2NQK1BjRnppL0FqcWhhMkZOMFVFb3NvNEJJRkJKNjZsY1E3L0syelhwajVlZ3VKUFF5OTNrYmt3N0xOUkZqaG1NcjlzTjV0NGhZUDJiK3Q1bW12TkRtSlBRbnZJenhPcXdsVnczUnRleFVSeE92ejVSTnJUUzdIMVZvNFkvS3JuZ3Y3dWVIOTBhVEhJeFBWWkx6OHpaRmU5aFd5bldvazVyUEdURVRLOVJBeHZnMTJvelFSaDAzSU84T1F1amN4UlZVUWwwSW1pTU9HU0t3N1dtY3JDaE50aE1wNVoxM3dtUW1VWE9GRE1hK1R2NHBsUHlYd01IQUc4UzMxdE1iMUo0QjBsSGpSWWpacEUwWHdlaHZNYWJmdGdUWVJhLytRcWk4TU5XUlc0OGNCYkM3ZStnaXBCTHNFNDdwNExFelA0ZkZZYkZ6TGlBQ2hkMndTMWtoajVZSE9PNGhnQzlML3YwRVVJbXJVQ2F4Qk9BaFhSQURIbFdiZmV1eithTVNQaWo1bFRlZGpWdXBxZm53Z05lelJsZGI3cTQvVGxmTzJZcFJGME1wNndyQXZjQzQiLCJtYWMiOiIyNjQxYjAzYjk5ZjZiZjU5ZjdlMTFiYmZjMjBhNWIyYWE4NGQyNzVlYzg1NTBhN2E3ZDdjZWY2NTAwZWMwNjY4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:14:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Inl5MzcxbXJLaFJneGVETnB5eFFTSUE9PSIsInZhbHVlIjoiZU9HczNpbE5lSUp0dU1HSmR3V1ozeGpuTzF6eitJOWo5SHl4dnRPcElSU0Y1OHhjTVlhTVJ1MlhsdGw1R3BuUnMvVTlRaGFvcjNHREplb1g5K3NhRjlVaW9udDUrby82OEI2RFh6dkhqMkNYOUtvcnlCdzlIWEoyTHdiYjE4M0Jvc3p4SUdqd2ZQN25FM3JyOVBiWHZ0eFBvbEtMQW02MjNuV3NkbkZVc3RBVTQxQU5vanRqeUk3WkJ1dWhsb2tORVRXZEJBREs5WWxuc3R4Q0Z3VDVlbGlFaWxhUE1INFpPODFSQUxHWkU1QWExdFh3Nm1ncEFJdlZMMHgyOUs0elkrcDhVTnZDVnJ0WFRFUmZQL09wUWRpN2NGVE1SMkl5Rzc0Q0xDcnpLczZSU0cxR1poQWtLZy9jOWo2UWdWckhXOWppRXZ3UUxrVFRER0wzRGlySFpBUjExQkRhV1Y4cW95WVNmZWhRRjh0aGpCTCtZK0Fha3libWxqOXRKS1BaY2JubkVCVTg2ZnNIZkt0bTM4YzgyTzZ3azJMbjB0Qm5RRmR3aTlBZlJqblhkUXlFdVFqK2o0NloxdllLSmtMdytyZS93ZzRvVVRFVkc5M2g0UW9XaGt2TzRpTHpZNVpOZUFTQ2xGSXdQRWkzSHc2cWZDNHd1OVZ0Rk1HK3VmY1giLCJtYWMiOiI5ZDc3ZWE4ZDcxYTZiMDc5NGI1OGU0MTg3NzFjNzU2YmMxNTYzYTg4ZWE1NjA2YTJjMjc4ZGVjY2E3OTIzYzM3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:14:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InNnbzdnenJlZ2dOaDdiTUVuWXhRdXc9PSIsInZhbHVlIjoiR3dpUG9CSlkwMWR3UFpMRFREVTdUMmRnOWVMbWs2T1RYU0RSMTBRYjZ5eHc5K3lDbm92Q3c2c0ZOZlFrbURNZXY4VVVYUDJGd3ppR2h3azlBT2NQK1BjRnppL0FqcWhhMkZOMFVFb3NvNEJJRkJKNjZsY1E3L0syelhwajVlZ3VKUFF5OTNrYmt3N0xOUkZqaG1NcjlzTjV0NGhZUDJiK3Q1bW12TkRtSlBRbnZJenhPcXdsVnczUnRleFVSeE92ejVSTnJUUzdIMVZvNFkvS3JuZ3Y3dWVIOTBhVEhJeFBWWkx6OHpaRmU5aFd5bldvazVyUEdURVRLOVJBeHZnMTJvelFSaDAzSU84T1F1amN4UlZVUWwwSW1pTU9HU0t3N1dtY3JDaE50aE1wNVoxM3dtUW1VWE9GRE1hK1R2NHBsUHlYd01IQUc4UzMxdE1iMUo0QjBsSGpSWWpacEUwWHdlaHZNYWJmdGdUWVJhLytRcWk4TU5XUlc0OGNCYkM3ZStnaXBCTHNFNDdwNExFelA0ZkZZYkZ6TGlBQ2hkMndTMWtoajVZSE9PNGhnQzlML3YwRVVJbXJVQ2F4Qk9BaFhSQURIbFdiZmV1eithTVNQaWo1bFRlZGpWdXBxZm53Z05lelJsZGI3cTQvVGxmTzJZcFJGME1wNndyQXZjQzQiLCJtYWMiOiIyNjQxYjAzYjk5ZjZiZjU5ZjdlMTFiYmZjMjBhNWIyYWE4NGQyNzVlYzg1NTBhN2E3ZDdjZWY2NTAwZWMwNjY4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:14:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-87221128\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-511102277 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-511102277\", {\"maxDepth\":0})</script>\n"}}