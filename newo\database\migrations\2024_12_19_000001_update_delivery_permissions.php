<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class UpdateDeliveryPermissions extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // إضافة صلاحيات جديدة إذا لم تكن موجودة
        $newPermissions = [
            'manage financial record',
            'show financial record',
        ];

        foreach ($newPermissions as $permission) {
            Permission::firstOrCreate([
                'name' => $permission,
                'guard_name' => 'web',
            ]);
        }

        // البحث عن دور Delivery وإعطاؤه الصلاحيات الجديدة
        $deliveryRole = Role::where('name', 'Delivery')->first();
        if ($deliveryRole) {
            $deliveryRole->givePermissionTo($newPermissions);
        }

        // البحث عن دور Cashier وإعطاؤه صلاحيات POS إذا لم تكن موجودة
        $cashierRole = Role::where('name', 'Cashier')->first();
        if ($cashierRole) {
            $cashierPermissions = [
                'manage pos',
                'show pos',
                'show pos dashboard',
            ];
            
            foreach ($cashierPermissions as $permission) {
                Permission::firstOrCreate([
                    'name' => $permission,
                    'guard_name' => 'web',
                ]);
            }
            
            $cashierRole->givePermissionTo($cashierPermissions);
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // يمكن إضافة كود للتراجع إذا لزم الأمر
    }
}
