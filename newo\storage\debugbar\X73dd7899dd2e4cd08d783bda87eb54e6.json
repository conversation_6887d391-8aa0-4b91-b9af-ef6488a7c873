{"__meta": {"id": "X73dd7899dd2e4cd08d783bda87eb54e6", "datetime": "2025-06-08 00:28:15", "utime": **********.498705, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749342494.418788, "end": **********.498735, "duration": 1.0799469947814941, "duration_str": "1.08s", "measures": [{"label": "Booting", "start": 1749342494.418788, "relative_start": 0, "end": **********.319808, "relative_end": **********.319808, "duration": 0.9010200500488281, "duration_str": "901ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.319829, "relative_start": 0.9010410308837891, "end": **********.498738, "relative_end": 3.0994415283203125e-06, "duration": 0.1789090633392334, "duration_str": "179ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48114848, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.03986, "accumulated_duration_str": "39.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.384464, "duration": 0.01523, "duration_str": "15.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 38.209}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4148002, "duration": 0.0023799999999999997, "duration_str": "2.38ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 38.209, "width_percent": 5.971}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.441824, "duration": 0.00864, "duration_str": "8.64ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 44.18, "width_percent": 21.676}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.453441, "duration": 0.00216, "duration_str": "2.16ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 65.855, "width_percent": 5.419}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.465716, "duration": 0.009179999999999999, "duration_str": "9.18ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 71.274, "width_percent": 23.031}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.480652, "duration": 0.00227, "duration_str": "2.27ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 94.305, "width_percent": 5.695}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1856259549 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1856259549\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.462728, "xdebug_link": null}]}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-270596644 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-270596644\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1203497102 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1203497102\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1357721100 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1357721100\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1834 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749341283326%7C2%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik9Dc0xLaXlHRkZrQXlxMVFpVk9YVnc9PSIsInZhbHVlIjoiUE81c0pqV0hUY1ZaOGlHM3EvaTI3b0JmUUlQajNtTHM2dFozcU9YUUNMNFdqdzhhK2U2UWtBdHhjZm92WEdtV1ZUK0JpQWo1S1F3dHhYOEVjSDdMZVBSaUNnUW41Y3JSdGtYbUV1c3FMdXJiRURIN0ZFTVhoekx2S2RvMS9VUmN3ME1EeG1BMHVSVlRUSDQ5YnlpVHpUNFphOHRjdmRMZGRJNWFCR1hJMDh5ZnB1eHFONjNjUmpJSWFBNGNuZHlvYVZaQXBaV2JVNXlqa1dwWVFjZlNPNWlVRVhGR0J0VjA5RUQ0VlB2SjlnOS83NGJud1JjRmJjVWhwbmlwVHRSUDJINkVFK21LbzhqOVhsMXhFSjNOckhVU3drT3RiY1hoeFMrWTVGOUlSL3RSTk1TV1NjU2RxMlZQNTYyQURCdVFRT0o2UnNXdFE0KzNWUlQzLzV1U2c5RDVQZDkydHpXaXV6WnRCWVBVT21VczNsVXFQNXRBMzZDaER6SCtEMmF5Yk1JcVVSRjNONjBQSnFPU01zQlFBSUhrWGhmL0MweEhzUDdzNUVuSUdDdDUyaEw1TndseEdmak1VNm8zUCtZeVZ1SE8rWHZKSGFEUldYQmVndTdralkwTFZXSWhySFgzQVB4OENhRFR5UWxCbWQ3VEMxcDVDN0NGVTQvRFVMKysiLCJtYWMiOiJkZTA3NThjNWU0NmJkODczZmExOGFmZDMwODg1ZDQ0OGQ4OWJlN2RmOWU4NGRjMGJjMjcxYWJkMmNmZTg0ZmQxIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImdBaWpoa3MrSVMvbktONXpjdTZQQkE9PSIsInZhbHVlIjoiWnJQektGU3lXZTFXamFtWFVBMkMweUplTXR1eGdpQkVBcEJSVDFQcXVEWjEva3Y5WFdvT3J3UEEzRXBVQk5VTElmSGEwcFVZRVhnVE9WREdMUWZMcGhTNFVFL2lTdU4vZnFSUGZTNVJKdmMyTkRXYWhXQW5qVHBBT1RBbThaeW9DWFZEcDVWM0hVYnVhWTR0WEZ3N0dvTDdocm1IL1pVK0JmWkhlYkNldFpyK3BTQ3ZIM2syUUxxQ09jblVabWdwdUt3dkFaMW1RMTNHU2FVdkdDTE5mcG5GcklEaGF2RWxwOGx5cGdDNlFGeGF2NUxNS1BGdTZLdEN3UmJnb0FVK1BKTHdNTVFPVzNmbkVOZmxoQ3E5RXhpSURDUEZoSnFpc3p2SWxFNjFGQ0V4cHZjbjJsN2NMc3JMM0hYK01iQ3RlQTFWc3ZKWHFRSm8vbVJFUDNHOVBDejhuQ0dFRVRubmgxS2xRRTdLdzMzTFN5dnd3Y1kwOXRrc3FkZDNqSHpxTGRrd0JyOE5yZXhpMFJOZlZUTkJUdFRiR0xKSmxqSEdDKzhTM3ZxK1kydVp1MXVrSGc0ZUZWS081RUs1S3Myd2F0bGJxeXBTb0NLVlNpREFabFQ5VS92R3BXREx4eSt3clNyYzdVNHBEclFKeTJqSHRMODIzSjFabFNMTitRSzkiLCJtYWMiOiJmNjhkZjhhMDA0ODNhMmQ4YTVhYWIwYTQxZDYwMGQ4YTI5MzhjYzYxYmYxZjVkNjFkZTgwNzlmYzBhYzMzOGQ1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1802156081 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1802156081\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-9685463 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:28:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlJjZWdybTFPZE1rK1JnUHY1NTljV2c9PSIsInZhbHVlIjoiUE95STVlMW9RYmoydUhFN2FwQ1E1MUNWRWFiTXE5TGxsQzNZKzlXeGdxQ3VuRWhUTm9hRmhsam00a25uWXVEdXZjSWVFT3IxamFXMDRLeHV6N3QrTE9kRUdYejhPR3lzUGdMV3J3RUJaOHBKQWM5Vmh4b0M5Yk4wVUpJNHFaaXZOK01VbUY5ZTJLL05xbVdDcG1IRGkyWDBOeklvK2JZaE5NTGxaRllhajFjOWUrbFZ1ZFFhbG92cnVOUE82R2hQaVFmaHd2RkJ3c2ZzZTJhNjFuSUg4MVZjMGVXaFJIRHhtZVpybFZ6N2dSTllvbkhCa1czOCt5eVl6NkJBeFkzRzlwZFFTZWhZdXN1ZENnbWdGNHpBQXRaSWQ2TzdianE0L3NXYTVqQ3BUUDhrMWYxQ3dCR1dqeGxwUVp1M095SUVHS2FjYVJjNjY2ZmxzSGtuL2xCWGcwMmpUUEJuNE51enhCVlVCbXBuU29BOGVRWDR0aDkvVndIbUJMV2ZnNWFVcjR3U2I0U0VEaWM1R2p4TnBaVkVwbGhPSnNrVFdaZlB1emtoeWJ5Zk9sa0dWcHhlZm56WkpuSmd5K3R6dGowQkptV3RQVEdmVmtTdDBRcHV5ZTcxbDZ0ZDQwL2dxVDZxTHJoRFVFNlgrTGdWMVBzS3VldWk1NktGK0NhZXVwVmoiLCJtYWMiOiIyNGFlMWRmNTRjZWY4ZDU1MTcwNzgyYjAzYzEzZjlmMjk2NWM4NzMyNTU2NTlmN2MyZDNiZTg5ZTlmOWNjZGE0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:28:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IldTRzBiM28raW52OGxlMlM0NlBOTlE9PSIsInZhbHVlIjoidVlJRndTRkloY0hKN0FBM051UEdtUzdhSTE4VmpPdkpKRGE5QjhhN3E0RXl6aXo3MHpldDZseUUvUEJ2RmFHenZhQVEzczhJMC9lNXNtREpoUXplWDBvT2RXcjB5NTU2dGQwTjVqNnJVZ0cxeXFGWUJEeGlDNUhYY1hMbmJNb1pEekV0WHVRemZKa0NoOEZveEVIMy9lNm9rZ1V1cW01eFhWNnhZRG10NTNvbHZoYUV1M3ZiZUkwMzRzaFF1T1NDbWtsTDg1eURhek5GQUVKWkV6MUwybnVpTGZ4R0dYeUZuLzF1eWF6aUNRVzZicU95ZGdmTlJldmNsL3N4YW8wYkVCNWZuT1VLd2g4aXhwY254VGN4dkRINWVKNkkrOUVIODhCRWVsTEJ5U2ZLVVJxU0N5K0FZR2xNMFZLNTJjL2FydzRMdlZVTzhtNkZ6cElwVGdQNUc1SUlrWVZTc0gweUduYTJjbk9WZW5QdXE0S3kwS0pscGJ4MVBoVDJSNVNEbjNsNm96czYybjlzVGhCeWxiT2RFUWJmM3JTVzFidnZKQUo3OEhLaThIdE1DKzRWVUZLMUV3U1FyRlZBelBnTUZ1SnNWM1lFRXRPd3JLVmxUNGRZSnYwaVIwWnpIRUJoeWJabE5iWTRpNUEzQjJKQ3lDNHA1RC9GWW52QU42clgiLCJtYWMiOiI2Mzk4Yzc2MjdlOWUwZTcxMThhZWVkZDlhOWQyNmVhN2ZkYjIxMzc3Yjg3ZWU1NzA4YmExYzk1YmYzZWE5NmY1IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:28:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlJjZWdybTFPZE1rK1JnUHY1NTljV2c9PSIsInZhbHVlIjoiUE95STVlMW9RYmoydUhFN2FwQ1E1MUNWRWFiTXE5TGxsQzNZKzlXeGdxQ3VuRWhUTm9hRmhsam00a25uWXVEdXZjSWVFT3IxamFXMDRLeHV6N3QrTE9kRUdYejhPR3lzUGdMV3J3RUJaOHBKQWM5Vmh4b0M5Yk4wVUpJNHFaaXZOK01VbUY5ZTJLL05xbVdDcG1IRGkyWDBOeklvK2JZaE5NTGxaRllhajFjOWUrbFZ1ZFFhbG92cnVOUE82R2hQaVFmaHd2RkJ3c2ZzZTJhNjFuSUg4MVZjMGVXaFJIRHhtZVpybFZ6N2dSTllvbkhCa1czOCt5eVl6NkJBeFkzRzlwZFFTZWhZdXN1ZENnbWdGNHpBQXRaSWQ2TzdianE0L3NXYTVqQ3BUUDhrMWYxQ3dCR1dqeGxwUVp1M095SUVHS2FjYVJjNjY2ZmxzSGtuL2xCWGcwMmpUUEJuNE51enhCVlVCbXBuU29BOGVRWDR0aDkvVndIbUJMV2ZnNWFVcjR3U2I0U0VEaWM1R2p4TnBaVkVwbGhPSnNrVFdaZlB1emtoeWJ5Zk9sa0dWcHhlZm56WkpuSmd5K3R6dGowQkptV3RQVEdmVmtTdDBRcHV5ZTcxbDZ0ZDQwL2dxVDZxTHJoRFVFNlgrTGdWMVBzS3VldWk1NktGK0NhZXVwVmoiLCJtYWMiOiIyNGFlMWRmNTRjZWY4ZDU1MTcwNzgyYjAzYzEzZjlmMjk2NWM4NzMyNTU2NTlmN2MyZDNiZTg5ZTlmOWNjZGE0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:28:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IldTRzBiM28raW52OGxlMlM0NlBOTlE9PSIsInZhbHVlIjoidVlJRndTRkloY0hKN0FBM051UEdtUzdhSTE4VmpPdkpKRGE5QjhhN3E0RXl6aXo3MHpldDZseUUvUEJ2RmFHenZhQVEzczhJMC9lNXNtREpoUXplWDBvT2RXcjB5NTU2dGQwTjVqNnJVZ0cxeXFGWUJEeGlDNUhYY1hMbmJNb1pEekV0WHVRemZKa0NoOEZveEVIMy9lNm9rZ1V1cW01eFhWNnhZRG10NTNvbHZoYUV1M3ZiZUkwMzRzaFF1T1NDbWtsTDg1eURhek5GQUVKWkV6MUwybnVpTGZ4R0dYeUZuLzF1eWF6aUNRVzZicU95ZGdmTlJldmNsL3N4YW8wYkVCNWZuT1VLd2g4aXhwY254VGN4dkRINWVKNkkrOUVIODhCRWVsTEJ5U2ZLVVJxU0N5K0FZR2xNMFZLNTJjL2FydzRMdlZVTzhtNkZ6cElwVGdQNUc1SUlrWVZTc0gweUduYTJjbk9WZW5QdXE0S3kwS0pscGJ4MVBoVDJSNVNEbjNsNm96czYybjlzVGhCeWxiT2RFUWJmM3JTVzFidnZKQUo3OEhLaThIdE1DKzRWVUZLMUV3U1FyRlZBelBnTUZ1SnNWM1lFRXRPd3JLVmxUNGRZSnYwaVIwWnpIRUJoeWJabE5iWTRpNUEzQjJKQ3lDNHA1RC9GWW52QU42clgiLCJtYWMiOiI2Mzk4Yzc2MjdlOWUwZTcxMThhZWVkZDlhOWQyNmVhN2ZkYjIxMzc3Yjg3ZWU1NzA4YmExYzk1YmYzZWE5NmY1IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:28:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-9685463\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-776477654 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-776477654\", {\"maxDepth\":0})</script>\n"}}