<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('معالج فواتير البيع')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('invoice.processing.index')); ?>"><?php echo e(__('معالجة فواتير المبيعات')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('معالج فواتير البيع')); ?></li>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('css-page'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('css/datatable/buttons.dataTables.min.css')); ?>">
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <div id="printableArea">
        <div class="row mt-3">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5><?php echo e(__('معالج فواتير البيع')); ?></h5>
                    </div>
                    <div class="card-body table-border-style">
                        <div class="table-responsive">
                            <table class="table datatable">
                                <thead>
                                <tr>
                                    <th><?php echo e(__('رقم الفاتورة')); ?></th>
                                    <th><?php echo e(__('تاريخ')); ?></th>
                                    <th><?php echo e(__('عميل')); ?></th>
                                    <th><?php echo e(__('مستودع')); ?></th>
                                    <th><?php echo e(__('المجموع الفرعي')); ?></th>
                                    <th><?php echo e(__('الخصم')); ?></th>
                                    <th><?php echo e(__('المجموع')); ?></th>
                                    <th><?php echo e(__('طريقة الدفع')); ?></th>
                                    <th><?php echo e(__('منشئ الفاتورة')); ?></th>
                                    <th><?php echo e(__('المستخدم')); ?></th>
                                    <th><?php echo e(__('الإجراءات')); ?></th>
                                </tr>
                                </thead>

                                <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $posPayments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pos): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td class="Id">
                                            <a href="<?php echo e(route('invoice.processing.show', $pos->id)); ?>" class="btn btn-outline-primary">
                                                <?php echo e(AUth::user()->posNumberFormat($pos->id)); ?>

                                            </a>
                                        </td>

                                        <td><?php echo e(Auth::user()->dateFormat($pos->pos_date)); ?></td>
                                        <td><?php echo e(!empty($pos->customer) ? $pos->customer->name : __('Walk-in Customer')); ?></td>
                                        <td><?php echo e(!empty($pos->warehouse) ? $pos->warehouse->name : ''); ?></td>
                                        <td><?php echo e(Auth::user()->priceFormat($pos->getSubTotal())); ?></td>
                                        <td><?php echo e(Auth::user()->priceFormat($pos->getTotalDiscount())); ?></td>
                                        <td><?php echo e(Auth::user()->priceFormat($pos->getTotal())); ?></td>
                                        <td>
                                            <?php if($pos->status_type == 'returned'): ?>
                                                <span class="badge bg-danger text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;"><?php echo e(__('مرتجع بضاعة')); ?> ↩️</span>
                                            <?php elseif($pos->status_type == 'cancelled'): ?>
                                                <span class="badge bg-secondary text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;"><?php echo e(__('ملغية')); ?> ❌</span>
                                            <?php elseif(!empty($pos->customer) && strpos(strtolower($pos->customer->name), 'delivery') !== false): ?>
                                                <?php if($pos->is_payment_set): ?>
                                                    <span class="badge bg-success text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;"><?php echo e(__('تم التحصيل من مندوب التوصيل')); ?> ✅</span>
                                                <?php else: ?>
                                                    <span class="badge bg-warning text-dark" style="font-size: 0.85rem; padding: 0.35em 0.6em;"><?php echo e(__('جاري تحصيلها من مندوب التوصيل')); ?> 🚚</span>
                                                <?php endif; ?>
                                            <?php elseif(isset($pos->posPayment) && $pos->posPayment->payment_type): ?>
                                                <?php if($pos->posPayment->payment_type == 'cash'): ?>
                                                    <span class="badge bg-success text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;"><?php echo e(__('نقد')); ?> 💵</span>
                                                <?php elseif($pos->posPayment->payment_type == 'network'): ?>
                                                    <span class="badge bg-info text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;"><?php echo e(__('شبكة')); ?> 💳</span>
                                                <?php elseif($pos->posPayment->payment_type == 'split'): ?>
                                                    <span class="badge bg-warning text-dark" style="font-size: 0.85rem; padding: 0.35em 0.6em;"><?php echo e(__('دفع مقسم')); ?> 💳 💵</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;"><?php echo e(__('غير مدفوع')); ?></span>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="badge bg-danger text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;"><?php echo e(__('غير مدفوع')); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if(!empty($pos->createdBy)): ?>
                                                <?php if($pos->createdBy->type == 'company'): ?>
                                                    <?php if(!empty($pos->user_id) && !empty(\App\Models\User::find($pos->user_id))): ?>
                                                        <?php echo e(\App\Models\User::find($pos->user_id)->name); ?>

                                                    <?php elseif(!empty($pos->shift) && !empty($pos->shift->creator)): ?>
                                                        <?php echo e($pos->shift->creator->name); ?>

                                                    <?php else: ?>
                                                        <?php echo e($pos->createdBy->name); ?>

                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <?php echo e($pos->createdBy->name); ?>

                                                <?php endif; ?>
                                            <?php else: ?>
                                                <?php echo e(__('غير معروف')); ?>

                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if(!empty($pos->user)): ?>
                                                <?php echo e($pos->user->name); ?>

                                            <?php elseif(!empty($pos->user_id) && !empty(\App\Models\User::find($pos->user_id))): ?>
                                                <?php echo e(\App\Models\User::find($pos->user_id)->name); ?>

                                            <?php elseif(!empty($pos->shift) && !empty($pos->shift->creator)): ?>
                                                <?php echo e($pos->shift->creator->name); ?>

                                            <?php else: ?>
                                                <?php echo e(__('غير معروف')); ?>

                                            <?php endif; ?>
                                        </td>
                                        <td class="Action">
                                            <div class="action-btn bg-primary ms-2">
                                                <a href="<?php echo e(route('invoice.processing.edit.products', $pos->id)); ?>" class="mx-3 btn btn-sm d-inline-flex align-items-center" data-bs-toggle="tooltip" title="<?php echo e(__('تعديل المنتجات')); ?>">
                                                    <i class="ti ti-edit text-white"></i>
                                                </a>
                                            </div>
                                            <div class="action-btn bg-danger ms-2">
                                                <a href="<?php echo e(route('invoice.processing.return', $pos->id)); ?>" class="mx-3 btn btn-sm d-inline-flex align-items-center" data-bs-toggle="tooltip" title="<?php echo e(__('مرتجع')); ?>" onclick="return confirm('<?php echo e(__('هل أنت متأكد من تغيير حالة الفاتورة إلى مرتجع بضاعة؟')); ?>')">
                                                    <i class="ti ti-arrow-back-up text-white"></i>
                                                </a>
                                            </div>
                                            <div class="action-btn bg-warning ms-2">
                                                <a href="<?php echo e(route('invoice.processing.cancel', $pos->id)); ?>" class="mx-3 btn btn-sm d-inline-flex align-items-center" data-bs-toggle="tooltip" title="<?php echo e(__('كنسل')); ?>" onclick="return confirm('<?php echo e(__('هل أنت متأكد من تغيير حالة الفاتورة إلى ملغية؟')); ?>')">
                                                    <i class="ti ti-x text-white"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="11" class="text-center"><?php echo e(__('لا توجد فواتير متاحة')); ?></td>
                                    </tr>
                                <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\to\newo\resources\views/invoice_processing/invoice_processor.blade.php ENDPATH**/ ?>