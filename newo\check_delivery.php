<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use Spatie\Permission\Models\Role;
use Illuminate\Foundation\Application;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== فحص دور Delivery ===\n";

$deliveryRole = Role::where('name', 'Delivery')->first();
if ($deliveryRole) {
    echo "✅ دور Delivery موجود\n";
    echo "الصلاحيات: " . implode(', ', $deliveryRole->permissions->pluck('name')->toArray()) . "\n";
} else {
    echo "❌ دور Delivery غير موجود\n";
}

echo "\n=== فحص مستخدم Delivery ===\n";

$deliveryUser = User::where('email', '<EMAIL>')->first();
if ($deliveryUser) {
    echo "✅ مستخدم Delivery موجود\n";
    echo "الأدوار: " . implode(', ', $deliveryUser->roles->pluck('name')->toArray()) . "\n";
    echo "يمكنه إدارة POS: " . ($deliveryUser->can('manage pos') ? 'نعم' : 'لا') . "\n";
    echo "يمكنه إدارة الدليفري: " . ($deliveryUser->can('manage delevery') ? 'نعم' : 'لا') . "\n";
    echo "لديه دور Delivery: " . ($deliveryUser->hasRole('Delivery') ? 'نعم' : 'لا') . "\n";
} else {
    echo "❌ مستخدم Delivery غير موجود\n";
}

echo "\n=== فحص دور Cashier ===\n";

$cashierRole = Role::where('name', 'Cashier')->first();
if ($cashierRole) {
    echo "✅ دور Cashier موجود\n";
    echo "الصلاحيات: " . implode(', ', $cashierRole->permissions->pluck('name')->toArray()) . "\n";
} else {
    echo "❌ دور Cashier غير موجود\n";
}

echo "\n=== فحص مستخدم Cashier ===\n";

$cashierUser = User::where('email', '<EMAIL>')->first();
if ($cashierUser) {
    echo "✅ مستخدم Cashier موجود\n";
    echo "الأدوار: " . implode(', ', $cashierUser->roles->pluck('name')->toArray()) . "\n";
    echo "يمكنه إدارة POS: " . ($cashierUser->can('manage pos') ? 'نعم' : 'لا') . "\n";
    echo "لديه دور Cashier: " . ($cashierUser->hasRole('Cashier') ? 'نعم' : 'لا') . "\n";
} else {
    echo "❌ مستخدم Cashier غير موجود\n";
}

echo "\n=== انتهى الفحص ===\n";
