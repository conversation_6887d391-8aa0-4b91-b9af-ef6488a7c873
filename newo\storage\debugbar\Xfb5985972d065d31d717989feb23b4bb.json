{"__meta": {"id": "Xfb5985972d065d31d717989feb23b4bb", "datetime": "2025-06-08 00:30:59", "utime": **********.08509, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749342658.001196, "end": **********.085125, "duration": 1.0839290618896484, "duration_str": "1.08s", "measures": [{"label": "Booting", "start": 1749342658.001196, "relative_start": 0, "end": 1749342658.971528, "relative_end": 1749342658.971528, "duration": 0.970332145690918, "duration_str": "970ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749342658.971557, "relative_start": 0.9703609943389893, "end": **********.08513, "relative_end": 5.0067901611328125e-06, "duration": 0.11357307434082031, "duration_str": "114ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45593240, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.007859999999999999, "accumulated_duration_str": "7.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.027943, "duration": 0.00564, "duration_str": "5.64ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 71.756}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.05279, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 71.756, "width_percent": 15.267}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.065152, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 87.023, "width_percent": 12.977}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1972287596 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1972287596\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1936159513 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1936159513\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-178975361 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-178975361\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1370579277 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1834 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749342654379%7C5%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imt1bzBZWk84R1kwcFVOd3Y1VWJGYlE9PSIsInZhbHVlIjoiVmpOaGdvUEpmMGRsME1xemdtVWlMQm5iZHhISzRIbS9uSkNOc212a1o2UVM2RDg1Um8rdHc4dGJPNGNYZTVsd0hPR0xua2JoYnFvS3RnZFJBcFdsRkQ2alJOK1pVU1VBMkk0R3hwUlM0Mnl1Y05vSExNNkFqb216cnhNckttUUtZT1lwam1JcTlMUGNRYkFycGU0amJ6ZDUrcHdLc2ZaRElkeHZTVXN5L1J1QU5pRFpKRjk1SVp5YzhYSHNZL0cyemYyV0s2cFlENXZvcDBXZXVOY1FJOWxGTnFDN1ZvUFE1N2MrRFl4VEJMbGlSQktsb3BIM28wZ0owMURmRUdUZ0svQ1NzQmlRNkVrVFB4NHhJcmVvTkFvZXVaL0g1SkVmUm8rTlUwRWU0N3hNUkUyeHYyL3ZIdERCNnI2aEJQeXZWNFo3ZTAwakV4OWpDVXk0NnBBMDV3bWVOblFpSzVMdXdRTXorYTN6UzE0WkJRN2JtYjZUZ0ZHcUdGNENiT0RGb0JiSHh0WVoyWE9OYjhtRlhKZDNoNlpqczlNZlUvbTRLeEYzVEp3c1dLdXB2N2ZDVllGYnBWSU5KQndKTHo4Vlh4amVFWE9lclhvU0Fxa3FteW1CeDJqdGZVMHJsbGorWE9SNUZ0dmNHaGFjRmpaNXVBVG9mTUdKZWttcTZaTHUiLCJtYWMiOiJkZDdjNmQzM2QxNWI1NjgzMmM1NzA2ZTUzOTIyZWE4NWE3OGJiZGY2ZTMxNmIyYzhjYmNmOWRhNWQ1Zjg4MzI4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImV6RkM0blVNVjk2K0NaWXJkdk8xZGc9PSIsInZhbHVlIjoiZTNIYmpDWlk2b3BlcE5jaU5mQUt6T0xRVlRzUW9VODd1RGxNUUFBcHo0M0NHK0VVTnF2TXdwK2ErNXJyc2pIanVaWEFhZ1pzakxiclo3MVN1ckgrOFd2eEYvT2FLZzM3VWQyOER5ekt1blJnQjRLVStHdzFFQkRrTjR5cG0vR0pwdU51VUhobFV3QlEyS1hJVzcwVlM3ejh3WDdTY3pVeWFaRmZzSmczSHFUM0xhcFpsWlVEOEtHbHl6alFaY3hpOGtXS1B3b0xMQWcxblpqVFQ4b01VdWRWNDhlQmpwRHVMSE9FSlFpMVA2UEllYm5EY2d1WCtFT0JXWE1WeVZROUhsd3NlMDJsSzBpU2RrTUl5TThHNHJSS0tpUmVxK2g2aGZnTFprbmhPdjAyU1MzQWVzbjhhOGFpQXlZNW80cDNPUDRVUWd0a2h2N0FtM2VPcUxpTEdvTHRnUzVWZ0g4N1NJdEZ1dXBBNmNiNFNoQUM1QnAvVXV4aUdQRU1KNDdpOXp2Vk5nN2lldC82UEUyMGRMZnZiTzhNQloyUGNyNS82Z0RiRk9xakg1VnBTNVNuYTFLN2d6dWU1STMzbzVsVk1vMFQxNDgwODlsUjJLSUdwZXRkOVFnUXhOUWtIYVlVekdmM3BkSmR5VFNGVjFpQ292Q01DL0JPbzNGNlBNazgiLCJtYWMiOiI1Y2QxYzk5OGI1MTBlNTlhMDM1NzJiODVmZWI1MGY2ZjRjNjhlOTRkZTI4MGE0MzE4YWE5OTJlNmJiNzIzODMyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1370579277\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-277294195 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-277294195\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:30:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IitZRGJPVnJKWWJCbEFGMXRsanVLL0E9PSIsInZhbHVlIjoiMFJGeWxoMlhGdDlrTzd3M01yYnRMOE8rUHJBeWpoREo2aVdqWnJmWUdnaGtYdW9jOXlVemNUdUdxdC9mTmM3V0NhZk05M1dxWnRaMDhFNS9RMTFWTkJLZlVGRTZMQmkyUDNjL0FMQWZoQ0lNUno5a1YxTjRzOWc5VUx5SHI3VXM2ZllRZG8wM1ZabUhQZXlaSjA5YUJ1WVlocGFLZUZZK3MyQWhBc0hRQWZaZmpnd3lmTjM0Z1lhSGVhUWZXTEVRenNzeWp2VnJBN214Uis1UDJBK1pZOUtMVG04RUExVnN2bUJWRU1zS09xUG1aa24yaFR6dHYzSEg5YU5BOTdsSU5qWExKRnFhTDFJaTVpSkFkMy9qUTBUVkkvZjBiMFphL2FueEovTFBVNUV0TnRGSzB4MmZqbWd2SFJERzNPa0ZDZEJPSkFtbDJGT2NVaUN6aFJjVWJKZVo4dVRsbUNpblh4SFBMSWF2d2JJVktCRHY1TDFUbm01am94WW82TXkvaTlON2NUNFRLZ0pPM3ZUYjVkV3oyUGcxUkowdzYxUHF0RkpJYzBaRzc4cXBlWmlJUHZIZVZyVStsQzE4K2FZVlJjM3BrUWFoTXkxaWZ0WEt6ZUZZVFduMXJtTmlLblBYN090bXJjeTZQZ1prRk5ydm5wTmloMCtGUVZVT1Izb0siLCJtYWMiOiIyNGNlMTM3YTAwMDM1ODkzMmJmZGZjN2RhZTllNjllNGRiOGM4Y2Y0YzllNDUzOWMzMTgwMDI1ZmVhZGEyMjU4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:30:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ilg2aEZhd0hJTE1zdFlvSjFJcE9oOWc9PSIsInZhbHVlIjoiYjFlaEoremhMTTdSOUY4Wkg1TFZvZGJOdG8vWEpjM0V2dVM4WG1qaWlJaGprKy9CYktQQWtkbHgvN0FiRUZjUlBrOWJINXRwQWZEczN2aHdqU2gxRWdsMGNnZFlSUmVITVZzOGtneHF1ZXJyK3FwRmV4OEJSVXFDTnZ1R2hBNCtlcTl0U1dRY0hLaUlxUXJ4T0Mra2ZrWmo2RDhoWjhJOTlTc05PZ2FRcjdvOWVmUWZMMFUwVFoxTWFjeVdGejNBV1FJR2xwVDBZMTFacndvY0JkdHpuT0toVFg2dmRSREhobFA1NGIrTFVoaXUzRG5JNmtDSFpJaDdYQWZ0OU5OWUIvYitkdlhRRDlqMGdadzM2VmZDdUJ3bS9LMVNDNlYwMWVxVHJrOVhZT2llYXI0YmNXRUR3Zno4cUZid2RlN0Q0QnhNTUJKNldKL0l4MnlzRVQ0YzJsTjV5b1V0V3N1dEMrY1ZmaXlqclJSdW5pemZ5OEtHV2Z3cmMvblpPMmhHSUMwZElsNjFZRGlNalo4ZTJwSTkySjRTZFExb2pjUXlETEVkTlp4dldQcU9PUjlJdGVvWEVhSjYwNUk4dEc5YzhOTU1RL2ZzV2FvbmgrbHpRK1pod1pFU0RsVEFHdEtMMlBIdHdXa2t2dS9YYU9WTXpHZURNY0NMSHE2YmIySlAiLCJtYWMiOiI0MzZhZWIxM2IyYjNhNmI4ZTYwZjNlODdlNmRmZTlhM2I0NWEyZTBmNjY2OGFlNjZlYjA4NzdiZTNlMzA3Y2Q3IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:30:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IitZRGJPVnJKWWJCbEFGMXRsanVLL0E9PSIsInZhbHVlIjoiMFJGeWxoMlhGdDlrTzd3M01yYnRMOE8rUHJBeWpoREo2aVdqWnJmWUdnaGtYdW9jOXlVemNUdUdxdC9mTmM3V0NhZk05M1dxWnRaMDhFNS9RMTFWTkJLZlVGRTZMQmkyUDNjL0FMQWZoQ0lNUno5a1YxTjRzOWc5VUx5SHI3VXM2ZllRZG8wM1ZabUhQZXlaSjA5YUJ1WVlocGFLZUZZK3MyQWhBc0hRQWZaZmpnd3lmTjM0Z1lhSGVhUWZXTEVRenNzeWp2VnJBN214Uis1UDJBK1pZOUtMVG04RUExVnN2bUJWRU1zS09xUG1aa24yaFR6dHYzSEg5YU5BOTdsSU5qWExKRnFhTDFJaTVpSkFkMy9qUTBUVkkvZjBiMFphL2FueEovTFBVNUV0TnRGSzB4MmZqbWd2SFJERzNPa0ZDZEJPSkFtbDJGT2NVaUN6aFJjVWJKZVo4dVRsbUNpblh4SFBMSWF2d2JJVktCRHY1TDFUbm01am94WW82TXkvaTlON2NUNFRLZ0pPM3ZUYjVkV3oyUGcxUkowdzYxUHF0RkpJYzBaRzc4cXBlWmlJUHZIZVZyVStsQzE4K2FZVlJjM3BrUWFoTXkxaWZ0WEt6ZUZZVFduMXJtTmlLblBYN090bXJjeTZQZ1prRk5ydm5wTmloMCtGUVZVT1Izb0siLCJtYWMiOiIyNGNlMTM3YTAwMDM1ODkzMmJmZGZjN2RhZTllNjllNGRiOGM4Y2Y0YzllNDUzOWMzMTgwMDI1ZmVhZGEyMjU4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:30:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ilg2aEZhd0hJTE1zdFlvSjFJcE9oOWc9PSIsInZhbHVlIjoiYjFlaEoremhMTTdSOUY4Wkg1TFZvZGJOdG8vWEpjM0V2dVM4WG1qaWlJaGprKy9CYktQQWtkbHgvN0FiRUZjUlBrOWJINXRwQWZEczN2aHdqU2gxRWdsMGNnZFlSUmVITVZzOGtneHF1ZXJyK3FwRmV4OEJSVXFDTnZ1R2hBNCtlcTl0U1dRY0hLaUlxUXJ4T0Mra2ZrWmo2RDhoWjhJOTlTc05PZ2FRcjdvOWVmUWZMMFUwVFoxTWFjeVdGejNBV1FJR2xwVDBZMTFacndvY0JkdHpuT0toVFg2dmRSREhobFA1NGIrTFVoaXUzRG5JNmtDSFpJaDdYQWZ0OU5OWUIvYitkdlhRRDlqMGdadzM2VmZDdUJ3bS9LMVNDNlYwMWVxVHJrOVhZT2llYXI0YmNXRUR3Zno4cUZid2RlN0Q0QnhNTUJKNldKL0l4MnlzRVQ0YzJsTjV5b1V0V3N1dEMrY1ZmaXlqclJSdW5pemZ5OEtHV2Z3cmMvblpPMmhHSUMwZElsNjFZRGlNalo4ZTJwSTkySjRTZFExb2pjUXlETEVkTlp4dldQcU9PUjlJdGVvWEVhSjYwNUk4dEc5YzhOTU1RL2ZzV2FvbmgrbHpRK1pod1pFU0RsVEFHdEtMMlBIdHdXa2t2dS9YYU9WTXpHZURNY0NMSHE2YmIySlAiLCJtYWMiOiI0MzZhZWIxM2IyYjNhNmI4ZTYwZjNlODdlNmRmZTlhM2I0NWEyZTBmNjY2OGFlNjZlYjA4NzdiZTNlMzA3Y2Q3IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:30:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1858777887 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1858777887\", {\"maxDepth\":0})</script>\n"}}