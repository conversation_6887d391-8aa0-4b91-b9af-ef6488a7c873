{"__meta": {"id": "X1dfc5270a2cfccdc52221df6e1eaf71c", "datetime": "2025-06-08 00:30:10", "utime": **********.048941, "method": "GET", "uri": "/customer/check/delivery?customer_id=7", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749342609.084144, "end": **********.048973, "duration": 0.9648289680480957, "duration_str": "965ms", "measures": [{"label": "Booting", "start": 1749342609.084144, "relative_start": 0, "end": 1749342609.935079, "relative_end": 1749342609.935079, "duration": 0.8509349822998047, "duration_str": "851ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1749342609.935109, "relative_start": 0.8509647846221924, "end": **********.048977, "relative_end": 3.814697265625e-06, "duration": 0.11386799812316895, "duration_str": "114ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43910216, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02192, "accumulated_duration_str": "21.92ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.00404, "duration": 0.02105, "duration_str": "21.05ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 96.031}, {"sql": "select * from `customers` where `customers`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0335531, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 96.031, "width_percent": 3.969}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-758172797 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-758172797\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-131509762 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>7</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-131509762\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-766190108 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-766190108\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1261419503 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1834 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749342593005%7C4%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImFZV1M4SS9nQUFOMmQwTnFiV0pJMFE9PSIsInZhbHVlIjoiNy9GSWVKKzZ5bmNYM2NENGpIaURMWFUwM1lnTU85dnlZVHZKSFRFVENjNkhJcDk5YzZzSXVaSW9pZVc4QW9LcEJscHMwamF5RmFudGNZMHlFdlN5M3kvQmtJaWZLWFFZcXl0T1p0b09kczYyRTVHb1lrTkN6cWE4dFdxUXNDWDUwNk91T1QzV0QxRzIvbjAvanRRNFpkdVRYVmJCeXhYbkJ0Ykp6UjZDRVgwM2RjZEZTRFBEUVNJSWxNNEVNOXJQa0tNVWY2bzY2NTdrTDRacUJhSERTby9LODg3aW4xWlE1dWZWUDlSWCtvd2wxWmsxTFMrOGdKMTBTMlg0dU44ZGNMeDVlUDgwdmZSRkRETS96V3V6ZldscVQxQnB0TU5rVXJZdG9NMXFTeWlYQkZ3Y1FyRzlrZ1pWNmo1NC92WWk2TGNJOFhiem5xMElTbWIvbWxiNzgzNjBMUXllbzlZdkxlcnEwQkNRT1JGdURNTVdPUHdYMTFUTU9WNjU4bFQ2V3JEekVMSVdmYVVhc0JBenBTQ3h4NkUvdlZHVTVQc3ZrVU14R2c4eTRDVDhxSmJ2L2ZIZjNjc29tSHdhNzhsKzQyR1hzWS9ta0dUU1JaMnRKT3NuVHZ0YkY4NEFmdEhNZ1NlNGdrcys3anAySG13L2xOVVh0RHAxZ2plNk9mc0ciLCJtYWMiOiIyN2Y1ZWQxNTA2MGVmYTAzNjJjN2M0ZGU4YTk0ZTk5NTQ4MzhmYjM1NzA0N2JmNzJiMDIzNDkxOTBhNmNjNDQyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImhVK3UxYkR6c0hkWHJzV29kcnhjenc9PSIsInZhbHVlIjoicGFxbGtzMnBBWVl3VkR4d2JpNitOalNLb3RTQ000MVJGVlZOTXNWUzJIWVVLUVdXWjk0ZytYZ0dNaS9oNXdRNlZQVEVjZHVzMGlLcUhva1lxNU5YamFZWWdqTksya05wNXRmR0JzY3d6MDhLWjQwUU5XNHZ1dTg3eFJuRjNKZk55M2FLTCt6dmtMYmk0OEZ6dkNaNytsRFRYalRZdWw3UnFBcHprN2ZIdDdKTk1nV0g4K3BFOXNSeUdYakFxTlQrUEdXRjV3N0p1VDU3Z0pmT0dpVG1JckJHUFJGT3ZCN0o3WlREZGxqRXd0T2Y1ajEvb1FsOU9jQytsb1RmZXJWb0hJN2JDMHM0Z2VTaW1pYzRzcUJUNG1xSDdmbGJQcWxLenBKRlY2Uitra0xYenBsT2p5WUNVNEdrWjVyb215UElMVEhVTytza1IzOUdBbUgvc3pWMjcvbDNEVUNubmdFVzFLR3lUWnBzTFRDQjZuaVQyV3NKZm1aNjVCK1JUWjVnOGsxUzZQUDRGNjZnNXBxZFlDTW9OSmlyMGFocnZIUXhucDlJN3MzbS9QaVBmZHBpVmRldHdLUnVHL3JiSXdMdllKU0xBRjRKV0FBQnFMRStrL1lsaG1MS2pjY09Kd1FoZ2lPRWJ4QkxxT0NRNitiQk1QZVJvV2hjaHo2R2VZeHIiLCJtYWMiOiIzNzViMWMyMGY0YzdlZDVmMTBjZmU4OTY5MzBiMDJmMTc4NjcxYzJmNjc0NmFiMTgxYjQzYTI2YWMyNmJhMjljIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1261419503\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-755920119 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-755920119\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-162418698 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:30:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJMbU1SRkQ1LzZGbHcvb3pLTDNQekE9PSIsInZhbHVlIjoiWEI2SlYzdHRyc0RVWGQybkpLRURGNDkxNmZQZEcycStvVmVGbFRoTUtqbVFia2V3bStzeUxVVThBenpvaVVQeGwwTHVKWVpWYXhFRVVQekhBL1BJS09Jakd5dzFkTitBMVROZHlHUzVRdWhtbThFNVRaYTErM2ZxcFVrV256S2hWVlBaSjY2V2VVQXpiNFV0TVZzTFBDeHdhMG1CYTZ5a3h6L3ZoRzFlSkFWcW90QndDQTY1MmcxaUIrcnFkdThwbkZkb09ibHBjQVhGTXRjRkpKK0JhVDZuUFNDVVJjMDBzNFpMRGJHcFUvZ1JzY2RNRVRDNXNxZVVzQ3JGRVorMDFiWHdOYUZ1K25jVmc4UnYzVWZ6emtPYXZiQ09rUGltemVud05IZXpBekVua2tnR0cyZlpZY09xSkZYNXUzdzA2dGZ2cHRJUTVMQWJHWGxmcUNVTWI4YkFGYXZiVUNUUy95RnovZjBkY082TjNWaXRGK2Z4Q0M4NGNSOUZjZEUwbkZGcWpGWjlMS0tJU3pXdk9xVmE4UkcwdWIzdnlqUTJRZ0hlNFBWRFJacUN6TWNQSVl6T0JPS1lRNUFIK1pTZTVPd2preGxveTA4ajN5OEdNa21kQ2o1ZXNkdlZzSTQ1SzRHa2ROS2ZhNGFNWExqSHkxVmRVcHlZT0VwZVpNOEQiLCJtYWMiOiIwNTlkZDFlNGU5OTdkNWNjNGQ4YzkwNGYxMmUxNDlhZjhhOTRkZTBiZGFlMTUwMGU5YmU1MWNiZDQwY2Y0Y2U4IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:30:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImxJWUthRFU1QzJlZ01FdVZWUGtWakE9PSIsInZhbHVlIjoiLzJ6TmFkOGw2U2hEblZnRmtla0JYRUpZTStNN1crcmdxdnc4cklvRDJOZEtzcVdNTXpubEpDUER6R3VzQjB1N1pPUDhNUkM1WkZWaFJCZnp0NDhZb0JTRHZjcWMzck4yV1VMSlNUczFpcTc1cUYrNXdQMExXbGtVQVJYUlBHekQ2MUdwQ3lqc1FIYUdOVjRIMitDNFRMdE93Q2JPRGxpdXRxYkEwOUpGenZ6NFZzaEg1NW9RWGhEZ0JTTjF5VXh4ZHJLenA2WlBQRzZyVUI5eWZaOUZDdHBTS1lzM2tWcmN5SGJNbEplbjZCTW5TWXRaR2h4TFFuYWJ3V2t6bUJ2K0JqYWxJb0hjVThKYmtFTFhXTzU4YllmOXdnZ1dMa1FxSUxzRXYzV3lFWWhWNWdSUFdZSXNpMEJPZitFcEtqbm41OEZLeE1HdXpwRzd5OFAzK2JvY0pPUEkzM3NweVJuYzcxN3Jkd2EyaExkZFFsb1B0WXU2TmdLSis5ODFVODZoY05JNVplR3Z0dDVnRUlVMFJvNllrV0NNZUVoQnZUNkRRbzZ4SzUwY0xjYlZZdnp2WUFZOEduNDVtYjBmVkFERjhyMVlNVXplZ1RXbzVCU1pPcjZHTXltbWdnenlSRGk3WnIyUjhIak5DTDZSMUozZ3N0N3BVM0VDenlic3lCcEUiLCJtYWMiOiIyMGVhZmJiOTFhZDU2NmY1MzBhMmM0NDg0ZDc5MTAyYTllNGMxZDNjMzBkYTM3YzA2OWY0NTI4YTg3YjUwZWEwIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:30:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJMbU1SRkQ1LzZGbHcvb3pLTDNQekE9PSIsInZhbHVlIjoiWEI2SlYzdHRyc0RVWGQybkpLRURGNDkxNmZQZEcycStvVmVGbFRoTUtqbVFia2V3bStzeUxVVThBenpvaVVQeGwwTHVKWVpWYXhFRVVQekhBL1BJS09Jakd5dzFkTitBMVROZHlHUzVRdWhtbThFNVRaYTErM2ZxcFVrV256S2hWVlBaSjY2V2VVQXpiNFV0TVZzTFBDeHdhMG1CYTZ5a3h6L3ZoRzFlSkFWcW90QndDQTY1MmcxaUIrcnFkdThwbkZkb09ibHBjQVhGTXRjRkpKK0JhVDZuUFNDVVJjMDBzNFpMRGJHcFUvZ1JzY2RNRVRDNXNxZVVzQ3JGRVorMDFiWHdOYUZ1K25jVmc4UnYzVWZ6emtPYXZiQ09rUGltemVud05IZXpBekVua2tnR0cyZlpZY09xSkZYNXUzdzA2dGZ2cHRJUTVMQWJHWGxmcUNVTWI4YkFGYXZiVUNUUy95RnovZjBkY082TjNWaXRGK2Z4Q0M4NGNSOUZjZEUwbkZGcWpGWjlMS0tJU3pXdk9xVmE4UkcwdWIzdnlqUTJRZ0hlNFBWRFJacUN6TWNQSVl6T0JPS1lRNUFIK1pTZTVPd2preGxveTA4ajN5OEdNa21kQ2o1ZXNkdlZzSTQ1SzRHa2ROS2ZhNGFNWExqSHkxVmRVcHlZT0VwZVpNOEQiLCJtYWMiOiIwNTlkZDFlNGU5OTdkNWNjNGQ4YzkwNGYxMmUxNDlhZjhhOTRkZTBiZGFlMTUwMGU5YmU1MWNiZDQwY2Y0Y2U4IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:30:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImxJWUthRFU1QzJlZ01FdVZWUGtWakE9PSIsInZhbHVlIjoiLzJ6TmFkOGw2U2hEblZnRmtla0JYRUpZTStNN1crcmdxdnc4cklvRDJOZEtzcVdNTXpubEpDUER6R3VzQjB1N1pPUDhNUkM1WkZWaFJCZnp0NDhZb0JTRHZjcWMzck4yV1VMSlNUczFpcTc1cUYrNXdQMExXbGtVQVJYUlBHekQ2MUdwQ3lqc1FIYUdOVjRIMitDNFRMdE93Q2JPRGxpdXRxYkEwOUpGenZ6NFZzaEg1NW9RWGhEZ0JTTjF5VXh4ZHJLenA2WlBQRzZyVUI5eWZaOUZDdHBTS1lzM2tWcmN5SGJNbEplbjZCTW5TWXRaR2h4TFFuYWJ3V2t6bUJ2K0JqYWxJb0hjVThKYmtFTFhXTzU4YllmOXdnZ1dMa1FxSUxzRXYzV3lFWWhWNWdSUFdZSXNpMEJPZitFcEtqbm41OEZLeE1HdXpwRzd5OFAzK2JvY0pPUEkzM3NweVJuYzcxN3Jkd2EyaExkZFFsb1B0WXU2TmdLSis5ODFVODZoY05JNVplR3Z0dDVnRUlVMFJvNllrV0NNZUVoQnZUNkRRbzZ4SzUwY0xjYlZZdnp2WUFZOEduNDVtYjBmVkFERjhyMVlNVXplZ1RXbzVCU1pPcjZHTXltbWdnenlSRGk3WnIyUjhIak5DTDZSMUozZ3N0N3BVM0VDenlic3lCcEUiLCJtYWMiOiIyMGVhZmJiOTFhZDU2NmY1MzBhMmM0NDg0ZDc5MTAyYTllNGMxZDNjMzBkYTM3YzA2OWY0NTI4YTg3YjUwZWEwIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:30:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-162418698\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1368912096 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1368912096\", {\"maxDepth\":0})</script>\n"}}