{"__meta": {"id": "Xfe97c426c507a66a868b366d4d3d81ad", "datetime": "2025-06-08 00:30:54", "utime": **********.47381, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749342653.128586, "end": **********.473856, "duration": 1.3452699184417725, "duration_str": "1.35s", "measures": [{"label": "Booting", "start": 1749342653.128586, "relative_start": 0, "end": **********.276361, "relative_end": **********.276361, "duration": 1.1477749347686768, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.276385, "relative_start": 1.147799015045166, "end": **********.473862, "relative_end": 5.9604644775390625e-06, "duration": 0.19747686386108398, "duration_str": "197ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45589928, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00818, "accumulated_duration_str": "8.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.375699, "duration": 0.005719999999999999, "duration_str": "5.72ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 69.927}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.4154272, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 69.927, "width_percent": 14.181}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.438092, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 84.108, "width_percent": 15.892}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-101915735 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1834 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; _clsk=1i31pha%7C1749342593005%7C4%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ii90bHdPUWZvaTlDdXNnRWw3OVpSUUE9PSIsInZhbHVlIjoiaTR3L2o2bkdKUFQ2RGJOY2VwNC9jRzZkeC9CZGJrdlJmNlR1ei9oN1lVR3ZjWER1VDV4eHVyNDRjbE9oSWhwNFlEWXJiN3NPQkptSUJLYlBlaC81VjJubEZ2VDFxUE5YM0pFMU9QQkpRYVJVenZTb3pMNlE0MlJCRlVPT2pTUVZoZWdlNVAwWmw1aWplQTIycStsMVMwb3lGNWZHbVdhc1h1MDY1dVR1MmZYOVNNVHVwTlZCY012dlYwSllDcHpvSkdhM3gzL2VNeFlwZGdxYnhKdk5mNmt6WG5oMFIxSlBhOW9lYmxFaTFsSVlrb2IyME5WVWJGZk9QOGJsQzZBY3JrMnFtdWlEVUs3bVZFeXFJM0R5KzRBcXpodUVDY2ZKSDNqMEdxOU1La2hFRklBaUsxNjhzeVZYL1RBR0RINGV1NEFsMk1KcUNVaktMd3hZdlphU25qRllDNHR2MDBmV3FWYzV4VytMdVp2MzhBQUg5QXRNdHFlcDV6ejJiNFZEeUo1UC9GSmZUd1VrZDZFbngwaVg3dkYzbll6NGtQU0ozdVZ2c2hoOXY4VlJvWVF2RThtWlBoOTFpbHQwTTVOWWZZWFFhSDE5R1o5WWNld0ZjK29WZERnMXUzcktDcElHZEpnUXA5M1orMUNCRWNBaUVNSTl4SUNXWUFCK2NtMVkiLCJtYWMiOiI4NjNjMzUzZDFlZTM5OWViNDIzMDk4ZjkwYTQ2MzIyYjIyYzIyMTY5YjY3NGE5YTQ3OWYyMTM3OTkzNzczM2MyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ikc1bmI0c1VER1FoNCtVMUVwWm1hb2c9PSIsInZhbHVlIjoiWXZ0ekY0Rm5nSytlTDFYR0tPeDJWODUvKzZGUk1WdDRnTmplSTNISkxpdnFOYkVBZXRMK3NRZ2RuR1N3cVRJbG95YkRrL0ZDcXFjcGpDWVdzRnlra0xoMHRoeUttYnhZSmZBT2pJSkRkZ3VMSVhpNFRVc3BrQWkyUmdZYW03VS83RjJjRGVIWlJRYzZjQXBuWFhmRG0xaTZTalloU2xncVJxWEc2ZkVOd21HUVU1NU43WWl6OFBZNng0YmJhdGVyOXJTbkp2eU5mZXJDY3hERkJUcEpTcUxXSTNDU0ZRNVQ3a1pDU1RyNmRXYTNwR2g5eW9iSVlhcWJFalI1bWZPbkQxTlFpYVd6Mm5yYmV6aG9ERW9jR1RMdVYybjR2eDA1UEpSR2NQR0tlSkk4TTQwOUZDbXJ6dEVCMXB5V0c1L0xDTzMyREkwY2xiTC9xWWQwdUx1dCtSbi9JSlVackpzVzhITXdPb3NFdWlGRVpscGp2QzhvZTZzOTBhUXZYOWNOZXc3cVI2WTM4bzlFMWovZ0VmbDhRVlAva3RhV2RWcGdxSU9qdDBwRW01d2dhRzVsWTRpL2dLWWpTd1BDRkpSTjA3U0ZxU1p4bE1GaHk2OU50UGVUcmxETWlWVHFXYjBrcE1aVzVwd3FqY3h5bysvdUZXU1NDVHU0QTJPTzc4UXoiLCJtYWMiOiI5ZWIxNmFiZGVmYmMzNzJlOGE1MzRlYmI5ODMzODU4NjgwYTFkNGEwZjI4MDkzNDU0OWRhMWIzZGE0M2M5MzA3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-101915735\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1196059317 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1196059317\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-200707023 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:30:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNYVlpIWTBERmNHUXdDSXdlNGdhL0E9PSIsInZhbHVlIjoiNWc3WTY4bFJNbm5aTHU4RWxKUExjSmFaWWVkYk9xb0NPQ2R2SUdMdHg0QXdXYnBtRlVuaXlJdG1KVTJyM1ZSTE53bWxPRnpGdGRoNmxjbklGdHBjS2tkYlB4UDBEREduSkVzTE1ZT3lDT1ZGSDJ0bmt2Q295Y3VWSWZWQmJMNTNyQjE1ekUvaVBvWktIcTdNMmFFbnVHeGFsKzNoZk1TbmprOElKUUh6WUJXOUx1eEtURWlLNDNpWmN4NEVDQ29Iazg1eHFvbndZdVl1NnRzRGl4SnlNck5GWG1uZVFMRVM4ZUZWSkQxR1VINmpWL2k0L0tMRTFLaVphOUFGOFJaQ3ZHeWRkZlBYNTQ3czR2NlpiSXdYWUp1T3Vvcmcva3VWQVU4MnEwQlRWYklUOWNtT096RkZleEpvbXNEeTI2MExBNnR4cUJmUGdWaVVoS29MbklIQld5dk9PbllJVlc3eFY0QlBpamhGTTF4VFArbFZmcWRqN01LMGVUV1FPWndqMk8yTXgrWEVBTngyWVZYeGR6dWFFL1NRUEdmZWs4WkdPelVuYk1mQ0xRZm1wRVNpeDd1eDdKRTJ4c0F0K1A0UE5SS3ZpMSs4V2tqdFJCRm5WQ3dZcFhnTFlTKzkyVmhlTTdmZDZ0bEFQR3EyK251Tk0yNVdxdjFzZDFzeWVZNWgiLCJtYWMiOiI2ODk2ZDJmMTZkYTNmMTUxZmE2MWJkYWIzMWFlMzVhMzQxZTVjNGM3OWZjODgxYmJjMzNiYWMxNmU3ODcxYTRjIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:30:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IndVMGxPWjFFUW1aUHZPalVpUldxd1E9PSIsInZhbHVlIjoiWlNnOXJyKzhYWU5lSTJTaGJjVDkwbGprdGxIVEhEQlNIMWhNTGx2aU1EQm52VEpuYUt5Ri84OFdyNGFnUU1tTUhoa0QydjhIVUdlUWZoa1Zia3plakVoN09tY1ZxVWZDcmdKT01Eb0l1NWF4aVZwVHR6YW9ZOXl3V2hNbTMxZTc3c0JCYjRhWGcwbjJTOEZCb0FxaTE1Qng3cVdzemtsVmZrZWR1RVFLZGxHUDdMT1pobWpibDBzcU9pR0ZkV0YzTVFOOWlwcW1tM2paYysyM05BR3pVOXRWREIxZmZOaUxuN0l3OTdGdXFTT0ZwMnVBWldMc3U5T3NXRks2SzdMN2lHazN5aEw2SWh0cTQ1Njd6SURCNFo1TGdkc3JjaUxNNFFHU1o3ZlZlbkJrb2xRNGtuS25WRXhKUmJIUDVlRGVYOXNLMURWTTk4NXM5OG1heWl5MHdWMHh4V2VlWlM4ZWJ5Nm9rWXJqWGJzbUhCc3hCYkhaVDRvckxIUk0rTVlVMnJaWjRLbTZRRnNrSXJFb1dzSWZvNFZPRnJwdjZoUDJiTUE1YW00NkphOEwvdnk4OWhZYWFOY3BzV3Q3cmxXLzkydzhKZDA4VjBXV1NKWHhxUFBMN0IwOHdyTkF5TE5GT1BJTENZM2plcmtsYkM4ZVd0bFgyK2xxOUsxbGtKMnkiLCJtYWMiOiI4MzkyYzg2NDUwYTQ2ZmE3OWI0MjQwOGIzNDNiNWExNTkwOWVhMjMxY2Q3NDU2MDA1YzBkYTkzYjI5Y2EwN2E5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:30:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNYVlpIWTBERmNHUXdDSXdlNGdhL0E9PSIsInZhbHVlIjoiNWc3WTY4bFJNbm5aTHU4RWxKUExjSmFaWWVkYk9xb0NPQ2R2SUdMdHg0QXdXYnBtRlVuaXlJdG1KVTJyM1ZSTE53bWxPRnpGdGRoNmxjbklGdHBjS2tkYlB4UDBEREduSkVzTE1ZT3lDT1ZGSDJ0bmt2Q295Y3VWSWZWQmJMNTNyQjE1ekUvaVBvWktIcTdNMmFFbnVHeGFsKzNoZk1TbmprOElKUUh6WUJXOUx1eEtURWlLNDNpWmN4NEVDQ29Iazg1eHFvbndZdVl1NnRzRGl4SnlNck5GWG1uZVFMRVM4ZUZWSkQxR1VINmpWL2k0L0tMRTFLaVphOUFGOFJaQ3ZHeWRkZlBYNTQ3czR2NlpiSXdYWUp1T3Vvcmcva3VWQVU4MnEwQlRWYklUOWNtT096RkZleEpvbXNEeTI2MExBNnR4cUJmUGdWaVVoS29MbklIQld5dk9PbllJVlc3eFY0QlBpamhGTTF4VFArbFZmcWRqN01LMGVUV1FPWndqMk8yTXgrWEVBTngyWVZYeGR6dWFFL1NRUEdmZWs4WkdPelVuYk1mQ0xRZm1wRVNpeDd1eDdKRTJ4c0F0K1A0UE5SS3ZpMSs4V2tqdFJCRm5WQ3dZcFhnTFlTKzkyVmhlTTdmZDZ0bEFQR3EyK251Tk0yNVdxdjFzZDFzeWVZNWgiLCJtYWMiOiI2ODk2ZDJmMTZkYTNmMTUxZmE2MWJkYWIzMWFlMzVhMzQxZTVjNGM3OWZjODgxYmJjMzNiYWMxNmU3ODcxYTRjIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:30:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IndVMGxPWjFFUW1aUHZPalVpUldxd1E9PSIsInZhbHVlIjoiWlNnOXJyKzhYWU5lSTJTaGJjVDkwbGprdGxIVEhEQlNIMWhNTGx2aU1EQm52VEpuYUt5Ri84OFdyNGFnUU1tTUhoa0QydjhIVUdlUWZoa1Zia3plakVoN09tY1ZxVWZDcmdKT01Eb0l1NWF4aVZwVHR6YW9ZOXl3V2hNbTMxZTc3c0JCYjRhWGcwbjJTOEZCb0FxaTE1Qng3cVdzemtsVmZrZWR1RVFLZGxHUDdMT1pobWpibDBzcU9pR0ZkV0YzTVFOOWlwcW1tM2paYysyM05BR3pVOXRWREIxZmZOaUxuN0l3OTdGdXFTT0ZwMnVBWldMc3U5T3NXRks2SzdMN2lHazN5aEw2SWh0cTQ1Njd6SURCNFo1TGdkc3JjaUxNNFFHU1o3ZlZlbkJrb2xRNGtuS25WRXhKUmJIUDVlRGVYOXNLMURWTTk4NXM5OG1heWl5MHdWMHh4V2VlWlM4ZWJ5Nm9rWXJqWGJzbUhCc3hCYkhaVDRvckxIUk0rTVlVMnJaWjRLbTZRRnNrSXJFb1dzSWZvNFZPRnJwdjZoUDJiTUE1YW00NkphOEwvdnk4OWhZYWFOY3BzV3Q3cmxXLzkydzhKZDA4VjBXV1NKWHhxUFBMN0IwOHdyTkF5TE5GT1BJTENZM2plcmtsYkM4ZVd0bFgyK2xxOUsxbGtKMnkiLCJtYWMiOiI4MzkyYzg2NDUwYTQ2ZmE3OWI0MjQwOGIzNDNiNWExNTkwOWVhMjMxY2Q3NDU2MDA1YzBkYTkzYjI5Y2EwN2E5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:30:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-200707023\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-14******** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-14********\", {\"maxDepth\":0})</script>\n"}}