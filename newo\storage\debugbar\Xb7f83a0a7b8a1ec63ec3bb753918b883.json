{"__meta": {"id": "Xb7f83a0a7b8a1ec63ec3bb753918b883", "datetime": "2025-06-08 01:15:18", "utime": **********.869351, "method": "GET", "uri": "/add-to-cart/5/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.205132, "end": **********.869373, "duration": 0.6642410755157471, "duration_str": "664ms", "measures": [{"label": "Booting", "start": **********.205132, "relative_start": 0, "end": **********.711404, "relative_end": **********.711404, "duration": 0.5062720775604248, "duration_str": "506ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.711419, "relative_start": 0.5062870979309082, "end": **********.869376, "relative_end": 2.86102294921875e-06, "duration": 0.15795683860778809, "duration_str": "158ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53622552, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1322\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1322-1579</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02052, "accumulated_duration_str": "20.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.77196, "duration": 0.01582, "duration_str": "15.82ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 77.096}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8015602, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 77.096, "width_percent": 3.996}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.828477, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 81.092, "width_percent": 5.312}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.8324041, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 86.404, "width_percent": 4.727}, {"sql": "select * from `product_services` where `product_services`.`id` = '5' limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1326}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.840415, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1326", "source": "app/Http/Controllers/ProductServiceController.php:1326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1326", "ajax": false, "filename": "ProductServiceController.php", "line": "1326"}, "connection": "ty", "start_percent": 91.131, "width_percent": 4.337}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 5 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["5", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1330}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.8478858, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "ty", "start_percent": 95.468, "width_percent": 4.532}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 16,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-700725690 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-700725690\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.838691, "xdebug_link": null}]}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "pos": "array:1 [\n  5 => array:9 [\n    \"name\" => \"Free Plan\"\n    \"quantity\" => 1\n    \"price\" => \"12.00\"\n    \"id\" => \"5\"\n    \"tax\" => 0\n    \"subtotal\" => 12.0\n    \"originalquantity\" => 22\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/add-to-cart/5/pos", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1512198520 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1512198520\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1664041900 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1939 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i31pha%7C1749345246637%7C11%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjFqU0RtZWUrd2Rpc0FyWFR6UnJ0eFE9PSIsInZhbHVlIjoieENwTW9UT1NCTzlNMCtlOUhmeXhtaTE2d3RFdlV4SkVZSUF4M09mVG1Zbkl5NEx3VWlBOWNmWW1MZ1ZxbWtmV3h0aXE5UklqYkcvaElJN2RqT2swQ3BQYjJHalJ4N2JRRFF1TnRoSmMrMjB6QTgwbnJWalo5TW83cXE2MVlPbGV5SXdwQnlCdTlMSDl3RHAwazhac0VPVFBla1hJS2hOV1l5ZW5Ccms1WGZlSDArZnNxV0o4MmZPN00wOWlUeGErY0RXS2lxOXppVmdqcC9ULzVsUFd1ZzBXUHJ3blFsWDlUQ2QvU3BFVHF2N2NYVGhrVTN1TlY4eEl5UkVXdUQ4Y2MrTW9KMmFWaHZPWEZ4WW16RHFhUW9LNE5Zak9YZVVFdWVGdG5VcW1BNXVkSEZMTE1JZmxiV21YMHUwNThzc1FKWEdrd1FBTUdsL2FKYU4rZ0JYcVRIRGtaSzF4amdHVDB2d3JrV3lLTWQ0STVsY2R4TWEzbTBGc2I4ajd2T3VSQkR3V3hITkVWak1LSm8rS3NNWmpKZm1SdlZJdkYxdEpoaFB4RHZEOFFuNTNGZk15UFF2QndGMlJKWnRJUzFGUm9oM3FhV3RsMFU1RFQzZjVjdDVOTDN1eUQyeHVXc3NqNk11VkZibUZYbytoblpVRkRva0NVWVkySmpoU2RHb3kiLCJtYWMiOiI5NjAxNTc1ZGY2YWZjY2RhOTE2OTU5MjU5ZTQ2ZDUyZGZjOTMwNTBiY2IzMTQ4YWVjNjQ1NjAxN2IyMDY5M2Y4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Ik5nVm5qcUdVNFBmeUV2dzBubTB5ZkE9PSIsInZhbHVlIjoieGJFTXRpaWkwZkRhcVdzd0RSQVQ4TmpUZjJFZm5nSzhVSW5NYlhhaStqaEFOdkFxKy9CTUlYUUZUcEJ2d0I0R1l1R1ZZTWNIRXI4dFFUN28vcW4rWXpiYmtLNkcva2MyZFpPd05JeVlmQTA2R0lwTXkyNGtxelphQzZSaGdkazBWdFVKRUN5SitWcU1KS3NCeWsrZHpXRG9SclgxL2crUTQrTCswcTVmMlVSVDVwcHNOU004RmIycEhUTG1NTG81ZUFOeG1BYlJlTmVwditUeEd6V2ZEYzNLQi9sK0R1TkFtZDVQTDdkN3RrMzdvL01STmdwRlM4aFBlZVQvUXlPcnlKd3NwVlpLbTgzeEczSjZwRTYzTVZNc0lKV3g2VC9qUDdVdGRpdVlBalliMi9TdFc4QlY2NCtiTDdOQzFHRGtFT0RvSm9Tck1Ka2lyZ0s2NUFDVi9MNVE0YlJ6N2FPR0QrWFl6SFVQUGtUNnlDWktYazFTaTdwTVlIOExOd1RSekhDZUlZK3F2d0ZhL2gvbzgyOXpENWZWUU5ockVnZkRRbDljODU4QTNkTlE3T284SWhTeUZ4UDluRmd2aU5CK0IzbnM5SzBZT0lvZUlrRHdZbk14UUN0b0R6a3M5R2s1bHBJdWYweFY0dlpMVjNHT2IwTzA1Yno4TVBQRDdmamwiLCJtYWMiOiIzNzQ1YmQ1OGU3YjExNTk3MWVkNWMxMDcwNTEzOTRmYWM1YzM1ZTk3OWJkZTFhY2EzM2VjYmYwYzlmNGUzZDE2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1664041900\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1141967201 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1141967201\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2001801280 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 01:15:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNLT2w3QTJTcVVTdWw4S2F2ZnlPS3c9PSIsInZhbHVlIjoiMzg3aFRVKzBGcVhlSHNkbk9JRktwUjRlNDdoMk4rUE95aWV3UnVhUCtONysrSGIrYTlsV044Y3RQcnF5cTdXNVV1TEVHRitMYlRkdTlKZTNRZkM3cDJsZ0lXMFhDQWhtWjFScmNtSlpyRmFLVnNHaWpjc0x0b3drbDVDakcvekVmNnowdExKMXQzc0xjSGgxVlZpQ1NRaWNFeFZMbU5SSVFEWDFwQVdRY0x0Q2xweGhzRFQ4MVFaU2tYYXVvM1FBa3pQVkRiZHhZTTZxbDFSSnZWZm5vZFcxME1GSys0eGxCUFhoQTZ1d0hvRTZxS2Zra3ExVmpWbG9PRVVYUjlUUU5nWjNVcTZmVFg1UWZZbkpGb3VxK0FQc1lHc1pEOVdXUDZXSlczaHVUNEppUFRtVk1qSHNZLzFVcGtYUkhPR2pISlNkVHF0R1JQejlLSi9sc0NDenRHSmRxeEE4MEhqYlpRN1d6YUFnQlVxVVFUTW5vZG5LSkp6YzhhVXhZRnpQWnFzWTljRXFJS3RFMEhrVUZNaTF2UVl6b2xwTG5xeUZwWXlIQmx4M2xTM2FJdi9SYktXdDB4SGprTTcwWHhIWVFkdzFIOUpLcU1iTDU3TkNwRU8yaHdSbGFvR1RqSWt4SG1uc1pPamZZeTNrbE1qU2ZxRm41SHhzSTUrV3lFdWQiLCJtYWMiOiJjMGIyNWI3ZDk0NzZlNzg5NDVmOGJhMzZmNTZkNDQ5ZWI5NjlhNmIwMGJhYzJmNDNhODZjMTJkNDFiZmJmODM5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:15:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlRsenM0ejRIR2dlRlBiditLS1lhNUE9PSIsInZhbHVlIjoiZVVxSEFzaFZSR0VjNjRwOU4vRGxFdmZSNmhPd0FHb0xmWmxFODQyRkl5VzBwVG96OEs4blFKbTNjazhobTVjeGo3dGpTbUNJZjRrdjlESEdsTmVVdEhVUkJaUDdkTjExN0svb1ZVLzNTZEFWTkxMWDMyOUZmOWtoRXZtcCtlM1dndHJ2YlNMNk1UYmdWWGt0VkV3bGFKTjA5TnA4dGI0REViMDhBMCtrMm1GVDJaVzFSaFZCSHNhMVo3d2VTUXdESXFFTzJPSHIva3M1bFFpUDNBS2RSWmhGODlhOVpHZTRmMHpJdFM3OXN3WTlGZ0Vxc3JJRDF6YUNoeW41eXdBWVJqdFNsM2lvMDBhOGQ3eWRvdkp4ZGN5dDlrOUp2MlQ3cjNFZ1c3VGlaS2xXbVZGVkVLU1cyb3NqaDJVV2ZUREY2V2xzdHZ1UlgxOWNIUTZNaUNEUXVwbW0rUzh2RnFVaEphMUczOURBM2Nab0Y0MFhXZWxvaFlhTVJqcVU2TzBJM3ZWOHRwRlpvOVByaEdtSzRkZE1McXBjLzhLTExld1k1NVZvYmExVFFWaEppZERiSzczRWtmS0VKRFF4S0JaMCt3TFpQMFZvVk0wUFVFYUZYYXZRQU15b0lCOVEwRTFrNTRZVlUxb3RJd0RGNlRvVjE5dzh3NWc3Wlk3OVBsZGciLCJtYWMiOiJmYzk2OGQ5ZTAzNjUyMGQ1MjJjYWQ5NWM2MjliNjNiZGFiZDJmYzdhMWU3MmVkZmNmNDlhZDQxY2JhYWI4NWY0IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 03:15:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNLT2w3QTJTcVVTdWw4S2F2ZnlPS3c9PSIsInZhbHVlIjoiMzg3aFRVKzBGcVhlSHNkbk9JRktwUjRlNDdoMk4rUE95aWV3UnVhUCtONysrSGIrYTlsV044Y3RQcnF5cTdXNVV1TEVHRitMYlRkdTlKZTNRZkM3cDJsZ0lXMFhDQWhtWjFScmNtSlpyRmFLVnNHaWpjc0x0b3drbDVDakcvekVmNnowdExKMXQzc0xjSGgxVlZpQ1NRaWNFeFZMbU5SSVFEWDFwQVdRY0x0Q2xweGhzRFQ4MVFaU2tYYXVvM1FBa3pQVkRiZHhZTTZxbDFSSnZWZm5vZFcxME1GSys0eGxCUFhoQTZ1d0hvRTZxS2Zra3ExVmpWbG9PRVVYUjlUUU5nWjNVcTZmVFg1UWZZbkpGb3VxK0FQc1lHc1pEOVdXUDZXSlczaHVUNEppUFRtVk1qSHNZLzFVcGtYUkhPR2pISlNkVHF0R1JQejlLSi9sc0NDenRHSmRxeEE4MEhqYlpRN1d6YUFnQlVxVVFUTW5vZG5LSkp6YzhhVXhZRnpQWnFzWTljRXFJS3RFMEhrVUZNaTF2UVl6b2xwTG5xeUZwWXlIQmx4M2xTM2FJdi9SYktXdDB4SGprTTcwWHhIWVFkdzFIOUpLcU1iTDU3TkNwRU8yaHdSbGFvR1RqSWt4SG1uc1pPamZZeTNrbE1qU2ZxRm41SHhzSTUrV3lFdWQiLCJtYWMiOiJjMGIyNWI3ZDk0NzZlNzg5NDVmOGJhMzZmNTZkNDQ5ZWI5NjlhNmIwMGJhYzJmNDNhODZjMTJkNDFiZmJmODM5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:15:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlRsenM0ejRIR2dlRlBiditLS1lhNUE9PSIsInZhbHVlIjoiZVVxSEFzaFZSR0VjNjRwOU4vRGxFdmZSNmhPd0FHb0xmWmxFODQyRkl5VzBwVG96OEs4blFKbTNjazhobTVjeGo3dGpTbUNJZjRrdjlESEdsTmVVdEhVUkJaUDdkTjExN0svb1ZVLzNTZEFWTkxMWDMyOUZmOWtoRXZtcCtlM1dndHJ2YlNMNk1UYmdWWGt0VkV3bGFKTjA5TnA4dGI0REViMDhBMCtrMm1GVDJaVzFSaFZCSHNhMVo3d2VTUXdESXFFTzJPSHIva3M1bFFpUDNBS2RSWmhGODlhOVpHZTRmMHpJdFM3OXN3WTlGZ0Vxc3JJRDF6YUNoeW41eXdBWVJqdFNsM2lvMDBhOGQ3eWRvdkp4ZGN5dDlrOUp2MlQ3cjNFZ1c3VGlaS2xXbVZGVkVLU1cyb3NqaDJVV2ZUREY2V2xzdHZ1UlgxOWNIUTZNaUNEUXVwbW0rUzh2RnFVaEphMUczOURBM2Nab0Y0MFhXZWxvaFlhTVJqcVU2TzBJM3ZWOHRwRlpvOVByaEdtSzRkZE1McXBjLzhLTExld1k1NVZvYmExVFFWaEppZERiSzczRWtmS0VKRFF4S0JaMCt3TFpQMFZvVk0wUFVFYUZYYXZRQU15b0lCOVEwRTFrNTRZVlUxb3RJd0RGNlRvVjE5dzh3NWc3Wlk3OVBsZGciLCJtYWMiOiJmYzk2OGQ5ZTAzNjUyMGQ1MjJjYWQ5NWM2MjliNjNiZGFiZDJmYzdhMWU3MmVkZmNmNDlhZDQxY2JhYWI4NWY0IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 03:15:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2001801280\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Free Plan</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">12.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>12.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>22</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}