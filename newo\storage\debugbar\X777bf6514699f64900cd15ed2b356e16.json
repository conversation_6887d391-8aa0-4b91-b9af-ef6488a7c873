{"__meta": {"id": "X777bf6514699f64900cd15ed2b356e16", "datetime": "2025-06-08 00:58:00", "utime": **********.825018, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.140764, "end": **********.825039, "duration": 0.6842749118804932, "duration_str": "684ms", "measures": [{"label": "Booting", "start": **********.140764, "relative_start": 0, "end": **********.729822, "relative_end": **********.729822, "duration": 0.5890579223632812, "duration_str": "589ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.729842, "relative_start": 0.5890779495239258, "end": **********.825042, "relative_end": 3.0994415283203125e-06, "duration": 0.0952000617980957, "duration_str": "95.2ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45285064, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1671\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1671-1681</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01599, "accumulated_duration_str": "15.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.780853, "duration": 0.01489, "duration_str": "14.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 93.121}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.809489, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 93.121, "width_percent": 6.879}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-600447235 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-600447235\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-767449154 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-767449154\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1666945585 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1666945585\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1745687865 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">_clck=1dm5toa%7C2%7Cfwl%7C0%7C1985; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1i31pha%7C1749344276347%7C8%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InFoS04zK0NUZ2VLSXYvTitUcnQyNmc9PSIsInZhbHVlIjoiWU5OUWFXbzFWU2I5czZyNlJWeENxakxCYjd0dFBPRStqQUlSOWNsenQrOWIxdy9ENVJZelUvMXExUWNBaVZ5SVd2aFZQMDFqWWdxMXMxMndqODhyWU1PVlFKZlcvQW5Wb3hNSVZ1RFJ4VnVpaWhsczBWblhwUTQ1RldJcWo1bFJDRU1QR0xOamlQRDlBNjZQYnRmckE3Sm1UbEZyd3phdVJwZnVoNzFBOGFHanJCK2UwNmlkWlp3TWZQeHNYYjlQb2tKM2hKYnF2UWJNVTljWXZWc05HR253M0hyNHR6THF3OHp3eWVhNXRUdXczeDVJRGhRQVVOTDltclJLTjNHdnNKa0JoVHdFU3hLSzdOVEdxYmoyOXd2MUg0N3JUZEVhdDhtVjNVbDBQYWZ6ei9SbWVXTUs0ZEZ0NUJQRElTN1lDZ2RTYkR1M2l6QTU1MGd0b1NTZFFXcFY4aCtpb0Ztb29WcS94SWVzUjJFMndpMnd1d2MvTjZ2cXNkK2xHcVBQeTBTTnc0YWduU1owTFgzOWRhMVBGNDdOcktUWnpNY0FEblZmeDduTW1lYXV1RUFrQ3doMEgwcThCSExMd2JuTnYzeUs4bVFXMytOSlJVMGJ6Z0VNR25PVS95bzRBNVZLRHRPOWU0TnhWYnh0YXJ0bVNUbnZJazVPYWJiSGR0TDEiLCJtYWMiOiI0MGE4NmM3MzllYmRmOWYzODc5NTJhZTAzNDM1OTRmOGVhNGY1YWUwNzIyYjY0OGM4ZTEzYzcyNDg3YjBiMzczIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IisxRTl3LzhmRE5udkFnOXFJVFRJalE9PSIsInZhbHVlIjoiYTg0Uko2bDZ5dERiQnVwRWNNQUNlOFcyRStqcVdsMG5ITUlENWR0ZEIvbXZIQlBNZklUQjc3d1BraUhXN0kvZGYrcCtIQWlzN20vblYrQkoxZTRadVFjdlVDMDExb1pseWpMWFpXczhGaE1nZkpvL1dPUDFXY29NV2pqZ2YzaUsxOS9yM3B1RFdiLytYUGRYYjRhQW5Ub3VYTzdOZXhJcE1jRzNOQTdCeHdJY245NWN5MUh6VWVYaFJVMi9SdTNhdEZXQVZ5a1U0SXllNEt1MUtsMXN1S3B4ZVFDd2NKUVBJWHZ1Y1N4UzhmWHl0cEg2N0tZNmE0SitVWE5Xemp3bFlERXhKaVFSTG50VU1ha2lGM1lzTk9iWHpmdGFSamJSSkpJZEFoRmV2T092QXlNM2QwVVo1V2ZmQzdZWjBNRTN1QW8zTml0c3VQWUx3U1lsY2s5bXZ5QmdqZjcreU1iclMwTDhycU1UcWxCalpMbGVPcnEycVN1K2R2YlhqSUZVM3RmcWx6V2VDVnFKQm1aNitnTHJFM0hxQktCWUN6L1JzTDBIb2JLejF1cy9LRjRqbFp1V0d4TTUvVGJ1dC9IbjRHOW1MRHh2Z3V3OXd1R3dBbGRYdUVoSGdXVWVZUU1TdDRyZS90TmJESzNxRVBpYWtPaThpODAxK3kxNHVUV1EiLCJtYWMiOiI3MWM1MzEyZjRmNjMzNzdhMGUzNDgzMjFjYWJmMDBkNjlkODgzMzZlNWI2MGEwMDYyYWI1MDZkNGQ1MWIwMjdmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1745687865\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-222031330 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ljyZnEkq9wtwNjNB5PUGnt2C2gBP8SuztakKIPL</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-222031330\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-362005011 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:58:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImNRSTBnVDFGUWFWUUR1djd5K0dvc1E9PSIsInZhbHVlIjoiSEtvL1QzMFJLcE9lR1BnaXhLKzJvZUkrbWxiWmo5cVQ4WVVjY1hxOTM0RFJmVFlXQm9vQkQ3Tld1eEczdWRDemJ2WGVHWHZFUTJhalFKck13TTBJYVNCTUM3bHFzajZHaFUwUWliUGRQZTB4Yjg4QU9hMzhCYTR1TCs0RnJDK3ZzcUlkMHVZMWFldDFqZStuV3ZDSktZbEZOTVFlMFB3MUdKbHR1U0VSSVdVeUloZEwvTkw1QXdjK3NzTklGTkRqaFBEOGhpMDd6TkFnRWl0OXkrZHhvQ2wyVFJCUks2Uk1obGNYbDRJQmVVdSszcUNCSE83ZkpTL2lSeTBuNnNGUnJhN1J2UFhoaldoa3p4SElZdmlEZEU0bXJaR2RUb3Z4NCtWYnRLWms2eWk1dEJSL214UTBOZEFoTU1Va1pPbHdZM0VmbEJFaWFWTEowcHpUOHpPN2tuaEUzOW8vdi9sZ2liY2VUUWxqWW5IWE9EZ1hCeVQyVlRWaEd3N3VFMnkxd3prbjhTNTUyWVNybWV3UmRqdmIyNTJxVFhwNHg4anhIQm1GVmw2RU5pR1dxczVNWkl1bTBPR2xtVHg5MHVyTlZQbmoxZHhKVFhKd2o0c2xtVmVyY28xZE1QUWdZWFc4M3VNZlByR2REdk4xT2tNclZUNXhCYWxXc2dhb2h2UmkiLCJtYWMiOiJkYjdkNmQ4NTI3NjEyYjc5MThhNWNkOTgwYmVmYTY0NDgwZmFkYzNhN2NkNGEzN2RiYzlkOWNhYWE0NDVkYTgzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:58:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlFFVE9TRXk0aTlEeE9BWmdnR1NmcEE9PSIsInZhbHVlIjoiUXVGV3pxbzlkWXRpK2psQ3BaMXYyUVZ3ejdGTW03ajNKc2ZPNjNmRHIwY0NWaVZuSllSbVA0RmZwSFhUS1FDWklBdklUZDZ1YmNhdHpob0dnelNKNjc1Q1Y0c1Bia1QzOWVCeU1tRmpBOXQ3MDFIWncwaGIxYzl2YUE4VVFKQUpHNlRQUjgzUEcxN1RtK01yUW81Z2NBWDh2SVFDR0E1dDE1Mk9pRHU0a0IvTzlkWmVzS0wycFRETUEvUHVpUXdaMWdrWVYxenJuVGNRUnFGd1VzNVVyUW1jSnNrdW8vWTZlWG1GNWxFcS9lT3lGMnh4cmxmNVE1UWxCOFNUVGM5WnVGbzIzV29kQ0RQbWJwdjVoZVMrYnQ3RnNDdTNncWI1MitvMzMrd3JaSFhaTlhnWENwd3lpY0E4MlhBQ2FjdjMrKzcyZkVKam0vaFMvcEJSR2wvdGNuRUJWeElSUEFLM2FhR2xIcm5OWDcvT3NiMTczQTBoSS9EZGVOckdXYnVmOEpuQkxJcG02Z3FzcnhWZFJVK1hTT0xJTHhaNzhLbElETXpDYXAvajRBYm5ta2dJbmlPdzhZMVE2b0V4NnRNZU83V3RjdlgwY0ltU1psSVJxYnU1ai9nWGZCUnFTaDFHU1ZUd3FoTVBJK0JvYURBN29pTHVmdE1hcEdwMkIxdXAiLCJtYWMiOiI3MmNjMDBhMDEyZGFlYjRiNDZjNWVjYjA2Y2YwZDc0ZTNlYzU2NTZmMjdmZGY3MTNjZTdlYjYwYjNlNTc0NzMzIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:58:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImNRSTBnVDFGUWFWUUR1djd5K0dvc1E9PSIsInZhbHVlIjoiSEtvL1QzMFJLcE9lR1BnaXhLKzJvZUkrbWxiWmo5cVQ4WVVjY1hxOTM0RFJmVFlXQm9vQkQ3Tld1eEczdWRDemJ2WGVHWHZFUTJhalFKck13TTBJYVNCTUM3bHFzajZHaFUwUWliUGRQZTB4Yjg4QU9hMzhCYTR1TCs0RnJDK3ZzcUlkMHVZMWFldDFqZStuV3ZDSktZbEZOTVFlMFB3MUdKbHR1U0VSSVdVeUloZEwvTkw1QXdjK3NzTklGTkRqaFBEOGhpMDd6TkFnRWl0OXkrZHhvQ2wyVFJCUks2Uk1obGNYbDRJQmVVdSszcUNCSE83ZkpTL2lSeTBuNnNGUnJhN1J2UFhoaldoa3p4SElZdmlEZEU0bXJaR2RUb3Z4NCtWYnRLWms2eWk1dEJSL214UTBOZEFoTU1Va1pPbHdZM0VmbEJFaWFWTEowcHpUOHpPN2tuaEUzOW8vdi9sZ2liY2VUUWxqWW5IWE9EZ1hCeVQyVlRWaEd3N3VFMnkxd3prbjhTNTUyWVNybWV3UmRqdmIyNTJxVFhwNHg4anhIQm1GVmw2RU5pR1dxczVNWkl1bTBPR2xtVHg5MHVyTlZQbmoxZHhKVFhKd2o0c2xtVmVyY28xZE1QUWdZWFc4M3VNZlByR2REdk4xT2tNclZUNXhCYWxXc2dhb2h2UmkiLCJtYWMiOiJkYjdkNmQ4NTI3NjEyYjc5MThhNWNkOTgwYmVmYTY0NDgwZmFkYzNhN2NkNGEzN2RiYzlkOWNhYWE0NDVkYTgzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:58:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlFFVE9TRXk0aTlEeE9BWmdnR1NmcEE9PSIsInZhbHVlIjoiUXVGV3pxbzlkWXRpK2psQ3BaMXYyUVZ3ejdGTW03ajNKc2ZPNjNmRHIwY0NWaVZuSllSbVA0RmZwSFhUS1FDWklBdklUZDZ1YmNhdHpob0dnelNKNjc1Q1Y0c1Bia1QzOWVCeU1tRmpBOXQ3MDFIWncwaGIxYzl2YUE4VVFKQUpHNlRQUjgzUEcxN1RtK01yUW81Z2NBWDh2SVFDR0E1dDE1Mk9pRHU0a0IvTzlkWmVzS0wycFRETUEvUHVpUXdaMWdrWVYxenJuVGNRUnFGd1VzNVVyUW1jSnNrdW8vWTZlWG1GNWxFcS9lT3lGMnh4cmxmNVE1UWxCOFNUVGM5WnVGbzIzV29kQ0RQbWJwdjVoZVMrYnQ3RnNDdTNncWI1MitvMzMrd3JaSFhaTlhnWENwd3lpY0E4MlhBQ2FjdjMrKzcyZkVKam0vaFMvcEJSR2wvdGNuRUJWeElSUEFLM2FhR2xIcm5OWDcvT3NiMTczQTBoSS9EZGVOckdXYnVmOEpuQkxJcG02Z3FzcnhWZFJVK1hTT0xJTHhaNzhLbElETXpDYXAvajRBYm5ta2dJbmlPdzhZMVE2b0V4NnRNZU83V3RjdlgwY0ltU1psSVJxYnU1ai9nWGZCUnFTaDFHU1ZUd3FoTVBJK0JvYURBN29pTHVmdE1hcEdwMkIxdXAiLCJtYWMiOiI3MmNjMDBhMDEyZGFlYjRiNDZjNWVjYjA2Y2YwZDc0ZTNlYzU2NTZmMjdmZGY3MTNjZTdlYjYwYjNlNTc0NzMzIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:58:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-362005011\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-642256701 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">R6jIEPwrvHwJxidUNKl5UUqEAVD7HeayQqr2ddpx</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-642256701\", {\"maxDepth\":0})</script>\n"}}