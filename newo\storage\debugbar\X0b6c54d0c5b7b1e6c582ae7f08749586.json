{"__meta": {"id": "X0b6c54d0c5b7b1e6c582ae7f08749586", "datetime": "2025-06-08 00:40:59", "utime": **********.447789, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749343258.811393, "end": **********.447817, "duration": 0.6364240646362305, "duration_str": "636ms", "measures": [{"label": "Booting", "start": 1749343258.811393, "relative_start": 0, "end": **********.365239, "relative_end": **********.365239, "duration": 0.5538458824157715, "duration_str": "554ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.365253, "relative_start": 0.5538599491119385, "end": **********.44782, "relative_end": 2.86102294921875e-06, "duration": 0.08256697654724121, "duration_str": "82.57ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45054704, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00415, "accumulated_duration_str": "4.15ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4088118, "duration": 0.00269, "duration_str": "2.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 64.819}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.4242911, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 64.819, "width_percent": 15.904}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4342842, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 80.723, "width_percent": 19.277}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-685700575 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-685700575\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-780074395 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-780074395\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2055672739 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2055672739\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1995 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=a091af98-270d-406e-ab7e-33a6e610d6e2414686; _clck=nups8l%7C2%7Cfwl%7C0%7C1960; _clsk=1dgl8io%7C1749343246363%7C41%7C1%7Ck.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InpIeTNVR0R4cjJJeThYMm8yNkphUGc9PSIsInZhbHVlIjoiSDNxSnYzWlc4UjU3YjVWTms2OER5QUc1TUtYTEwyMUp5N3dVWUY5dHRCa1lBRW11RHVRME4yd3pGdWFFV3ZFNkdTNmdxNUJsek1rL0l4YVVoUlAxNE5jb2k1MndDaE9LV2dkUzFVOTNuK2xjRUc1YWdVVndoRWxOTmZ6WE12LzVVaHZCKzVFYnAwWkl3SHQvckRMSE4zRm1ESFY1bCtNMFRxOS9pYXFsMTJISTVnWWhkNWNiSmVNZTdqWXhUNmRUbVFMb09NTHIrNm9oSFIwbDh6UUtpNWRIRTBFUXFJcCtPMEo0MDc3RWN0SXRucUpGYkkrRjVQSWRYeE5HdnBoTWxhUllBeUROK0pyZ25NSzVhTzdaWHlrM0lmczRiMkovMFRqWnE0UjNqWElPdHd2d1RkbFg5OHdWQzVITWQ3TS9maWVvTGdpaXJpTUlzR2R0YjJrY2VjdkNhb2JGRGxlK0JFUUdIcUUyZ0FwckxrMC9QVk95TFVpcURaTFdibjdMRFVCaG1ZVWhCZTlvci9LT0hYM2xhekcyU3FrSEFjMERNV0h4a0dyTS9PcDNRbFNGQk16em1tQ3RWUGEweUo5MFdoZTdhK2syY21lZGFPSHUvc05GUWZGY3Rmd243ZExBU2VDM1NOekJyMlBHSnRjUHdBbnFBOVFzYWNOb1lhV3kiLCJtYWMiOiI0NzdhZThjOTMxNTgyMDUwZjk4YjgwNGVmYTM4NWRiYmNlYTI1OWRjNWMyNDllNDdlN2RjNmVjZDA1MjBhYmYzIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkE3MDZPdnBleTdYOGxnbkNIS05DWUE9PSIsInZhbHVlIjoiM0dqZnVoNy90R2Z5TVVZWXc0UGRmT01UaFFpVVFWZ05rY1hPbThWWTFKd1M2cUdVeHhDd3hHelN6OHhCLzd3RjYwZmw5bmNyM2pyQ0lxb0tYa2xyZWlGMHNJTTdHaUNOMVQ0WlE1NTdPM04yeW1sSDc4QXVGUnhLWE1IU2JrWUtJcGZEaU4wUGtjUnh5ZXJVUkVwVWF4Z1hmQjhuc3ZjTGtKaElmekRDQUJYUk1LQ3J5Sy9USnY4Q2Y1QTBHM01SYTMwbUYreWR1L1RrNFlmUHV1YlFTbEZ3djhnNENmaWdDZTlRQnc2WmFvMm5oam1hU3p6M2lGc0xsRzY1SFpjQnRia2ZSN1pHMmt1TTNoWi9wVGlNRkE0T2JWRm0zeG5GODV2U3VqUjhWWThCSDRsc3N2OVhxd1AwYmNsYThvQ1VyQUptYXpLbzRQNHlrSFpGTzdqUlo2alRSeFhsdy80QUJveTBWS2VIMnhLSnhBejAycE55ajZmMjJialEyUnFoU2RMWVRvcm5iWHJvTVZkREpSclBQcUxSaEpUL2NmSGZxT2VDWEZrUzhLdVV5c3RvYVJka0NFOE1uamdjUUhPUHRhRWVzeGJDSG5HSFJ4VVEwUFNrS2kxaVorelRCZ3QxVUxESWdvL3J2N2VWekdxbEx3NHFVc0N2c3BkNEc2WmwiLCJtYWMiOiI3MDNmMzlhZWRmNjY1YmM5MjUyZmU4M2M3MmE2MmI0ODc0M2I1NjYyNmNhYzE4YjIzZWQ4ZmUzOTNiYjIyZDhiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1466820805 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Eo35AMmp3Bkf2zsyHLroae0N6MdUih7xJ0ojLwSF</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1466820805\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-243122682 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sun, 08 Jun 2025 00:40:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImNZc25kd3pKVTN3Tm1LMi90amRlV2c9PSIsInZhbHVlIjoicXBqb24rNHdaSEx0ZllPT09RVXliSUZqRGJCWStOTFZZWFBsbTBUK0FMdUs4VTFVODQ4clltK2Vqdmg1Vi82QnBTNDk3NlgyT1p4K3JOS3daSDhCZFErdmpLTmZ2Rkd2K2xnWTludmw5TFhoOUR2WERKMk54Z2ZBV0g0VjArYjdiRHJ2R0JpZ2xZYTVOK3JxdXBKajlZazZHVTNuZ3RYYy9KaHlnbUVQT3Rla0tJOWR1MWRFSGxPMWtTNDVyUFdpNThVaENubFYwNWhiVVVGaVFKdWcxeU45YmdwZkpPT1laMisxOE5NM2RnVUg4elc3VzkxWjBWQ2NwVDRuUFZ5QmhUWis3d1dHTWNMbVVsZjFpK3hENmltNjRzb2FoMGFRcUxMeUtGV3BQNCt1bUhPbGwwTERQbTRZeFN2dms3cUtVbEY0aVE1S1R4ajZ1N29zampQQVliajh3WmRMUXJPNjNma1p0YXFKRFpXTHJEYlFBQ3VsN3hWU2NvcTVja2RyQ014b0pQRFk4dFQ3bmtwOUlvODU2azBmbzFiODF4WVg5NVRIcGljSEowZ1F4UUVCUzRNRkRKS3cyZVRiODFLS0g5OFQ5WVRMREV1TEVoMmhUZzNXWUlVOG5zVHNZNTFqbnBpMXFEY2dBN2tVMEw4TUNhYSs0VUdwRFRBMjVmUDgiLCJtYWMiOiI1YzA1M2Q1YjE3YjE2NDE2ZWZhZDAxYWYwYTIyMjhiNGYwMzU1NjYxOTBhYjFiNjY3N2ZkMWRkYTNmNjMzOWQyIiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:40:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkZSYW1CUVJidU5INkh6dzlYdFlRbEE9PSIsInZhbHVlIjoicXQ3TnJOUFl6MktscVRJUVE3NHpCdkw1ODFOM1NzVGwrN0ludVgwb0ZkREl0ekdxTVZnYmRZdGdWRTJJcUMyZURzQU8wMi9VSnJDVDNKVlBOcDhJMFBBSHFBZGNBVHdWeXZBVWx4dERySjd3Syt2REhwVkJDUlM5OFRpdE5ZbCtYOHFMbHFMeWZFdEFCUWFNZU9qbStETEVGUW1yNlRnNzRsZTg4RTl4RFhySTFYZVA3aEpiUTFqcElpeXFNTEtSbnVmYisvOURtd3NZMm5SK0lIUU5PUlRZK3l3d0pjUVh3bGttRnlxZjdTdmZWbXZXaXZ4OVZrWGpUM0RjSERzdFZwbzBBWkR2VWRPcUJXUXFLQVdiZTdYVHc3ZTJNM1VoYmlCcWNXOGQ0YlQ0RkM5cUJDSCtXVWZtS3N1cng4QjYrdjdCS1RIdzI2K0FvcXVzR0YyZThUY3lsem9wWmVseVdJWk1oRnJMdDRtMFk1MlJhWFR2d0ZYZnZLVWlBdk9ZY21uKytucHlHRnV6Znd5dUw4ajU0WlU0QitMelZ2aWUrTFZNcGlzNmdEaWdwU1FrRzV1N1RVMVVjdTNPVjRhU0tKL3MyeEF2eG9vWVk1Y3g3SWU0TkIyc1hLeld4U0dxZTZ5MTZSUUZjYkJCWTJWdEpJVXc0bFJaQkk5UmNNUWkiLCJtYWMiOiI5MWM3YWZmY2FiY2JlM2Q2MzBjNjljYjhiODJkOWUzZmJiY2RjZTIwN2Y1NWZiZmVhMjgxZDVjMDkyNjVhYjc5IiwidGFnIjoiIn0%3D; expires=Sun, 08 Jun 2025 02:40:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImNZc25kd3pKVTN3Tm1LMi90amRlV2c9PSIsInZhbHVlIjoicXBqb24rNHdaSEx0ZllPT09RVXliSUZqRGJCWStOTFZZWFBsbTBUK0FMdUs4VTFVODQ4clltK2Vqdmg1Vi82QnBTNDk3NlgyT1p4K3JOS3daSDhCZFErdmpLTmZ2Rkd2K2xnWTludmw5TFhoOUR2WERKMk54Z2ZBV0g0VjArYjdiRHJ2R0JpZ2xZYTVOK3JxdXBKajlZazZHVTNuZ3RYYy9KaHlnbUVQT3Rla0tJOWR1MWRFSGxPMWtTNDVyUFdpNThVaENubFYwNWhiVVVGaVFKdWcxeU45YmdwZkpPT1laMisxOE5NM2RnVUg4elc3VzkxWjBWQ2NwVDRuUFZ5QmhUWis3d1dHTWNMbVVsZjFpK3hENmltNjRzb2FoMGFRcUxMeUtGV3BQNCt1bUhPbGwwTERQbTRZeFN2dms3cUtVbEY0aVE1S1R4ajZ1N29zampQQVliajh3WmRMUXJPNjNma1p0YXFKRFpXTHJEYlFBQ3VsN3hWU2NvcTVja2RyQ014b0pQRFk4dFQ3bmtwOUlvODU2azBmbzFiODF4WVg5NVRIcGljSEowZ1F4UUVCUzRNRkRKS3cyZVRiODFLS0g5OFQ5WVRMREV1TEVoMmhUZzNXWUlVOG5zVHNZNTFqbnBpMXFEY2dBN2tVMEw4TUNhYSs0VUdwRFRBMjVmUDgiLCJtYWMiOiI1YzA1M2Q1YjE3YjE2NDE2ZWZhZDAxYWYwYTIyMjhiNGYwMzU1NjYxOTBhYjFiNjY3N2ZkMWRkYTNmNjMzOWQyIiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:40:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkZSYW1CUVJidU5INkh6dzlYdFlRbEE9PSIsInZhbHVlIjoicXQ3TnJOUFl6MktscVRJUVE3NHpCdkw1ODFOM1NzVGwrN0ludVgwb0ZkREl0ekdxTVZnYmRZdGdWRTJJcUMyZURzQU8wMi9VSnJDVDNKVlBOcDhJMFBBSHFBZGNBVHdWeXZBVWx4dERySjd3Syt2REhwVkJDUlM5OFRpdE5ZbCtYOHFMbHFMeWZFdEFCUWFNZU9qbStETEVGUW1yNlRnNzRsZTg4RTl4RFhySTFYZVA3aEpiUTFqcElpeXFNTEtSbnVmYisvOURtd3NZMm5SK0lIUU5PUlRZK3l3d0pjUVh3bGttRnlxZjdTdmZWbXZXaXZ4OVZrWGpUM0RjSERzdFZwbzBBWkR2VWRPcUJXUXFLQVdiZTdYVHc3ZTJNM1VoYmlCcWNXOGQ0YlQ0RkM5cUJDSCtXVWZtS3N1cng4QjYrdjdCS1RIdzI2K0FvcXVzR0YyZThUY3lsem9wWmVseVdJWk1oRnJMdDRtMFk1MlJhWFR2d0ZYZnZLVWlBdk9ZY21uKytucHlHRnV6Znd5dUw4ajU0WlU0QitMelZ2aWUrTFZNcGlzNmdEaWdwU1FrRzV1N1RVMVVjdTNPVjRhU0tKL3MyeEF2eG9vWVk1Y3g3SWU0TkIyc1hLeld4U0dxZTZ5MTZSUUZjYkJCWTJWdEpJVXc0bFJaQkk5UmNNUWkiLCJtYWMiOiI5MWM3YWZmY2FiY2JlM2Q2MzBjNjljYjhiODJkOWUzZmJiY2RjZTIwN2Y1NWZiZmVhMjgxZDVjMDkyNjVhYjc5IiwidGFnIjoiIn0%3D; expires=Sun, 08-Jun-2025 02:40:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-243122682\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1978365522 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cN6pwMHluqMWM9hLaRtOtRasnQF8pfN3ghxjsOfa</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1978365522\", {\"maxDepth\":0})</script>\n"}}